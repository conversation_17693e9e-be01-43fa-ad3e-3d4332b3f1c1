@extends('layouts.app')
@section('content')

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-9">
                <h1>Batch Overview</h1>
            </div>
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">

                <div class="card">
                    <!-- /.card-header -->
                    <div class="card-body">
                        <h3 class="box-title popup-title m-0">Filter Batch</h3>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><b>Department</b></label>
                                    <select class="form-control select2 department-filter" id="department_classroom" name="department" aria-invalid="false">
                                        <option value="">
                                            All
                                        </option>
                                        @foreach($department as $dept)
                                        <option value="{{$dept->id}}">
                                            {{$dept->name}}
                                        </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><b>Classroom</b></label>
                                    <select class="form-control select2 user-filter classroom-data" id="classroom_select" name="classroom" aria-invalid="false">
                                        <option value="">
                                            All
                                        </option>
                                        @foreach($classroomforfilter as $class)
                                        <option value="{{$class->id}}">
                                            {{$class->class_name}}
                                        </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <button id="filtertimetable" class="btn btn-primary filter-btn">Filter</button>
                                    <button id="filterreset"  class="btn btn-secondary filter-btn">Reset</button>

                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <div class="col-12">

                <div class="card">
                    <!-- /.card-header -->
                    <div class="card-body">
                        <div id="CalendarContent" class="fc fc-media-screen fc-direction-ltr fc-theme-standard">
                            <div class="fc-header-toolbar fc-toolbar fc-toolbar-ltr">
                                <div class="fc-toolbar-chunk">
                                    <div class="fc-button-group">
                                        <button type="button" title="Previous Week" aria-pressed="false" class="fc-prev-button fc-button fc-button-primary previosDay">
                                            <span class="fc-icon fc-icon-chevron-left"></span>
                                        </button>
                                        <button type="button" title="Next Week" aria-pressed="false" class="fc-next-button fc-button fc-button-primary nextDay">
                                            <span class="fc-icon fc-icon-chevron-right">
                                            </span>
                                        </button>
                                    </div>
                                    <button type="button" title="Today" aria-pressed="false" class="fc-today-button fc-button fc-button-primary today">Today</button>
                                </div>
                                <div class="fc-toolbar-chunk">
                                    <h2 class="fc-toolbar-title" id="fc-dom-1">{{ date('d-m-Y') }}</h2>
                                </div>
                                <div class="fc-toolbar-chunk">
                                <button title="Export Timetable" class="exporttimetable fc-today-button fc-button fc-button-primary">Export</button>
                                    <input id="datepicker-timetable" value="{{ date('d-m-Y') }}" type="hidden" />
                                </div>
                            </div>
                        </div>
                        <div id="ajaxtimetable"></div>
                        <div class="modal" id="newCTEntry" role="dialog" aria-labelledby="roleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h3 id="modeltitle" class="box-title popup-title m-0">Add New Entry</h3>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div id="createContent"></div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
@endsection
@section('scripts')
@if(session()->has('success'))
<script>
    $(document).ready(function() {
        toastr.success('{{ session()->get("success") }}')
    });
</script>
@endif
<script>
     var batchesRoute = {
        getclassroomlist: "{{route('classroom.ajax.list')}}",
        export: "{{route('batches.viewExport')}}",
    };

    function getData(date) {
        var filters =
            "classroom=" +
            $("#classroom_select").val() +
            "&department=" +
            $("#department_classroom").val();
        var params = $.extend({}, doAjax_params_default);
        params["url"] = "?date=" + date + "&" + filters;
        params["requestType"] = `GET`;
        params["successCallbackFunction"] = function successCallbackFunction(
            result
        ) {
            $("#ajaxtimetable").empty().html(result);
            $('.proxyDiv').parent().find('.addproxy').css('display', 'none');
        };
        commonAjax(params);
    }
</script>
<script src="{{ asset(mix('js/page-level-js/batches/view/index.js')) }}"></script>
@endsection