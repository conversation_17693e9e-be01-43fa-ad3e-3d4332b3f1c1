import prisma from '../config/prismaClient';
import { getLeaderboard } from './mockExamLeaderBoardService';
import axios from 'axios';

const REWARD_ITEM_ID = '9d252987-bc35-4cfc-b017-8e71df47dac8';
const UEST_APP_BASE_URL = process.env.UEST_APP_BASE_URL || 'http://localhost:4005';

interface LeaderboardWinner {
  rank: number;
  studentId: string;
  score: number;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
}

export const distributeDailyRewards = async (): Promise<{ success: boolean; message: string; details?: any }> => {
  try {

    // Get today's leaderboard top 3
    console.log('📊 Fetching today\'s leaderboard...');
    const leaderboardResult = await getLeaderboard('today', 1, 3);
    console.log('📊 Leaderboard result:', {
      hasData: !!leaderboardResult.data,
      dataLength: leaderboardResult.data?.length || 0,
      total: leaderboardResult.total
    });

    if (!leaderboardResult.data || leaderboardResult.data.length === 0) {
      return {
        success: false,
        message: 'No leaderboard data found for today'
      };
    }

    const topThreeWinners = leaderboardResult.data.slice(0, 3) as LeaderboardWinner[];
    
    if (topThreeWinners.length === 0) {
      return {
        success: false,
        message: 'No winners found in today\'s leaderboard'
      };
    }

    // Check if the reward item exists via API call to uest-app
    let rewardItem;
    try {
      const response = await axios.get(`${UEST_APP_BASE_URL}/admin/store/${REWARD_ITEM_ID}`);
      rewardItem = response.data.data;
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to fetch reward item: ${error.response?.data?.message || error.message}`
      };
    }

    if (!rewardItem) {
      return {
        success: false,
        message: `Reward item with ID ${REWARD_ITEM_ID} not found`
      };
    }

    if (rewardItem.status !== 'ACTIVE') {
      return {
        success: false,
        message: `Reward item is not active`
      };
    }

    if (rewardItem.availableStock < topThreeWinners.length) {
      return {
        success: false,
        message: `Insufficient stock for reward item. Available: ${rewardItem.availableStock}, Required: ${topThreeWinners.length}`
      };
    }

    const rewardResults = [];
    const errors = [];

    // Process each winner
    for (const winner of topThreeWinners) {
      try {
        const result = await processFreeOrder(winner, rewardItem);
        rewardResults.push({
          rank: winner.rank,
          studentId: winner.studentId,
          studentName: `${winner.firstName} ${winner.lastName}`,
          result
        });
      } catch (error: any) {
        console.error(`Error processing reward for student ${winner.studentId}:`, error);
        errors.push({
          rank: winner.rank,
          studentId: winner.studentId,
          error: error.message
        });
      }
    }

    console.log('🎉 Daily reward distribution completed:', {
      totalWinners: topThreeWinners.length,
      successfulRewards: rewardResults.length,
      errors: errors.length
    });

    return {
      success: true,
      message: `Successfully distributed rewards to ${rewardResults.length} winners`,
      details: {
        winners: rewardResults,
        errors: errors.length > 0 ? errors : undefined
      }
    };

  } catch (error: any) {
    console.error('❌ Error in daily reward distribution:', error);
    return {
      success: false,
      message: `Failed to distribute daily rewards: ${error.message}`
    };
  }
};

const processFreeOrder = async (winner: LeaderboardWinner, rewardItem: any) => {
  try {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

    try {
      const checkResponse = await axios.get(`${UEST_APP_BASE_URL}/store/orders`, {
        headers: {
          'Authorization': `Bearer ${generateTempToken(winner.studentId)}`, 
          'Content-Type': 'application/json'
        }
      });

      const todayOrders = checkResponse.data.data?.filter((order: any) =>
        order.itemId === REWARD_ITEM_ID &&
        order.createdAt.startsWith(today) &&
        order.itemPrice === 0 // Free orders
      );

      if (todayOrders && todayOrders.length > 0) {
        throw new Error(`Student ${winner.studentId} already received daily reward today`);
      }
    } catch (error: any) {
      if (error.message.includes('already received')) {
        throw error;
      }
      console.log(`Warning: Could not check existing orders for student ${winner.studentId}`);
    }

    const orderData = {
      cartItems: [{
        id: rewardItem.id,
        name: rewardItem.name,
        coinPrice: 0,
        quantity: 1
      }],
      totalCoins: 0,
      isReward: true, 
      rewardReason: `Daily Leaderboard Reward - Rank ${winner.rank}`
    };

    const response = await axios.post(`${UEST_APP_BASE_URL}/store/purchase-reward`, orderData, {
      headers: {
        'Authorization': `Bearer ${generateTempToken(winner.studentId)}`,
        'Content-Type': 'application/json',
        'X-Student-ID': winner.studentId,
        'X-User-Type': 'STUDENT'
      }
    });

    console.log(`✅ Free order created for rank ${winner.rank} student: ${winner.studentId}`);

    return {
      success: true,
      orderId: response.data.data?.orderId,
      message: `Free reward order created successfully`
    };

  } catch (error: any) {
    console.error(`Error creating free order for student ${winner.studentId}:`, error.response?.data || error.message);
    throw new Error(error.response?.data?.message || error.message || 'Failed to create free order');
  }
};

const generateTempToken = (studentId: string): string => {
  const jwt = require('jsonwebtoken');
  const secret = process.env.JWT_SECRET || 'your-jwt-secret';

  return jwt.sign(
    {
      id: studentId,
      userType: 'STUDENT',
      isRewardSystem: true
    },
    secret,
    { expiresIn: '1h' }
  );
};

// Function to check if rewards have already been distributed today
export const checkTodayRewardsDistributed = async (): Promise<boolean> => {
  try {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

    // Call uest-app API to check if any reward orders were placed today
    try {
      const response = await axios.get(`${UEST_APP_BASE_URL}/admin/store/orders`, {
        params: {
          startDate: today,
          endDate: today,
          itemId: REWARD_ITEM_ID
        },
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const todayRewards = response.data.data?.filter((order: any) =>
        order.itemPrice === 0 && // Free rewards
        order.createdAt.startsWith(today)
      );

      return todayRewards && todayRewards.length > 0;
    } catch (error: any) {
      console.error('Error checking today\'s rewards via API:', error.response?.data || error.message);
      return false;
    }
  } catch (error) {
    console.error('Error checking today\'s rewards:', error);
    return false;
  }
};
