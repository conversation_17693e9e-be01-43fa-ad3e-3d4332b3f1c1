import prisma from '../config/prismaClient';
import { getLeaderboard } from './mockExamLeaderBoardService';
import axios from 'axios';

const REWARD_ITEM_ID = '9d252987-bc35-4cfc-b017-8e71df47dac8';

interface LeaderboardWinner {
  rank: number;
  studentId: string;
  score: number;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
}

export const distributeDailyRewards = async (): Promise<{ success: boolean; message: string; details?: any }> => {
  try {
    console.log('🏆 Starting daily leaderboard reward distribution...');
    
    // Get today's leaderboard top 3
    const leaderboardResult = await getLeaderboard('today', 1, 3);
    
    if (!leaderboardResult.data || leaderboardResult.data.length === 0) {
      return {
        success: false,
        message: 'No leaderboard data found for today'
      };
    }

    const topThreeWinners = leaderboardResult.data.slice(0, 3) as LeaderboardWinner[];
    
    if (topThreeWinners.length === 0) {
      return {
        success: false,
        message: 'No winners found in today\'s leaderboard'
      };
    }

    // Check if the reward item exists
    const rewardItem = await prisma.storeItem.findUnique({
      where: { id: REWARD_ITEM_ID },
      select: {
        id: true,
        name: true,
        coinPrice: true,
        availableStock: true,
        status: true
      }
    });

    if (!rewardItem) {
      return {
        success: false,
        message: `Reward item with ID ${REWARD_ITEM_ID} not found`
      };
    }

    if (rewardItem.status !== 'ACTIVE') {
      return {
        success: false,
        message: `Reward item is not active`
      };
    }

    if (rewardItem.availableStock < topThreeWinners.length) {
      return {
        success: false,
        message: `Insufficient stock for reward item. Available: ${rewardItem.availableStock}, Required: ${topThreeWinners.length}`
      };
    }

    const rewardResults = [];
    const errors = [];

    // Process each winner
    for (const winner of topThreeWinners) {
      try {
        const result = await processFreeOrder(winner, rewardItem);
        rewardResults.push({
          rank: winner.rank,
          studentId: winner.studentId,
          studentName: `${winner.firstName} ${winner.lastName}`,
          result
        });
      } catch (error: any) {
        console.error(`Error processing reward for student ${winner.studentId}:`, error);
        errors.push({
          rank: winner.rank,
          studentId: winner.studentId,
          error: error.message
        });
      }
    }

    console.log('🎉 Daily reward distribution completed:', {
      totalWinners: topThreeWinners.length,
      successfulRewards: rewardResults.length,
      errors: errors.length
    });

    return {
      success: true,
      message: `Successfully distributed rewards to ${rewardResults.length} winners`,
      details: {
        winners: rewardResults,
        errors: errors.length > 0 ? errors : undefined
      }
    };

  } catch (error: any) {
    console.error('❌ Error in daily reward distribution:', error);
    return {
      success: false,
      message: `Failed to distribute daily rewards: ${error.message}`
    };
  }
};

const processFreeOrder = async (winner: LeaderboardWinner, rewardItem: any) => {
  return await prisma.$transaction(async (tx) => {
    // Check if student already received reward today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const existingOrder = await tx.storeOrder.findFirst({
      where: {
        modelId: winner.studentId,
        modelType: 'STUDENT',
        itemId: REWARD_ITEM_ID,
        createdAt: {
          gte: today,
          lt: tomorrow
        },
        // Check for orders with special reason indicating it's a daily reward
        OR: [
          { status: 'COMPLETED' },
          { status: 'PENDING' }
        ]
      }
    });

    if (existingOrder) {
      throw new Error(`Student ${winner.studentId} already received daily reward today`);
    }

    // Create the free order (no coin deduction)
    const order = await tx.storeOrder.create({
      data: {
        modelId: winner.studentId,
        modelType: 'STUDENT',
        itemId: rewardItem.id,
        itemName: rewardItem.name,
        itemPrice: 0, // Free reward
        quantity: 1,
        totalCoins: 0, // No coins charged
        status: 'COMPLETED' // Automatically complete the order
      }
    });

    // Update stock
    await tx.storeItem.update({
      where: { id: rewardItem.id },
      data: {
        availableStock: {
          decrement: 1
        }
      }
    });

    // Create a transaction record for tracking (but with 0 amount)
    await tx.uestCoinTransaction.create({
      data: {
        modelId: winner.studentId,
        modelType: 'STUDENT',
        amount: 0,
        type: 'DEBIT',
        reason: `Daily Leaderboard Reward - Rank ${winner.rank} (Order #${order.id.slice(-8)})`
      }
    });

    console.log(`✅ Free order created for rank ${winner.rank} student: ${winner.studentId}`);

    return {
      success: true,
      orderId: order.id,
      message: `Free reward order created successfully`
    };
  });
};

// Function to check if rewards have already been distributed today
export const checkTodayRewardsDistributed = async (): Promise<boolean> => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayRewards = await prisma.storeOrder.count({
      where: {
        itemId: REWARD_ITEM_ID,
        itemPrice: 0, // Free rewards
        createdAt: {
          gte: today,
          lt: tomorrow
        }
      }
    });

    return todayRewards > 0;
  } catch (error) {
    console.error('Error checking today\'s rewards:', error);
    return false;
  }
};
