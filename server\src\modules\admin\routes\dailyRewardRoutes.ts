import { Router } from 'express';
import * as dailyRewardController from '../controllers/dailyRewardController';
import { authMiddleware } from '@/middlewares/adminAuth';

const router = Router();

// Manual reward distribution (admin only)
router.post('/distribute', authMiddleware, dailyRewardController.manualDistributeRewards);

// Check reward status
router.get('/status', authMiddleware, dailyRewardController.checkRewardStatus);

// Get cron job status
router.get('/cron-status', authMiddleware, dailyRewardController.getCronJobStatus);

export default router;
