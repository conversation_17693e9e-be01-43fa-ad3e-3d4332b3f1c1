<?php

namespace Admission\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateLeavingCertificateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'student_name' => 'required|string|max:200',
            "mothers_name"    => "required|string|max:200",
            "place_of_birth"    => "required|string|max:200",
            "nationality"    => "required|string|max:200",
            "category"    => "required|string|max:200",
            "date_of_birth"    => "required|date",
            "last_school_attained"    => "required|string|max:200",
            "date_of_admission"    => "required|date",
            "date_of_leaving_school"    => "required|date",
            "standard"    => "required|string|max:200",
            "reason_for_leaving"    => "required|string|max:200",
            "attendance"    => "required|numeric",
            "progress"    => "required|string|max:200",
            "conduct"    => "required|string|max:200",
            "remarks"    => "required|string|max:200",
            "admission_standard"    => "required|string|max:200",
        ];
    }
}
