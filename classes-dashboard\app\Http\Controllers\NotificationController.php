<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class NotificationController extends Controller
{
    public function index(Request $request)
    {
        $unreadNotification = auth()->user()->unreadNotifications;

        $perPage = 10;

        $page = request()->input('page', 1);
        $currentPageNotifications = $unreadNotification->slice(($page - 1) * $perPage, $perPage);
        $notifications = new LengthAwarePaginator(
            $currentPageNotifications,
            $unreadNotification->count(),
            $perPage,
            $page,
            [
                'path' => request()->url(),
                'query' => request()->query(),
            ]
        );

        if ($request->ajax()) {
            return view('notification.notification', compact('notifications'));
        }

        return view('notification.index', compact('notifications'));
    }

    public function markAsNotification(Request $request)
    {
        auth()->user()->unreadNotifications->when($request->input('id'), function ($query) use ($request) {
            return $query->where('id', $request->input('id'));
        })->markAsRead();
        return response()->noContent();
    }
}
