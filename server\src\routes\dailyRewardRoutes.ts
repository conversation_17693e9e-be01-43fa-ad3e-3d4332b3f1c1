import { Router } from 'express';
import * as dailyRewardController from '../controller/dailyRewardController';

const router = Router();

// Manual reward distribution (admin only)
router.post('/distribute', dailyRewardController.manualDistributeRewards);

// Check reward status
router.get('/status', dailyRewardController.checkRewardStatus);

// Get cron job status
router.get('/cron-status', dailyRewardController.getCronJobStatus);

export default router;
