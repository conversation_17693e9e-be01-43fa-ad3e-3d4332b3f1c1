<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class CommonExport implements FromView
{
    protected $object;
    protected $view;

    public function __construct($object, $view)
    {
        $this->object = $object;
        $this->view = $view;
    }

    /**
     * @return \Illuminate\Contracts\View\View
     */
    public function view(): View
    {
        return view($this->view, [
            'datas' => $this->object
        ]);
    }
}
