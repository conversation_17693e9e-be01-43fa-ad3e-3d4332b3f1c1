'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormLabel,
} from '@/components/ui/form';
import { Loader2, Trash2, Plus } from 'lucide-react';

import { UseFormReturn } from 'react-hook-form';
import { EducationFormValues } from '@/lib/validations/classesEditSchema';
import { deleteEducationByAdmin } from '@/services/classesApi';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import AddEducationForm from './AddEducationForm';

interface EducationTabProps {
  educationForm: UseFormReturn<EducationFormValues>;
  formData: any;
  isSaving: boolean;
  onSubmit: () => Promise<void>;
  onEducationUpdate?: () => Promise<void>;
}

const EducationTab: React.FC<EducationTabProps> = ({
  educationForm,
  formData,
  onSubmit,
  onEducationUpdate,
}) => {
  const [deletingEducation, setDeletingEducation] = React.useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [educationToDelete, setEducationToDelete] = React.useState<any>(null);
  const [isAddEducationOpen, setIsAddEducationOpen] = React.useState(false);

  const handleDeleteEducation = async (education: any) => {
    if (!education.id) {
      toast.error('Education ID is required for deletion');
      return;
    }

    try {
      setDeletingEducation(education.id);
      await deleteEducationByAdmin(education.id, formData.id);
      toast.success('Education record deleted successfully!');

      // Refresh the data
      if (onEducationUpdate) {
        await onEducationUpdate();
      } else {
        window.location.reload();
      }
    } catch (error: any) {
      console.error('Delete education error:', error);
      toast.error(error.message || 'Failed to delete education record');
    } finally {
      setDeletingEducation(null);
      setIsDeleteDialogOpen(false);
      setEducationToDelete(null);
    }
  };

  return (
    <Form {...educationForm}>
      <form onSubmit={educationForm.handleSubmit(onSubmit)} className="space-y-6">
        <div className="flex justify-between items-center border-b border-gray-200 pb-2">
          <h2 className="text-lg font-semibold text-black">Education</h2>
          <Button
            type="button"
            variant="outline"
            onClick={() => setIsAddEducationOpen(true)}
            className="bg-black hover:text-white hover:bg-gray-800 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Education
          </Button>
        </div>

        {formData.education.length > 0 ? (
          formData.education.map((edu: any, index: number) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-md font-medium text-gray-700">Education {index + 1}</h3>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setEducationToDelete(edu);
                      setIsDeleteDialogOpen(true);
                    }}
                    disabled={deletingEducation === edu.id}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    {deletingEducation === edu.id ? (
                      <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                    ) : (
                      <Trash2 className="w-4 h-4 mr-1" />
                    )}
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                <div>
                  <FormLabel className="text-black font-medium">Is Education</FormLabel>
                  <div className="p-2 mt-2 bg-gray-50 border border-gray-200 rounded-md">
                    <span className="text-gray-700">{edu.isDegree ? 'Yes' : 'No'}</span>
                  </div>
                </div>

                {edu.isDegree && (
                  <>
                    <div>
                      <FormLabel className="text-black font-medium">Degree</FormLabel>
                      <div className="p-2 mt-2 bg-gray-50 border border-gray-200 rounded-md">
                        <span className="text-gray-700">{edu.degree || 'Not specified'}</span>
                      </div>
                    </div>

                    <div>
                      <FormLabel className="text-black font-medium">University</FormLabel>
                      <div className="p-2 mt-2 bg-gray-50 border border-gray-200 rounded-md">
                        <span className="text-gray-700">{edu.university || 'Not specified'}</span>
                      </div>
                    </div>

                    <div>
                      <FormLabel className="text-black font-medium">Passout Year</FormLabel>
                      <div className="p-2 mt-2 bg-gray-50 border border-gray-200 rounded-md">
                        <span className="text-gray-700">{edu.passoutYear || 'Not specified'}</span>
                      </div>
                    </div>

                    <div>
                      <FormLabel className="text-black font-medium">Degree Type</FormLabel>
                      <div className="p-2 mt-2 bg-gray-50 border border-gray-200 rounded-md">
                        <span className="text-gray-700">{edu.degreeType || 'Not specified'}</span>
                      </div>
                    </div>

                    {/* Certificate Display */}
                    <div className="md:col-span-2">
                      <FormLabel className="text-black font-medium">Certificate</FormLabel>
                      <div className="mt-2">
                        {/* Current Certificate Display */}
                        {edu.certificate ? (
                          <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-600">Current certificate: {edu.certificate}</span>
                              <a
                                href={`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}uploads/classes/${formData.id}/education/${edu.certificate}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 underline text-sm font-medium"
                              >
                                View Certificate
                              </a>
                            </div>
                          </div>
                        ) : (
                          <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
                            <span className="text-sm text-gray-500">No certificate uploaded</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          ))
        ) : (
          <p className="text-gray-500">No education records found.</p>
        )}
      
        {/* Delete Confirmation Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Delete Education Record</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this education record? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setIsDeleteDialogOpen(false);
                  setEducationToDelete(null);
                }}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={() => educationToDelete && handleDeleteEducation(educationToDelete)}
                disabled={deletingEducation === educationToDelete?.id}
              >
                {deletingEducation === educationToDelete?.id ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Add Education Form */}
        <AddEducationForm
          classId={formData.id}
          isOpen={isAddEducationOpen}
          onClose={() => setIsAddEducationOpen(false)}
          onSuccess={async () => {
            if (onEducationUpdate) {
              await onEducationUpdate();
            } else {
              window.location.reload();
            }
          }}
        />
      </form>
    </Form>
  );
};

export default EducationTab;
