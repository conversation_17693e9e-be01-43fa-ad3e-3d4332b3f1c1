$(document).ready(function () {
    $(".check-field").hide();
    $(".reference-field").hide();
    $("#payment_mode").change(function () {
        var selectedOption = $(this).val();
        if (selectedOption == "CHEQUE") {
            $(".check-field").show();
            $(".reference-field").hide();
        } else if (selectedOption == "ONLINE") {
            $(".reference-field").show();
            $(".check-field").hide();
        } else {
            $(".check-field").hide();
            $(".reference-field").hide();
        }
    });
});

$("#payment-forms").validate({
    rules: {
        payment_mode: {
            required: true,
        },
        paid_amount: {
            required: true,
            min: 1,
        },
        check_no: {
            required: function () {
                return $("#payment_mode").val() == "CHECK";
            },
        },
        payment_taken_by: {
            required: true,
        },
        payment_category: {
            required: true,
        },
        "category_type[]": {
            required: true,
            customMax: true,
            min: 0,
        },
        installment_name: {
            required: true,
        },
    },
    errorElement: "span",
    errorPlacement: function (error, element) {
        error.addClass("invalid-feedback");
        element.closest(".form-group").append(error);
    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass("is-invalid");
    },
    submitHandler: function (form) {
        ajaxHandler(
            form,
            feesRoute.payStudentFee,
            "post",
            "#payment-forms",
            "#savefee",
            "#makePayment",
            "#payment_details"
        );
        setTimeout(function () {
            location.reload();
        }, 1000);
        return false;
    },
});

let columns = [
    {
        data: "action",
        name: "action",
        orderable: false,
    },
    {
        data: "student_full_name",
        name: "student_full_name",
    },
    {
        data: "student_class_info",
        name: "student_class_info",
    },
    {
        data: "payment_date",
        name: "payment_date",
    },
    {
        data: "month_name",
        name: "month_name",
    },
    {
        data: "payment_mode",
        name: "payment_mode",
    },
    {
        data: "paid_amount",
        name: "paid_amount",
    },
    {
        data: "cheque_no",
        name: "cheque_no",
    },
    {
        data: "reference_no",
        name: "reference_no",
    },
    {
        data: "payment_status",
        name: "payment_status",
    },
    {
        data: "transaction_id",
        name: "transaction_id",
    },
];

function callBack(res) {
    $('.totalcredit').text(res.total_credit_amount);
}

let data = function (d) {
    d.start_date = $("#start_date").val();
    d.end_date = $("#end_date").val();
    d.department = $("#department").val();
    d.classroom = $("#classroom-filter").val();
    d.student_id = $("#student-data").val();
};

let table = commonDatatable("#payment_details", feesRoute.index, columns, data, callBack);

function tablescroll() {
    $("html, body").animate(
        {
            scrollTop: $("#payment_details").offset().top,
        },
        1000
    );
}

$("#filter").on("click", function (event) {
    event.preventDefault();
    tablescroll();
    table.draw();
});

$("#filterreset").click(function () {
    event.preventDefault();
    $("#start_date").val("");
    $("#end_date").val("");
    $("#department").val("").trigger("change");
    $("#classroom-filter").val("").trigger("change");
    $("#student-data").val("").trigger("change");
    tablescroll();
    table.draw();
});

$(document).on("click", ".deletePayment", function () {
    let payid = $(this).attr("data-paymentID");
    let url = feesRoute.delete;
    url = url.replace(":payid", payid);

    let params = $.extend({}, doAjax_params_default);
    params["url"] = url;
    params["requestType"] = `DELETE`;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        toastr.success(result.success);
        table.draw();
        setTimeout(function () {
            location.reload();
        }, 1000);
    };
    let calert = function calert() {
        commonAjax(params);
    };
    commonAlert(calert);
});

$.validator.addMethod(
    "customMax",
    function (value, element) {
        const max = parseFloat($(element).attr("max"));
        return this.optional(element) || value <= max;
    },
    function (params, element) {
        const max = $(element).attr("max");
        return `Please enter a value less than or equal to ${max}.`;
    }
);

$(".payBtn").click(function (event) {
    event.preventDefault();

    const studentid = $(this).data("studentid");
    const month = $(this).data("month");
    const feesData = $(this).data("fees");
    let sum = 0;
    $("#fee_student_id").val(studentid);
    $("#month").val(month).trigger("change");

    $(".categoryInput").val(""); // clear previous
    var jsonData = {};

    if (Array.isArray(feesData)) {
        feesData.forEach(function (item) {
            const category = item.fees_category;
            const amount = item.amount ?? 0;
            jsonData[category] = amount;

            const inputId = category.replace(/\s+/g, '') + "_input";
            sum += parseFloat(amount) || 0;
            $(`#${inputId}`).val(amount).attr("max", amount);;
        });
    }

     $("#paid_amount").val(sum);
    $("#fee_paid_category").val(JSON.stringify(jsonData));
});

$(document).ready(function () {
    $("#discountForm").submit(function (event) {
        event.preventDefault();

        var isValid = true;

        $("input[name='discount_amount[]']").each(function () {
            var value = $(this).val();
            $(this).closest(".form-group").find(".invalid-feedback").remove();
            if (value.trim() === "") {
                isValid = false;
                $(this).addClass("is-invalid");
                $(this)
                    .closest(".form-group")
                    .append(
                        '<span class="invalid-feedback">Discount amount is required</span>'
                    );
            } else {
                $(this).removeClass("is-invalid");
                $(this)
                    .closest(".form-group")
                    .find(".invalid-feedback")
                    .remove();
            }
        });

        if (isValid) {
            ajaxHandler(
                $("#discountForm"),
                feesRoute.feeDiscount,
                "post",
                "#discountForm",
                "#savefee",
                "#discountModal",
                "#payment_details"
            );
            setTimeout(function () {
                location.reload();
            }, 1000);
        }
    });

    function getQueryParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }

    const departmentValue = getQueryParam("department");
    const classroomValue = getQueryParam("classroom");

    if (departmentValue) {
        $("#department").val(departmentValue).trigger("change");
    }
    setTimeout(function () {
        if (classroomValue) {
            $("#classroom-filter").val(classroomValue).trigger("change");
            $("#filter").trigger("click");
        }
    }, 2000);
});

$(document).on("click", ".exportData", function () {
    var url = feesRoute.export;
    var data = {
        start_date: $("#start_date").val(),
        end_date: $("#end_date").val(),
        department: $("#department").val(),
        classroom: $("#classroom-filter").val(),
        student_id: $("#student-data").val(),
    };

    exportData(url, data);
});

$(document).on("click", ".exportDetailedFees", function () {
    if ($("#classroom-filter").val() == "") {
        toastr.error("Please select a classroom or student");
        return;
    }

    var url = feesRoute.exportDetailed;
    var data = {
        department_id: $("#department").val(),
        classroom_id: $("#classroom-filter").val(),
        student_id: $("#student-data").val(),
    };

    exportData(url, data);
});
