import axiosInstance from '@/lib/axios';

interface QuizTerminationLog {
  id: number;
  classId: string;
  examId: number;
  reason: string;
  terminatedAt: string;
  class: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  exam: {
    id: number;
    exam_name: string;
  };
}

export async function getQuizTerminationLogs(): Promise<QuizTerminationLog[]> {
  try {
    const response = await axiosInstance.get('/violance/get-log');

    if (response.status !== 200) {
      console.error(`Unexpected response status: ${response.status}`);
      return [];
    }

    return response.data.data;
  } catch (error: any) {
    console.error(`Failed to fetch quiz termination logs: ${error.message}`);
    return [];
  }
}
