'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Student, StudentProfile } from '@/lib/types';
import { getStudentProfile, updateStudentProfileStatus } from '@/services/studentApi';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { Loader2, FileText, Calendar, School, Home, User, Mail, Phone, Check, X, ArrowLeft, Coins } from 'lucide-react';
import Image from 'next/image';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { Card, CardContent } from '@/components/ui/card';

export default function StudentProfilePage() {
  const params = useParams();
  const router = useRouter();
  const studentId = params.id as string;

  const [student, setStudent] = useState<Student | null>(null);
  const [profile, setProfile] = useState<StudentProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    const fetchProfile = async () => {
      if (!studentId) return;

      try {
        setLoading(true);
        const profileData = await getStudentProfile(studentId);

        if (profileData && profileData.student) {
          setStudent(profileData.student);

          const profileCopy = { ...profileData };
          delete profileCopy.student;
          setProfile(profileCopy);
        } else {
          setProfile(profileData);
          toast.error('Student data not found');
        }
      } catch {
        toast.error('Failed to load student profile');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [studentId]);

  const handleStatusChange = async (status: 'PENDING' | 'APPROVED' | 'REJECTED') => {
    if (!studentId || !profile) return;

    try {
      setUpdating(true);
      await updateStudentProfileStatus(studentId, status);

      const updatedProfile = await getStudentProfile(studentId as string);
      setProfile(updatedProfile);

      toast.success(`Student profile status updated to ${status}`);
    } catch {
      toast.error('Failed to update student profile status');
    } finally {
      setUpdating(false);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'REJECTED':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      default:
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
    }
  };

  const handleGoBack = () => {
    router.back();
  };



  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-center items-center min-h-[60vh]">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Button
          variant="ghost"
          onClick={handleGoBack}
          className="mb-6 hover:bg-gray-100"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Students
        </Button>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="rounded-full bg-gray-100 p-4 mb-4">
              <User className="h-12 w-12 text-gray-400" />
            </div>
            <h2 className="text-xl font-semibold text-gray-700 mb-2">No Profile Found</h2>
            <p className="text-gray-500 text-center max-w-md">
              No profile information is available for this student. The student may not have completed their profile yet.
            </p>
            <Button
              variant="outline"
              onClick={handleGoBack}
              className="mt-6"
            >
              Return to Student List
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <Button
        variant="ghost"
        onClick={handleGoBack}
        className="mb-6 hover:bg-gray-100"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Students
      </Button>

      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="bg-black p-6">
          <h1 className="text-2xl font-bold text-white">Student Profile Details</h1>
          {student && (
            <p className="text-white/80 mt-1">
              {student.firstName} {student.lastName}
            </p>
          )}
        </div>

        <Tabs defaultValue="details" className="w-full">

          <TabsContent value="details" className="p-6 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <h3 className="text-lg font-semibold border-b pb-2">Personal Information</h3>

                {student ? (
                  <div className="space-y-4">
                    {/* First Name - Read Only */}
                    <div className="flex items-center gap-3">
                      <User className="h-5 w-5 text-gray-500" />
                      <div className="w-full">
                        <p className="text-sm text-gray-500">First Name</p>
                        <div className="p-2 bg-gray-50 border border-gray-200 rounded-md mt-1 w-full">
                          <p className="font-medium text-gray-700">{student.firstName}</p>
                        </div>
                      </div>
                    </div>

                    {/* Last Name - Read Only */}
                    <div className="flex items-center gap-3">
                      <User className="h-5 w-5 text-gray-500" />
                      <div className="w-full">
                        <p className="text-sm text-gray-500">Last Name</p>
                        <div className="p-2 bg-gray-50 border border-gray-200 rounded-md mt-1 w-full">
                          <p className="font-medium text-gray-700">{student.lastName}</p>
                        </div>
                      </div>
                    </div>

                    {/* Email - Read Only */}
                    <div className="flex items-center gap-3">
                      <Mail className="h-5 w-5 text-gray-500" />
                      <div className="w-full">
                        <p className="text-sm text-gray-500">Email</p>
                        <div className="p-2 bg-gray-50 border border-gray-200 rounded-md mt-1 w-full">
                          <p className="font-medium text-gray-700">{student.email}</p>
                        </div>
                      </div>
                    </div>

                    {/* Contact - Read Only */}
                    <div className="flex items-center gap-3">
                      <Phone className="h-5 w-5 text-gray-500" />
                      <div className="w-full">
                        <p className="text-sm text-gray-500">Contact Number</p>
                        <div className="p-2 bg-gray-50 border border-gray-200 rounded-md mt-1 w-full">
                          <p className="font-medium text-gray-700">{student.contact || 'Not provided'}</p>
                        </div>
                      </div>
                    </div>

                    {/* Uest Coins - Read Only */}
                    <div className="flex items-center gap-3">
                      <Coins className="h-5 w-5 text-gray-500" />
                      <div className="w-full">
                        <p className="text-sm text-gray-500">Uest Coins</p>
                        <div className="p-2 bg-gray-50 border border-gray-200 rounded-md mt-1 w-full">
                          <p className="font-medium text-gray-700">{student.coins !== undefined ? student.coins : 'Not available'}</p>
                        </div>
                      </div>
                    </div>

                    {/* Birthday */}
                    <div className="flex items-center gap-3">
                      <Calendar className="h-5 w-5 text-gray-500" />
                      <div className="w-full">
                        <p className="text-sm text-gray-500">Birthday</p>
                        <div className="p-2 bg-gray-50 border border-gray-200 rounded-md mt-1 w-full">
                          <p className="font-medium text-gray-700">
                            {profile.birthday ?
                              (() => {
                                try {
                                  return format(new Date(profile.birthday), 'dd MMMM yyyy');
                                } catch {
                                  return 'Invalid date';
                                }
                              })()
                              : 'Not provided'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p className="text-yellow-700">Student information is not available. Please try refreshing the page.</p>
                  </div>
                )}
              </div>

              <div className="space-y-6">
                <h3 className="text-lg font-semibold border-b pb-2">Educational Information</h3>

                <div className="space-y-4">
                  {/* Medium */}
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-gray-500" />
                    <div className="w-full">
                      <p className="text-sm text-gray-500">Medium</p>
                      <div className="p-2 bg-gray-50 border border-gray-200 rounded-md mt-1 w-full">
                        <p className="font-medium text-gray-700">{profile.medium}</p>
                      </div>
                    </div>
                  </div>

                  {/* Classroom */}
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-gray-500" />
                    <div className="w-full">
                      <p className="text-sm text-gray-500">Classroom</p>
                      <div className="p-2 bg-gray-50 border border-gray-200 rounded-md mt-1 w-full">
                        <p className="font-medium text-gray-700">{profile.classroom}</p>
                      </div>
                    </div>
                  </div>

                  {/* School */}
                  <div className="flex items-center gap-3">
                    <School className="h-5 w-5 text-gray-500" />
                    <div className="w-full">
                      <p className="text-sm text-gray-500">School</p>
                      <div className="p-2 bg-gray-50 border border-gray-200 rounded-md mt-1 w-full">
                        <p className="font-medium text-gray-700">{profile.school}</p>
                      </div>
                    </div>
                  </div>

                  {/* Address */}
                  <div className="flex items-start gap-3">
                    <Home className="h-5 w-5 text-gray-500 mt-1" />
                    <div className="w-full">
                      <p className="text-sm text-gray-500">Address</p>
                      <div className="p-2 bg-gray-50 border border-gray-200 rounded-md mt-1 w-full">
                        <p className="font-medium text-gray-700 break-words whitespace-normal overflow-hidden"
                           style={{ wordBreak: 'break-word', overflowWrap: 'break-word' }}>
                          {profile.address}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {profile.documentUrl ? (
              <div className="space-y-4 pt-4">
                <h3 className="text-lg font-semibold mb-4">Identity Document</h3>
                <div className="border rounded-lg p-6 bg-gray-50">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <FileText className="h-6 w-6 text-gray-500" />
                      <span className="font-medium text-lg">Student Document</span>
                    </div>
                    <a
                      href={profile.documentUrl.startsWith('http') ? profile.documentUrl : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${profile.documentUrl}?t=${new Date().getTime()}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline text-sm flex items-center gap-1"
                    >
                      <FileText className="h-4 w-4" />
                      View Document
                    </a>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-16">
                <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-700 mb-2">No Documents Available</h3>
                <p className="text-gray-500 max-w-md mx-auto">This student has not uploaded any identity documents yet.</p>
              </div>
            )}

            {profile.photo && (
              <div className="mt-8 border-t pt-6">
                <h3 className="text-lg font-semibold mb-4">Profile Photo</h3>
                <div className="flex justify-center">
                  <div className="border rounded-lg shadow-md bg-gray-50 p-4 max-w-full">
                    <div className="flex justify-center">
                      <Image
                        src={profile.photo.startsWith('http') ? profile.photo : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${profile.photo}?t=${new Date().getTime()}`}
                        alt="Student Photo"
                        className="max-w-full max-h-96 object-contain rounded-lg"
                        height={1000}
                        width={1000}
                        style={{ height: 'auto', width: 'auto' }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

        </Tabs>

        <div className="p-6 border-t mt-4 bg-gray-50">
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-center">
            <div className="flex items-center gap-3">
              <span className="font-medium">Profile Status:</span>
              <Badge className={`${getStatusBadgeClass(profile.status)} px-3 py-1 text-sm font-medium`}>
                {profile.status}
              </Badge>
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                className="border-green-500 text-green-600 hover:bg-green-50"
                onClick={() => handleStatusChange('APPROVED')}
                disabled={updating || profile.status === 'APPROVED'}
              >
                {updating && profile.status !== 'APPROVED' ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Check className="h-4 w-4 mr-2" />
                )}
                Approve
              </Button>

              <Button
                variant="outline"
                className="border-red-500 text-red-600 hover:bg-red-50"
                onClick={() => handleStatusChange('REJECTED')}
                disabled={updating || profile.status === 'REJECTED'}
              >
                {updating && profile.status !== 'REJECTED' ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <X className="h-4 w-4 mr-2" />
                )}
                Reject
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
