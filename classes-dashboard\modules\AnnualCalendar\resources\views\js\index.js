let calendarEl = document.getElementById('calendar');

let calendar = new FullCalendar.Calendar(calendarEl, {
    headerToolbar: {
        left: 'prev,next today',
        center: 'title',
        right: 'dayGridMonth,listMonth'
    },
    height: "auto",
    events: {
        url: calenderRoute.index,
        cache: true
    },
    eventDidMount: function(arg) {
        let cs = document.querySelectorAll(".cs");
        cs.forEach(function(v) {
            if (v.checked) {
                if (arg.event.extendedProps.cid === v.value) {
                    arg.el.style.display = "block";
                }
            } else {
                if (arg.event.extendedProps.cid === v.value) {
                    arg.el.style.display = "none";
                }
            }
        });
    },
    loading: function(bool) {
        $('.page-loader').toggle();
    },
});

calendar.render();

let csx = document.querySelectorAll(".cs");
csx.forEach(function(el) {
    el.addEventListener("change", function() {
        calendar.refetchEvents();
    });
});