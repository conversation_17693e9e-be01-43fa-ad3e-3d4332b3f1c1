<?php

namespace Batches\Repositories;

use Batches\Models\Batches;
use Batches\Interfaces\BatchesInterface;
use Batches\Models\BatchesClassroom;
use Batches\Models\BatchesDays;
use Batches\Models\BatchesSubjects;
use Batches\Models\BatchesTimeslots;
use Fees\Models\FeeType;
use Illuminate\Support\Facades\Auth;

class BatchesRepository implements BatchesInterface
{
    protected $batches;
    protected $batchesDays;
    protected $batchesSubjects;
    protected $batchesTimeslots;
    protected $batchesClassroom;

    public function __construct(
        Batches $batches,
        BatchesDays $batchesDays,
        BatchesSubjects $batchesSubjects,
        BatchesTimeslots $batchesTimeslots,
        BatchesClassroom $batchesClassroom
    ) {
        $this->batches = $batches;
        $this->batchesDays = $batchesDays;
        $this->batchesSubjects = $batchesSubjects;
        $this->batchesTimeslots = $batchesTimeslots;
        $this->batchesClassroom = $batchesClassroom;
    }

    public function getAll($request)
    {
        $batches = $this->batches::with(['batchSubjects', 'days', 'batchTimeslots', 'batchClassroom', 'resource'])
            ->where('year_id', getActiveYearId())
            ->where('class_uuid', Auth::id());

        searchColumn($request->input('columns'), $batches);
        orderColumn($request, $batches, 'batches.id');

        return $batches;
    }

    public function getDatatable($list)
    {
        return datatables()->of($list)
            ->addColumn('batch_name', fn($data) => $data->batch_name)

            ->addColumn(
                'subjects',
                fn($data) =>
                $data->batchSubjects->pluck('subject.subject_name')->implode(', ')
            )

            ->addColumn(
                'days',
                fn($data) =>
                $data->days->pluck('days')->implode(', ')
            )

            ->addColumn(
                'timeslots',
                fn($data) =>
                $data->batchTimeslots->map(function ($slot) {
                    return $slot->timeslot->start_time .
                        ' - ' .
                        $slot->timeslot->end_time;
                })->implode('<br>')
            )

            ->addColumn(
                'classroom',
                fn($data) =>
                $data->batchClassroom->pluck('classroom.class_name')->implode(', ')
            )

            ->addColumn(
                'resource',
                fn($data) =>
                optional($data->resource)->resource_name ?? '-'
            )

            ->addColumn('action', function ($data) {
                $button = '';
                if (Auth::user()->can('update batches')) {
                    $button .= '<button data-toggle="modal" data-target="#newBatchesEntry" type="button" class="editBatchesEntry btn" title="Edit" data-editbatchesid="' . $data->id . '"><i class="fa fa-edit"></i></button>';
                }
                if (Auth::user()->can('delete batches')) {
                    $button .= '<button type="button" class="deleteBatchesEntry btn" title="Delete" data-deletebatchesid="' . $data->id . '"><i class="fa fa-trash"></i></button>';
                }
                return $button;
            })
            ->rawColumns(['action', 'timeslots'])
            ->make(true);
    }

    public function saveBatches($data, $id = null)
    {
        if ($id) {
            $batch = $this->batches::findOrFail($id);
            $batch->batch_name = $data->batch_name;
            $batch->resource_id = $data->resource;
            $batch->save();

            $this->batchesClassroom::where('batch_id', $id)->delete();
            $this->batchesSubjects::where('batch_id', $id)->delete();
            $this->batchesTimeslots::where('batch_id', $id)->delete();
            $this->batchesDays::where('batch_id', $id)->delete();
        } else {
            $batch = $this->batches::create([
                'batch_name'  => $data->batch_name,
                'year_id'     => getActiveYearId(),
                'class_uuid'  => Auth::id(),
                'resource_id' => $data->resource,
            ]);
            $id = $batch->id;
        }

        // Insert classroom
        foreach ($data->classroom as $classroomId) {
            $this->batchesClassroom::create([
                'batch_id'     => $id,
                'classroom_id' => $classroomId,
            ]);
        }

        // Insert subjects
        foreach ($data->subjects as $subjectId) {
            $this->batchesSubjects::create([
                'batch_id'   => $id,
                'subject_id' => $subjectId,
            ]);
        }

        // Insert timeslots
        foreach ($data->timeslots as $timeslotId) {
            $this->batchesTimeslots::create([
                'batch_id'    => $id,
                'timeslot_id' => $timeslotId,
            ]);
        }

        // Insert days
        foreach ($data->days as $dayName) {
            $this->batchesDays::create([
                'batch_id' => $id,
                'days'     => $dayName,
            ]);
        }

        return $batch;
    }

    public function getBatchesById($id)
    {
        return $this->batches
            ->with([
                'batchSubjects',
                'batchSubjects.subject',
                'batchTimeslots',
                'batchTimeslots.timeslot',
                'batchClassroom',
                'batchClassroom.classroom.feesDetails.getCategoryFees',
                'resource',
                'days',
            ])
            ->find($id);
    }

    public function rawQueryForTimetable($request)
    {
        $rawBatches = $this->getAll($request)->get();
        $ctdata = collect();

        foreach ($rawBatches as $batch) {
            foreach ($batch->batchTimeslots as $tslot) {
                $slot = $tslot->timeslot;
                if (!$slot) continue;

                foreach ($batch->days as $day) {
                    $dayName = $day->days;

                    $classroomsData = [];
                    foreach ($batch->batchClassroom as $classroomItem) {
                        $classroom = optional($classroomItem->classroom);
                        $classroomIdMatch = !$request->classroom || $request->classroom == $classroom->id;
                        $departmentMatch = !$request->department || $request->department == $classroom->department_id;
                        $feeDetailItem = $classroomItem->classroom->feesDetails->first();

                         if ($feeDetailItem && $feeDetailItem->getCategoryFees) {
                                $categoryFees = $feeDetailItem->getCategoryFees;

                                if ($categoryFees instanceof \Illuminate\Support\Collection) {
                                    $fee = $categoryFees->sum('amount');
                                    $feeTypeModel = $feeDetailItem->feeType->name;
                                }

                                $fee_type = $feeTypeModel ?? 'N/A';
                        }

                        if ($classroomIdMatch && $departmentMatch) {
                            $classroomName = $classroom->class_name;

                            $subjects = collect($batch->batchSubjects)
                                ->filter(function ($batchSubject) use ($classroomItem) {
                                    return optional($batchSubject->subject)->classroom_id === $classroomItem->classroom_id;
                                })
                                ->pluck('subject.subject_name')
                                ->unique()
                                ->values();

                            $resourceName = optional($batch->resource)->resource_name;

                            $classroomsData[] = [
                                'name' => $classroomName,
                                'subjects' => $subjects,
                                'resource' => $resourceName,
                                'fee_type' => $fee_type,
                                'fees' => $fee
                            ];
                        }
                    }

                    $ctdata->push((object)[
                        'batch_id' => $batch->id,
                        'batch_name' => $batch->batch_name ?? 'Unnamed Batch',
                        'timeslot_id' => $slot->id,
                        'start_time' => $slot->start_time,
                        'end_time' => $slot->end_time,
                        'is_break' => $slot->is_break,
                        'break_name' => $slot->break_name,
                        'day' => $dayName,
                        'notes' => $batch->notes,
                        'classrooms' => $classroomsData
                    ]);
                }
            }
        }

        return $ctdata;
    }
}
