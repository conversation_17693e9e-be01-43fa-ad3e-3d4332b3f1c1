<?php

namespace Batches\Http\Controllers;

use App\Exports\TimeTableExport;
use Batches\Http\Requests\CreateBatchesRequest;
use Batches\Repositories\BatchesRepository;
use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Classroom\Repositories\ClassroomRepository;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Resources\Repositories\ResourceRepository;
use Timeslots\Repositories\TimeSlotRepository;

class BatchesController extends Controller
{

    protected $batchesRepository;
    protected $classroomRepository;
    protected $timeSlotRepository;
    protected $resourceRepository;

    public function __construct(
        BatchesRepository $batchesRepository,
        ClassroomRepository $classroomRepository,
        TimeSlotRepository $timeSlotRepository,
        ResourceRepository $resourceRepository

    ) {
        $this->middleware('permission:read batches', ['only' => ['index']]);
        $this->middleware('permission:create batches', ['only' => ['create', 'store']]);
        $this->middleware('permission:update batches', ['only' => ['edit', 'update']]);
        $this->middleware('permission:delete batches', ['only' => ['destroy']]);
        $this->middleware('permission:export batches data', ['only' => ['exportBatches']]);
        $this->batchesRepository = $batchesRepository;
        $this->classroomRepository = $classroomRepository;
        $this->timeSlotRepository = $timeSlotRepository;
        $this->resourceRepository = $resourceRepository;
    }

    public function index(Request $request)
    {
        $list = $this->batchesRepository->getAll($request);
        if (request()->ajax()) {
            return $this->batchesRepository->getDatatable($list);
        }
        return view('Batches::index', compact('list'));
    }

    public function create(Request $request)
    {
        $classroom = $this->classroomRepository->getAll($request)->get();
        $timeslots = $this->timeSlotRepository->getAll($request)->get();
        $resource = $this->resourceRepository->getAll($request)->get();

        return view('Batches::create', compact('classroom', 'timeslots', 'resource'));
    }

    public function store(CreateBatchesRequest $request)
    {
        $this->batchesRepository->saveBatches($request);
        return response()->json(['success' => 'Batches Created Successfully!!']);
    }

    public function edit(Request $request, $id)
    {
        $data = $this->batchesRepository->getBatchesById($id);
        $classroom = $this->classroomRepository->getAll($request)->get();
        $timeslots = $this->timeSlotRepository->getAll($request)->get();
        $resource = $this->resourceRepository->getAll($request)->get();

        return view('Batches::edit', compact('data', 'classroom', 'timeslots', 'resource'));
    }

    public function update(CreateBatchesRequest $request, $id)
    {
        $this->batchesRepository->saveBatches($request, $id);
        return response()->json(['success' => 'Batches Updated successfully!!']);
    }

    public function destroy($id)
    {
        $batches = $this->batchesRepository->getBatchesById($id);
        $batches->delete();
        return response()->json(['success' => 'Batches deleted successfully!!']);
    }

    public function exportBatches(Request $request)
    {
        $batchess = $this->batchesRepository->getAll($request)->get();
        return commonExport($batchess, 'Batches::export', 'batches');
    }

    public function viewBatches(Request $request)
    {
        $date = $request->date ?? now()->toDateString();
        $startdate = Carbon::parse($date)->startOfWeek(); // Monday
        $enddate = (clone $startdate)->addDays(5); // Saturday

        $classroomforfilter = $this->classroomRepository->getAll($request)->get();
        $department = departmentForStudentPortal();
        $timeslots = $this->timeSlotRepository->getAll($request)->get();
        $ctdata = collect();

        $ctdata = $this->batchesRepository->rawQueryForTimetable($request);

        if ($request->ajax()) {
            return view('Batches::ViewTimetable.ajaxtimetable', compact('ctdata', 'date', 'startdate', 'enddate', 'timeslots'));
        }

        return view('Batches::ViewTimetable.index', compact('ctdata', 'date', 'startdate', 'enddate', 'department', 'classroomforfilter', 'timeslots'));
    }


    public function exportBatchTimetable(Request $request)
    {
        $date = date('Y-m-d');
        $startdate = new Carbon(date('d-m-Y', strtotime($date . '-1 Monday')));
        $enddate = new Carbon(date('d-m-Y', strtotime($startdate . ' +5 days')));
        $timeslots = $this->timeSlotRepository->getAll($request)->get();
        $ctdata = collect();

        $ctdata = $this->batchesRepository->rawQueryForTimetable($request);

        return Excel::download(new TimeTableExport($ctdata, $date, $startdate, $enddate, $timeslots), 'Timetable.xlsx');
    }
}
