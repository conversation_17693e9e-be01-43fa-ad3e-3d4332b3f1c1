'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Loader2 } from 'lucide-react';
import Link from 'next/link';
import { UseFormReturn } from 'react-hook-form';
import { ProfileFormValues } from '@/lib/validations/classesEditSchema';

interface ProfileTabProps {
  profileForm: UseFormReturn<ProfileFormValues>;
  handleSubmit: (data: ProfileFormValues) => Promise<void>;
  isSaving: boolean;
  formData: any;
}

const ProfileTab: React.FC<ProfileTabProps> = ({
  profileForm,
  handleSubmit,
  isSaving,
  formData,
}) => {

  return (
    <Form {...profileForm}>
      <form onSubmit={profileForm.handleSubmit(handleSubmit)} className="space-y-8 pt-6">
        <>
          <h2 className="text-lg font-semibold text-black border-b border-gray-200 pb-2">
            Basic Information
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Username */}
            <FormField
              control={profileForm.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-black font-medium">
                    Username <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter username"
                      className="border-gray-300 focus:border-black focus:ring-black"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Class Name */}
            <FormField
              control={profileForm.control}
              name="className"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-black font-medium">
                    Class Name <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter class name"
                      className="border-gray-300 focus:border-black focus:ring-black"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* First Name */}
            <FormField
              control={profileForm.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-black font-medium">
                    First Name <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter first name"
                      className="border-gray-300 focus:border-black focus:ring-black"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Last Name */}
            <FormField
              control={profileForm.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-black font-medium">
                    Last Name <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter last name"
                      className="border-gray-300 focus:border-black focus:ring-black"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Email */}
            <FormField
              control={profileForm.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-black font-medium">
                    Email <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Enter email address"
                      className="border-gray-300 focus:border-black focus:ring-black"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Contact Number */}
            <FormField
              control={profileForm.control}
              name="contactNo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-black font-medium">
                    Contact Number <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter contact number"
                      className="border-gray-300 focus:border-black focus:ring-black"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Is Verified - Read Only */}
            <FormField
              control={profileForm.control}
              name="isVerified"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-black font-medium">Verification Status</FormLabel>
                  <FormControl>
                    <Input
                      value={field.value ? 'Verified' : 'Not Verified'}
                      className="border-gray-300 bg-gray-100 text-black cursor-not-allowed"
                      readOnly
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-2">
              <Label htmlFor="createdAt" className="text-black font-medium">Created At</Label>
              <Input
                id="createdAt"
                name="createdAt"
                value={formData.createdAt ? new Date(formData.createdAt).toLocaleDateString() : ''}
                className="border-gray-300 bg-gray-100 text-black"
                readOnly
              />
            </div>
          </div>

          <div className="space-y-6">
            <h2 className="text-lg font-semibold text-black border-b border-gray-200 pb-2">
              About Information
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Birthday */}
              <FormField
                control={profileForm.control}
                name="birthDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black font-medium">
                      Birthday <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        className="border-gray-300 focus:border-black focus:ring-black"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Catchy Headline */}
              <FormField
                control={profileForm.control}
                name="catchyHeadline"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black font-medium">
                      Catchy Headline <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter catchy headline"
                        className="border-gray-300 focus:border-black focus:ring-black"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Tutor Bio */}
              <FormField
                control={profileForm.control}
                name="tutorBio"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black font-medium">
                      Tutor Bio <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter tutor bio"
                        className="border-gray-300 focus:border-black focus:ring-black"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Save Button for Profile */}
          <div className="flex justify-end gap-4 pt-6">
            <Link href="/dashboard">
              <Button type="button" variant="outline" className="border-gray-300 text-black hover:bg-black hover:text-white">
                Cancel
              </Button>
            </Link>
            <Button
              type="submit"
              disabled={isSaving}
              className="bg-black hover:bg-gray-800 text-white disabled:bg-gray-400"
            >
              {isSaving ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
          </div>
        </>
      </form>
    </Form>
  );
};

export default ProfileTab;
