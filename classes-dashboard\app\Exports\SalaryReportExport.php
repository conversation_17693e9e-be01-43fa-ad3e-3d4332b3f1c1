<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\WithStyles;

class SalaryReportExport implements FromCollection, WithHeadings, WithStyles
{
    use Exportable;
    
    protected $item;
    public function __construct ($item)
    {
        $this->item = $item;
    }
    /**
     * 
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return collect($this->item);
    }

    public function headings() :array
    {
        return [
            'Employee name',
            'A/c. No',
            'Amount',
            'Narration'
        ];
    }

    
    public function styles(Worksheet $sheet)
    {
        return [
        1    => ['font' => ['bold' => true]],
        ];
    }
}
