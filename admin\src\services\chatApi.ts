import axiosInstance from "../lib/axios";
import { Class, Student, ConversationResponse } from "../lib/types";

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export const getClasses = async (page: number = 1, limit: number = 10, search?: string): Promise<PaginatedResponse<Class>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...(search && { search })
  });
  const response = await axiosInstance.get(`/admin-chat/classes?${params}`);
  return response.data;
};

export const getStudentsForClass = async (classId: string, page: number = 1, limit: number = 10, search?: string): Promise<PaginatedResponse<Student>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...(search && { search })
  });
  const response = await axiosInstance.get(`/admin-chat/students/${classId}?${params}`);
  return response.data;
};

export const getConversationBetween = async (classId: string, studentId: string): Promise<ConversationResponse> => {
  const response = await axiosInstance.get(`/admin-chat/conversation/${classId}/${studentId}`);
  return response.data.data;
};
