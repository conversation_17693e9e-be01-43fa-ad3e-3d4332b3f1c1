{"name": "@types/luxon", "version": "3.4.2", "description": "TypeScript definitions for luxon", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/luxon", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "carsonf", "url": "https://github.com/carsonf"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/luxon"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "83a770fd2e692c43654e302f45c777063f107c3ff280ada4a4e2a8f13d1f19f8", "typeScriptVersion": "4.6"}