import axiosInstance from '../lib/axios';
import { QuestionInput } from '../lib/types';

export const getQuestions = async (
  examId: number,
  page: number = 1,
  limit: number = 10,
  search: string = ''
): Promise<any> => {
  try {
    const response = await axiosInstance.get('/questions/', {
      params: { examId, page, limit, search },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch questions: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const getQuestion = async (id: number): Promise<Promise<any>> => {
  try {
    const response = await axiosInstance.get(`/questions/${id}`);
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch question: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const createQuestion = async (data: QuestionInput): Promise<Promise<any>> => {
  try {
    const response = await axiosInstance.post('/questions/', data);
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to create question: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const updateQuestion = async (
  id: number,
  data: Partial<QuestionInput>
): Promise<Promise<any>> => {
  try {
    const response = await axiosInstance.put(`/questions/${id}`, data);
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to update question: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const deleteQuestion = async (id: number, examId: number): Promise<Promise<any>> => {
  try {
    await axiosInstance.delete(`/questions/${id}`, { params: { examId } });
    return { success: true };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to delete question: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const saveQuestionAnswers = async (
  answers: { userId: number; questionId: number; answer: string }[]
): Promise<Promise<any>> => {
  try {
    const response = await axiosInstance.post('/questions/save', answers);
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to save answers: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const getResults = async (): Promise<Promise<any>> => {
  try {
    const response = await axiosInstance.get('/result');
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch results: ${error.response?.data?.message || error.message}`,
    };
  }
};
