'use client';

import * as React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import {
  format,
  startOfMonth,
  endOfMonth,
  startOfWeek,
  endOfWeek,
  addDays,
  addMonths,
  subMonths,
  isSameMonth,
  isSameDay,
  isToday
} from 'date-fns';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface CalendarProps {
  className?: string;
  selected?: Date;
  onSelect?: (date: Date) => void;
  disabled?: (date: Date) => boolean;
  mode?: 'single' | 'range';
}

function Calendar({
  className,
  selected,
  onSelect,
  disabled,
  ...props
}: CalendarProps) {
  const [currentMonth, setCurrentMonth] = React.useState(selected || new Date());

  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(monthStart);
  const startDate = startOfWeek(monthStart);
  const endDate = endOfWeek(monthEnd);

  const dateFormat = 'MMMM yyyy';
  const rows = [];
  let days = [];
  let day = startDate;
  let formattedDate = '';

  // Generate calendar days
  while (day <= endDate) {
    for (let i = 0; i < 7; i++) {
      formattedDate = format(day, 'd');
      const cloneDay = day;

      days.push(
        <div
          key={day.toString()}
          className={cn(
            'relative p-0 text-center text-sm focus-within:relative focus-within:z-20 cursor-pointer',
            'h-8 w-8 flex items-center justify-center rounded-md hover:bg-accent hover:text-accent-foreground',
            {
              'text-muted-foreground': !isSameMonth(day, monthStart),
              'bg-primary text-primary-foreground': selected && isSameDay(day, selected),
              'bg-accent text-accent-foreground': isToday(day) && (!selected || !isSameDay(day, selected)),
              'opacity-50 cursor-not-allowed': disabled && disabled(day),
            }
          )}
          onClick={() => {
            if (!disabled || !disabled(cloneDay)) {
              onSelect?.(cloneDay);
            }
          }}
        >
          <span className="font-normal">{formattedDate}</span>
        </div>
      );
      day = addDays(day, 1);
    }
    rows.push(
      <div className="flex w-full mt-2" key={day.toString()}>
        {days}
      </div>
    );
    days = [];
  }

  const nextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  const prevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  return (
    <div className={cn('p-3', className)} {...props}>
      <div className="flex flex-col gap-4">
        {/* Header */}
        <div className="flex justify-center pt-1 relative items-center w-full">
          <Button
            variant="outline"
            size="sm"
            className="absolute left-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100"
            onClick={prevMonth}
          >
            <ChevronLeft className="size-4" />
          </Button>
          <div className="text-sm font-medium">
            {format(currentMonth, dateFormat)}
          </div>
          <Button
            variant="outline"
            size="sm"
            className="absolute right-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100"
            onClick={nextMonth}
          >
            <ChevronRight className="size-4" />
          </Button>
        </div>

        {/* Calendar Grid */}
        <div className="w-full border-collapse space-x-1">
          {/* Days of week header */}
          <div className="flex">
            {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (
              <div
                key={day}
                className="text-muted-foreground rounded-md w-8 font-normal text-[0.8rem] text-center"
              >
                {day}
              </div>
            ))}
          </div>

          {/* Calendar rows */}
          {rows}
        </div>
      </div>
    </div>
  );
}

export { Calendar };
