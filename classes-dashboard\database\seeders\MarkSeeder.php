<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class MarkSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        $marksManagementPermissions = [
            'MARKS_MANAGEMENT' => [
                'enter marks' => 'create',
                'view marks' => 'view',
                'update marks' => 'update',
                'lock marks' => 'lock',
                'download result' => 'download', 
            ],
        ];

        foreach ($marksManagementPermissions['MARKS_MANAGEMENT'] as $description => $action) {
            Permission::firstOrCreate(
                ['name' => $description],
                ['guard_name' => 'web']
            );
        }
    }
}
