"use client";

import { useEffect, useState, useCallback } from "react";
import { useParams } from "next/navigation";
import Image from "next/image";
import { ColumnDef } from "@tanstack/react-table";
import { Skeleton } from "@/components/ui/skeleton";
import { DataTable } from "@/app-components/dataTable";
import Pagination from "@/app-components/pagination";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Filter,
  Download,
  Camera,
  Calendar,
  RefreshCw,
  X,
  AlertTriangle,
} from "lucide-react";
import { toast } from "sonner";
import { downloadExamRankingsExcel, ExamRankingFilters, getExamRankings } from "@/services/uwhiz-result";
import { getStudentExamPhotos, ExamPhoto } from "@/services/examMonitoringApi";
import { getStudentTerminationLogs } from "@/services/uwhizQuizTerminationLogApi";

type Ranking = {
  rank: number;
  firstName: string;
  lastName: string;
  email: string;
  score: number;
  contact: string;
  attempts: number;
  totalQuestions: number;
  id?: number;
  studentId?: number;
  totalTerminations?: number;
};

interface PaginationData {
  totalPages?: number;
  totalItems?: number;
  totalEntries?: number;
}
interface TerminationLog {
  id: number;
  reason: string;
  createdAt: string;
};

export default function ExamRankingPage() {
  const { examId } = useParams();
  const [data, setData] = useState<Ranking[]>([]);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalEntries, setTotalEntries] = useState(0);
  const [loading, setLoading] = useState(true);
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Ranking | null>(null);
  const [studentPhotos, setStudentPhotos] = useState<ExamPhoto[]>([]);
  const [photosLoading, setPhotosLoading] = useState(false);
  const [terminationLogs, setTerminationLogs] = useState<TerminationLog[]>([]);
  const [terminationLogsLoading, setTerminationLogsLoading] = useState(false);
  const [selectedStudentForTermination, setSelectedStudentForTermination] = useState<Ranking | null>(null);
  const [filters, setFilters] = useState<ExamRankingFilters>({
    firstName: '',
    lastName: '',
    email: '',
    score: '',
    rank: '',
    contact: ''
  });

  const [appliedFilters, setAppliedFilters] = useState<ExamRankingFilters>({});

  const getPhotoUrl = (photoUrl: string) => {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:4005/api/v1";
    const serverBaseUrl = baseUrl.replace("/api/v1", "");
    const fullUrl = `${serverBaseUrl}${photoUrl}`;
    console.log("Photo URL:", { photoUrl, baseUrl, serverBaseUrl, fullUrl });
    return fullUrl;
  };

  const fetchStudentPhotos = async (student: Ranking) => {
    if (!examId) {
      toast.error("Exam ID not found");
      return;
    }

    const studentId = student.id || student.studentId;
    if (!studentId) {
      toast.error("Student ID not found");
      return;
    }

    setPhotosLoading(true);
    setSelectedStudent(student);

    try {
      const result = await getStudentExamPhotos(studentId.toString(), parseInt(examId as string));
      if (result.success && result.data) {
        setStudentPhotos(Array.isArray(result.data) ? result.data : []);
        toast.success(
          `Found ${Array.isArray(result.data) ? result.data.length : 0} photos for ${student.firstName} ${student.lastName}`
        );
      } else {
        setStudentPhotos([]);
        toast.error(result.error || "No photos found for this student");
      }
    } catch (error) {
      toast.error("Failed to fetch student photos");
      console.error("Failed to fetch student photos", error);
      setStudentPhotos([]);
    } finally {
      setPhotosLoading(false);
    }
  };
  const fetchStudentTerminationLogs = async (student: Ranking) => {
    if (!examId) {
      toast.error("Exam ID not found");
      return;
    }
    const studentId = student.id || student.studentId;
    if (!studentId) {
      toast.error("Student ID not found");
      return;
    }

    setTerminationLogsLoading(true);
    setSelectedStudentForTermination(student);

    try {
      const result = await getStudentTerminationLogs(parseInt(examId as string), studentId.toString());
      console.log("Termination Logs API response:", result);
      if (result.success && result.data) {
        let logs = [];
        if (result.data.terminationLogs && Array.isArray(result.data.terminationLogs)) {
          logs = result.data.terminationLogs;
        } else if (Array.isArray(result.data)) {
          logs = result.data;
        } else {
          logs = [];
        }
        setTerminationLogs(logs);
      } else {
        setTerminationLogs([]);
        toast.error(result.error || "No termination logs found");
      }
    } catch (error) {
      toast.error("Failed to fetch termination logs");
      console.error("Failed to fetch termination logs", error);
      setTerminationLogs([]);
    } finally {
      setTerminationLogsLoading(false);
    }
  };
  const downloadPhoto = (photo: ExamPhoto) => {
    const link = document.createElement("a");
    link.href = getPhotoUrl(photo.photoUrl);
    link.download = `exam_${examId}_student_${photo.studentId}_${photo.id}.jpg`;
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const fetchRankings = useCallback(async (
    page: number = 1,
    limit: number = 10,
    filtersToApply: ExamRankingFilters = {}
  ) => {
    if (!examId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await getExamRankings(examId as string, page, limit, filtersToApply);
      if (!response.success && response.error) {
        throw new Error(response.error);
      }
      let rankings: Ranking[] = [];
      let pagination: PaginationData = {};
      if (response.success) {
        rankings = response.data?.rankings || response.data || [];
        pagination = response.data?.pagination || response.pagination || {};
      } else if (response.rankings) {
        rankings = response.rankings || [];
        pagination = {
          totalPages: response.totalPages || 1,
          totalItems: response.totalItems || 0,
        };
      } else {
        rankings = Array.isArray(response) ? response : [];
        pagination = {};
      }
      setData(rankings);
      const totalItems = pagination.totalItems || pagination.totalEntries || rankings.length;
      const calculatedTotalPages = pagination.totalPages || Math.ceil(totalItems / limit) || 1;

      setTotalPages(calculatedTotalPages);
      setTotalEntries(totalItems);
      setError(null);
    } catch (err: any) {
      const errorMessage = err.message || "Unknown error occurred";
      setError("Error fetching rankings: " + errorMessage);
      toast.error("Failed to fetch exam rankings: " + errorMessage);
      setData([]);
      setTotalPages(1);
      setTotalEntries(0);
    } finally {
      setLoading(false);
    }
  }, [examId]);

  useEffect(() => {
    if (examId) {
      fetchRankings(page, limit, appliedFilters);
    }
  }, [examId, page, limit, fetchRankings, appliedFilters]);


  const handleSearch = () => {
    console.log('Applying filters:', filters);
    setPage(1);
    setAppliedFilters({ ...filters });
  };

  const handleResetFilters = async () => {
    console.log('Resetting filters');
    const resetFilters: ExamRankingFilters = {
      firstName: '',
      lastName: '',
      email: '',
      score: '',
      rank: '',
      contact: '',
    };
    setFilters(resetFilters);
    setAppliedFilters({});
    setPage(1);
  };

  const handleDownloadExcel = async () => {
    if (!examId) return;

    setDownloadLoading(true);
    try {
      await downloadExamRankingsExcel(examId as string, appliedFilters);
      toast.success("Excel file downloaded successfully!");
    } catch (error: any) {
      console.error("Download error:", error);
      toast.error(error.message || "Failed to download Excel file");
    } finally {
      setDownloadLoading(false);
    }
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (value: string) => {
    setLimit(Number(value));
    setPage(1);
  };

  const handleFilterChange = (field: keyof ExamRankingFilters, value: string) => {
    setFilters((prev: any) => ({
      ...prev,
      [field]: value
    }));
  };

  const columns: ColumnDef<Ranking>[] = [
    {
      accessorKey: "rank",
      header: "Rank",
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("rank")}</div>
      ),
    },
    {
      accessorKey: "firstName",
      header: "First Name",
      cell: ({ row }) => row.getValue("firstName") || "N/A",
    },
    {
      accessorKey: "lastName",
      header: "Last Name",
      cell: ({ row }) => row.getValue("lastName") || "N/A",
    },
    {
      accessorKey: "email",
      header: "Student Email",
      cell: ({ row }) => row.getValue("email") || "N/A",
    },
    {
      accessorKey: "contact",
      header: "Student Contact",
      cell: ({ row }) => row.getValue("contact") || "N/A",
    },
    {
      accessorKey: "score",
      header: "Correct Answers",
    },
    {
      accessorKey: "attempts",
      header: "Attempts",
    },
    {
      accessorKey: "totalQuestions",
      header: "Total Questions",
    },
    {
      header: "Accuracy",
      cell: ({ row }) => {
        const { score, totalQuestions } = row.original;
        const accuracy = totalQuestions
          ? ((score / totalQuestions) * 100).toFixed(1)
          : "0.0";
        return `${accuracy}%`;
      },
    },
    {
      id: "photos",
      header: "Photos",
      cell: ({ row }) => (
        <Button
          variant="outline"
          size="sm"
          onClick={() => fetchStudentPhotos(row.original)}
          className="flex items-center gap-2"
        >
          <Camera className="w-4 h-4" />
          View Photos
        </Button>
      ),
    },
    {
      id: "termination",
      header: "Termination Logs",
      cell: ({ row }) => {
        return (
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchStudentTerminationLogs(row.original)}
            className="flex items-center gap-2"
          >
            <AlertTriangle className="w-4 h-4" />
            Termination 
          </Button>
        );
      },
    }


  ];

  const hasActiveFilters = Object.values(filters).some(filter => filter && filter.toString().trim() !== '');

  if (!examId) return <div className="p-6">Invalid exam ID</div>;

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold">Exam Rankings</h1>
      <div className="flex justify-end gap-4 mb-4">
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className={`flex items-center gap-2 ${hasActiveFilters ? 'border-primary text-primary' : ''}`}
          aria-label="Toggle filters"
        >
          <Filter className="w-4 h-4" />
          Filters
        </Button>
        <Button
          onClick={handleDownloadExcel}
          disabled={downloadLoading || loading}
          className="flex items-center gap-2 bg-[#ff914d] text-white hover:bg-[#e8823d] disabled:opacity-50"
          aria-label="Download Excel file"
        >
          <Download className="h-4 w-4" />
          {downloadLoading ? "Downloading..." : "Download xlsx"}
        </Button>
      </div>

      {showFilters && (
        <Card>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  placeholder="Filter by first name"
                  value={filters.firstName || ''}
                  onChange={(e) => handleFilterChange('firstName', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  placeholder="Filter by last name"
                  value={filters.lastName || ''}
                  onChange={(e) => handleFilterChange('lastName', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  placeholder="Filter by email"
                  value={filters.email || ''}
                  onChange={(e) => handleFilterChange('email', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="contact">Contact</Label>
                <Input
                  id="contact"
                  placeholder="Filter by contact"
                  value={filters.contact || ''}
                  onChange={(e) => handleFilterChange('contact', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="score">Correct Answers</Label>
                <Input
                  id="score"
                  type="number"
                  placeholder="Filter Correct Answers"
                  value={filters.score || ''}
                  onChange={(e) => handleFilterChange('score', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="rank">Rank</Label>
                <Input
                  id="rank"
                  type="number"
                  placeholder="Filter by rank"
                  value={filters.rank || ''}
                  onChange={(e) => handleFilterChange('rank', e.target.value)}
                />
              </div>
            </div>

            <div className="flex gap-2 mt-4">
              <Button
                onClick={handleSearch}
                disabled={loading || !examId}
              >
                Search
              </Button>
              <Button
                variant="outline"
                onClick={handleResetFilters}
                disabled={loading || !examId}
              >
                Reset
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Data Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>
              Exam Rankings ({totalEntries} total)
              {hasActiveFilters && (
                <span className="text-sm font-normal text-gray-500 ml-2">
                  - Filtered Results
                </span>
              )}
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
          ) : error ? (
            <div className="text-red-600 text-center py-8">{error}</div>
          ) : (
            <>
              <DataTable
                columns={columns}
                data={data}
                isLoading={loading}
              />

              <div className="flex justify-between items-center mt-4">
                <div className="text-sm text-gray-600">
                  {data.length} of {totalEntries} records
                  {limit && totalEntries > limit && ` (${limit} per page)`}
                </div>

                <div className="flex items-center gap-2">
                  <Select
                    value={limit.toString()}
                    onValueChange={handleLimitChange}
                  >
                    <SelectTrigger
                      className="w-[100px]"
                      aria-label="Select number of records per page"
                    >
                      <SelectValue placeholder="Select limit" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                      <SelectItem value="200">200</SelectItem>
                      <SelectItem value="500">500</SelectItem>
                    </SelectContent>
                  </Select>

                  <Pagination
                    page={page}
                    totalPages={totalPages}
                    setPage={handlePageChange}
                    entriesText=""
                  />
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Photo Modal */}
      {selectedStudent && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-6xl max-h-[90vh] overflow-auto w-full">
            <div className="p-4 border-b sticky top-0 bg-white">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-semibold text-lg">
                    {selectedStudent.firstName} {selectedStudent.lastName}
                  </h3>
                  <p className="text-sm text-gray-500">
                    Exam ID: {examId} | Email: {selectedStudent.email} | Rank: {selectedStudent.rank}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => fetchStudentPhotos(selectedStudent)}
                    disabled={photosLoading}
                  >
                    <RefreshCw
                      className={`w-4 h-4 mr-2 ${photosLoading ? "animate-spin" : ""}`}
                    />
                    Refresh
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSelectedStudent(null);
                      setStudentPhotos([]);
                    }}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="p-4">
              {photosLoading ? (
                <div className="flex justify-center items-center py-12">
                  <RefreshCw className="w-8 h-8 animate-spin text-primary" />
                </div>
              ) : studentPhotos.length > 0 ? (
                <div className="space-y-4">
                  <p className="text-sm text-gray-600">
                    Found {studentPhotos.length} photos for this student
                  </p>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {studentPhotos.map((photo) => (
                      <div
                        key={photo.id}
                        className="border rounded-lg overflow-hidden"
                      >
                        <div className="aspect-video relative">
                          <Image
                            src={getPhotoUrl(photo.photoUrl)}
                            alt={`Photo captured at ${formatDate(photo.capturedAt)}`}
                            fill
                            className="object-cover"
                            sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                          />
                        </div>
                        <div className="p-2">
                          <div className="flex items-center gap-1 text-xs text-gray-500 mb-2">
                            <Calendar className="w-3 h-3" />
                            {formatDate(photo.capturedAt)}
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            className="w-full"
                            onClick={() => downloadPhoto(photo)}
                          >
                            <Download className="w-3 h-3 mr-1" />
                            Download
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Camera className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">
                    No photos found for this student in exam {examId}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      {/* Termination Logs Modal */}
      {selectedStudentForTermination && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-auto w-full">
            <div className="p-4 border-b sticky top-0 bg-white">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-semibold text-lg">
                    Termination Logs - {selectedStudentForTermination.firstName} {selectedStudentForTermination.lastName}
                  </h3>
                  <p className="text-sm text-gray-500">
                    Exam ID: {examId} | Email: {selectedStudentForTermination.email} | Rank: {selectedStudentForTermination.rank}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => fetchStudentTerminationLogs(selectedStudentForTermination)}
                    disabled={terminationLogsLoading}
                  >
                    <RefreshCw
                      className={`w-4 h-4 mr-2 ${terminationLogsLoading ? "animate-spin" : ""}`}
                    />
                    Refresh
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSelectedStudentForTermination(null);
                      setTerminationLogs([]);
                    }}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="p-4">
              {terminationLogsLoading ? (
                <div className="flex justify-center items-center py-12">
                  <RefreshCw className="w-8 h-8 animate-spin text-primary" />
                </div>
              ) : terminationLogs.length > 0 ? (
                <div className="space-y-4">
                  <p className="text-sm text-gray-600">
                    Found {terminationLogs.length} termination logs for this student
                  </p>
                  <div className="space-y-3">
                    {terminationLogs.map((log, index) => (
                      <div
                        key={log.id}
                        className="border rounded-lg p-4 bg-balck-50 border-red-200"
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                Termination #{index + 1}
                              </span>
                              <span className="text-sm text-gray-500">
                                {new Date(log.createdAt).toLocaleString()}
                              </span>
                            </div>
                            <div className="text-sm">
                              <strong>Reason:</strong> {log.reason}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <X className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">
                    No termination logs found for this student in exam {examId}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}