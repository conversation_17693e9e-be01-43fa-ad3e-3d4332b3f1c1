<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Department\Models\Department;
use Illuminate\Database\Seeder;
use StaffLeaveType\Models\LeaveType;
use Years\Models\Years;

class SchoolConfigSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        
        $currentYear = Carbon::now()->year;
        $nextYear = $currentYear + 1;

        $years = Years::create([
            'year_name'  => "$currentYear-$nextYear",
            'start_date' => Carbon::create($currentYear, 6, 1),
            'end_date'   => Carbon::create($nextYear, 5, 31),
            'status'     => 'ACTIVE',
        ]);

        Department::create([
            'name' => 'Education',
            'created_by' => 'Super Admin',
            'educational' => 1,
            'year_id' => $years->id,
        ]);

        LeaveType::create([
            'leave_name' => 'lwp',
        ]);
    }
}
