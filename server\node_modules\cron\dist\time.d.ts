import { DateTime, Zone } from 'luxon';
import { CronJobParams } from './types/cron.types';
export declare class CronTime {
    source: string | DateTime;
    timeZone?: string;
    utcOffset?: number;
    realDate: boolean;
    private second;
    private minute;
    private hour;
    private dayOfMonth;
    private month;
    private dayOfWeek;
    constructor(source: CronJobParams['cronTime'], timeZone?: CronJobParams['timeZone'], utcOffset?: null);
    constructor(source: CronJobParams['cronTime'], timeZone?: null, utcOffset?: CronJobParams['utcOffset']);
    private _getWeekDay;
    sendAt(): DateTime;
    sendAt(i: number): DateTime[];
    getTimeout(): number;
    toString(): string;
    toJSON(): string[];
    getNextDateFrom(start: Date | DateTime, timeZone?: string | Zone): DateTime<boolean>;
    private _findPreviousDSTJump;
    private _checkTimeInSkippedRange;
    private _checkTimeInSkippedRangeSingleHour;
    private _checkTimeInSkippedRangeMultiHour;
    private _forwardDSTJump;
    private _wcOrAll;
    private _hasAll;
    private _parse;
    private _parseField;
}
