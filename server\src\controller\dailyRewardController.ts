import { Request, Response } from 'express';
import { distributeDailyRewards, checkTodayRewardsDistributed } from '../services/dailyLeaderboardRewardService';
import { dailyRewardCron } from '../jobs/dailyRewardCron';

export const manualDistributeRewards = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('🔧 Manual reward distribution triggered by admin');

    const result = await distributeDailyRewards();

    if (result.success) {
      res.status(200).json({
        success: true,
        message: result.message,
        data: result.details
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message
      });
    }
  } catch (error: any) {
    console.error('Error in manual reward distribution:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to distribute rewards'
    });
  }
};

export const testLeaderboard = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('🧪 Testing leaderboard fetch...');

    const { getLeaderboard } = require('../services/mockExamLeaderBoardService');

    // Test different timeframes
    const todayResult = await getLeaderboard('today', 1, 3);
    const weeklyResult = await getLeaderboard('weekly', 1, 3);
    const allTimeResult = await getLeaderboard('all-time', 1, 3);

    res.status(200).json({
      success: true,
      message: 'Leaderboard data fetched successfully',
      data: {
        today: todayResult,
        weekly: weeklyResult,
        allTime: allTimeResult
      }
    });
  } catch (error: any) {
    console.error('Error testing leaderboard:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch leaderboard'
    });
  }
};

export const testRewardWithAllTime = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('🧪 Testing reward distribution with all-time data...');

    // Temporarily modify the service to use all-time data
    const { distributeDailyRewards } = require('../services/dailyLeaderboardRewardService');

    // We'll modify the service temporarily for testing
    const result = await distributeDailyRewards();

    res.status(200).json({
      success: true,
      message: 'Test reward distribution completed',
      data: result
    });
  } catch (error: any) {
    console.error('Error in test reward distribution:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to test reward distribution'
    });
  }
};

export const checkRewardStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const alreadyDistributed = await checkTodayRewardsDistributed();
    const cronStatus = dailyRewardCron.getStatus();
    
    res.status(200).json({
      success: true,
      message: 'Reward status retrieved successfully',
      data: {
        todayRewardsDistributed: alreadyDistributed,
        cronJob: cronStatus
      }
    });
  } catch (error: any) {
    console.error('Error checking reward status:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to check reward status'
    });
  }
};

export const getCronJobStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const status = dailyRewardCron.getStatus();
    res.status(200).json({
      success: true,
      message: 'Cron job status retrieved successfully',
      data: status
    });
  } catch (error: any) {
    console.error('Error getting cron job status:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get cron job status'
    });
  }
};
