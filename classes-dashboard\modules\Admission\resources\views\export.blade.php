<table>
    <thead>
        <tr>
            <th><b>ID</b></th>
            <th><b>Admission Date</b></th>
            <th><b>Student First Name</b></th>
            <th><b>Student Middle Name</b></th>
            <th><b>Student Last Name</b></th>
            <th><b>Gender</b></th>
            <th><b>Date Of Birth</b></th>
            <th><b>Age</b></th>
            <th><b>Aadhaar No</b></th>
            <th><b>Blood Group</b></th>
            <th><b>Birth Place</b></th>
            <th><b>Mother Tongue</b></th>
            <th><b>Address</b></th>
            <th><b>City</b></th>
            <th><b>Pin</b></th>
            <th><b>District</b></th>
            <th><b>State</b></th>
            <th><b>Country</b></th>
            <th><b>Religion</b></th>
            <th><b>Caste</b></th>
            <th><b>Sub Caste</b></th>
            <th><b>Contact No</b></th>
            <th><b>Email</b></th>
            <th><b>Classroom</b></th>
            <th><b>Department</b></th>
            <th><b>Gr No</b></th>
            <th><b>UID No</b></th>
            <th><b>Route</b></th>
            <th><b>Waypoint</b></th>
            <th><b>Photo</b></th>
            <th><b>Admission Type</b></th>
            <th><b>Admission Year</b></th>
            <th><b>Created At</b></th>
        </tr>
    </thead>
    <tbody>
        @foreach ($datas as $data)
            <tr>
                <td>{{ $data->id }}</td>
                <td>{{ date_formatter($data->created_at) }}</td>
                <td>{{ $data->first_name }}</td>
                <td>{{ $data->middle_name }}</td>
                <td>{{ $data->last_name }}</td>
                <td>{{ $data->gender }}</td>
                <td>{{ $data->date_of_birth }}</td>
                <td>{{ $data->age }}</td>
                <td>{{ $data->aadhaar_no }}</td>
                <td>{{ $data->blood_group }}</td>
                <td>{{ $data->birth_place }}</td>
                <td>{{ $data->mother_tongue }}</td>
                <td>{{ $data->address }}</td>
                <td>{{ $data->city }}</td>
                <td>{{ $data->pin }}</td>
                <td>{{ $data->district }}</td>
                <td>{{ $data->state }}</td>
                <td>{{ $data->country }}</td>
                <td>{{ $data->religion }}</td>
                <td>{{ $data->caste }}</td>
                <td>{{ $data->sub_caste }}</td>
                <td>{{ $data->contact_no }}</td>
                <td>{{ $data->email }}</td>
                <td>{{ $data->class_name }}</td>
                <td>{{ $data->department_name }}</td>
                <td>{{ $data->uid_no }}</td>
                <td>{{ $data->route_name }}</td>
                <td>{{ $data->waypoint_name }}</td>
                <td>
                    @php
                        $nodeServerUrl = env('UEST_FRONTEND_URL', 'http://localhost:4005');
                        $imageUrl = $nodeServerUrl . '/uploads/student/'. $data->getStudent->id .'/studentPhoto.jpg';
                        $imageUrlPng = $nodeServerUrl . '/uploads/student/' . $data->getStudent->id . '/studentPhoto.png';
                    @endphp
                    {{ $imageUrl }}
                </td>
                <td>{{ $data->year_name }}</td>
                <td>{{ $data->created_at }}</td>
            </tr>
        @endforeach
    </tbody>
</table>
