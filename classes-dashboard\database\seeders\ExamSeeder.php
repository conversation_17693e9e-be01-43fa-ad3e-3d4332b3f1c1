<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;


class ExamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $examManagementPermissions = [
            'EXAM_MANAGEMENT' => [
                'create exam' => 'create',
                'read exam' => 'view',
                'update exam' => 'update',
                'delete exam' => 'delete',
            ],
        ];

        foreach ($examManagementPermissions['EXAM_MANAGEMENT'] as $description => $action) {
            // Uses the full description in the permission name
            // Creates permissions like: 'create exam', 'view exam', etc.
            Permission::firstOrCreate(
                ['name' => $description],
                ['guard_name' => 'web']
            );
        }
    }
}
