<?php

namespace App\Providers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\ServiceProvider;

class TenantServiceProvider extends ServiceProvider
{
    public function boot(Request $request)
    {
        if (!App::environment('local')) {
            $subdomain = explode('.', $request->getHost())[0];

            $globalTenantDB = 'core';
    
            $tenant = Cache::remember("tenantp_{$subdomain}", 3600, function () use ($subdomain, $globalTenantDB) {
                return DB::connection($globalTenantDB)->table('schools_erp')
                    ->where('subdomain', $subdomain)
                    ->first();
            });
    
            if ($tenant) {
    
                Config::set('database.connections.tenant', [
                    'driver' => 'mysql',
                    'host' => env('DB_HOST'),
                    'database' => "erp_" . $tenant->subdomain,
                    'username' => env('DB_USERNAME'),
                    'password' => env('DB_PASSWORD'),
                    'charset' => 'utf8mb4',
                    'collation' => 'utf8mb4_unicode_ci',
                    'prefix' => '',
                    'strict' => false,
                ]);
    
                DB::purge('tenant');
                DB::setDefaultConnection('tenant');
                DB::reconnect('tenant');
            }
        }
   
    }
}
