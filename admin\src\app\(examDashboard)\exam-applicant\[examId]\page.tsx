"use client";

import { useEffect, useState, useCallback } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import axiosInstance from "@/lib/axios";
import { DataTable } from "@/app-components/dataTable";
import Pagination from "@/app-components/pagination";
import { ExamApplication } from "@/lib/types";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Download,
  Filter,
} from "lucide-react";
import { toast } from "sonner";
import { downloadExamApplicantsExcel } from "@/services/uwhizExamApplicantApi";

interface ExamApplicantFilters {
  firstName?: string;
  lastName?: string;
  email?: string;
  contact?: string;
  search?: string;
}

export default function ExamRankingPage() {
  const [data, setData] = useState<ExamApplication[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [loading, setLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);

  const [filters, setFilters] = useState<ExamApplicantFilters>({
    firstName: '',
    lastName: '',
    email: '',
    contact: '',
    search: ''
  });

  const [appliedFilters, setAppliedFilters] = useState<ExamApplicantFilters>({});
  const params = useParams();
  const examId = Number(params.examId);

  const fetchData = useCallback(async (
    examId: number,
    page: number = 1,
    limit: number = 10,
    filtersToApply: ExamApplicantFilters = appliedFilters
  ) => {
    if (!examId) return;

    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });
      if (filtersToApply.firstName) params.append('firstName', filtersToApply.firstName);
      if (filtersToApply.lastName) params.append('lastName', filtersToApply.lastName);
      if (filtersToApply.email) params.append('email', filtersToApply.email);
      if (filtersToApply.contact) params.append('contact', filtersToApply.contact);
      if (filtersToApply.search) params.append('search', filtersToApply.search);

      const response = await axiosInstance.get(
        `/examApplication/${examId}?${params.toString()}`,
        {
          headers: {
            "Server-Select": "uwhizServer",
          },
        }
      );
      setData(response.data.data);
      setTotal(response.data.total);  
    } catch (error: any) {
      console.error("Failed to fetch data:", error.response?.data?.message || error.message);
     
      setData([]);
    } finally {
      setLoading(false);
    }
  }, [appliedFilters]);

  useEffect(() => {
    if (examId && Object.keys(appliedFilters).length > 0) {
      fetchData(examId, page, limit, appliedFilters);
    } else if (examId) {
      fetchData(examId, page, limit);
    }
  }, [examId, page, limit, fetchData]);

  const handleSearch = () => {
    setPage(1);
    setAppliedFilters(filters);
    fetchData(examId, 1, limit, filters);
  };

  const handleResetFilters = () => {
    const resetFilters: ExamApplicantFilters = {
      firstName: '',
      lastName: '',
      email: '',
      contact: '',
      search: ''
    };
    setFilters(resetFilters);
    setAppliedFilters(resetFilters);
    setPage(1);
    fetchData(examId, 1, limit, resetFilters);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    fetchData(examId, newPage, limit, appliedFilters);
  };

  const handleLimitChange = (value: string) => {
    setLimit(Number(value));
    setPage(1);
    fetchData(examId, 1, Number(value), appliedFilters);
  };

  const handleFilterChange = (field: keyof ExamApplicantFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };
  const handleDownloadExcel = async () => {
    if (!examId) {
      toast.error("Exam ID not found");
      return;
    }

    setDownloadLoading(true);
    try {
      await downloadExamApplicantsExcel(examId, appliedFilters);
      toast.success("Excel file downloaded successfully");
    } catch (error: any) {
      console.error("Download error:", error);
      toast.error("Failed to download Excel file");
    } finally {
      setDownloadLoading(false);
    }
  };

  const columns: ColumnDef<ExamApplication, unknown>[] = [
    {
      accessorKey: "firstName",
      header: "First Name",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "lastName",
      header: "Last Name",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "contact",
      header: "Contact",
      cell: (info) => info.getValue(),
    },
  ];

  const totalPages = Math.ceil(total / limit);
  const hasActiveFilters = Object.values(filters).some(filter => filter && filter.trim() !== '');

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold">Exam Applicants</h1>
      <div className="flex justify-end gap-4 mb-4">
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className={`flex items-center gap-2 ${hasActiveFilters ? 'border-primary text-primary' : ''}`}
        >
          <Filter className="w-4 h-4" />
          Filters
        </Button>
        <Button
          onClick={handleDownloadExcel}
          disabled={downloadLoading || !examId}
          className="flex items-center gap-2 bg-[#ff914d] text-white hover:bg-[#e8823d] disabled:opacity-50"
          aria-label="Download Excel file"
        >
          <Download className="h-4 w-4" />
          {downloadLoading ? "Downloading..." : "Download xlsx"}
        </Button>
      </div>
      {showFilters && (
        <Card>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    placeholder="Filter by first name"
                    value={filters.firstName || ''}
                    onChange={(e) => handleFilterChange('firstName', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    placeholder="Filter by last name"
                    value={filters.lastName || ''}
                    onChange={(e) => handleFilterChange('lastName', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    placeholder="Filter by email"
                    value={filters.email || ''}
                    onChange={(e) => handleFilterChange('email', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="contact">Contact</Label>
                  <Input
                    id="contact"
                    placeholder="Filter by contact"
                    value={filters.contact || ''}
                    onChange={(e) => handleFilterChange('contact', e.target.value)}
                  />
                </div>
              </div>

              <div className="flex gap-2 mt-4">
                <Button
                  onClick={handleSearch}
                  disabled={loading || !examId}
                >
                  Search
                </Button>
                <Button
                  variant="outline"
                  onClick={handleResetFilters}
                  disabled={loading || !examId}
                >
                  Reset
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}



      {/* Data Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>
              Exam Applicants ({total} total)
              {hasActiveFilters && (
                <span className="text-sm font-normal text-gray-500 ml-2">
                  - Filtered Results
                </span>
              )}
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={data}
            isLoading={loading}
          />

          <div className="flex justify-between items-center mt-4">
            <div className="text-sm text-gray-600">
              {data.length} of {total} records
              {limit && total > limit && ` (${limit} per page)`}
            </div>

            <div className="flex items-center gap-2">
              <Select
                value={limit.toString()}
                onValueChange={handleLimitChange}
              >
                <SelectTrigger
                  className="w-[100px]"
                  aria-label="Select number of records per page"
                >
                  <SelectValue placeholder="Select limit" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                  <SelectItem value="200">200</SelectItem>
                  <SelectItem value="500">500</SelectItem>
                </SelectContent>
              </Select>

              <Pagination
                page={page}
                totalPages={totalPages}
                setPage={handlePageChange}
                entriesText=""
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}