'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getQuizTerminationLogs } from '@/services/quizTerminationLog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { ArrowUpDown } from 'lucide-react';

interface QuizTerminationLog {
  id: number;
  classId: string;
  examId: number;
  reason: string;
  terminatedAt: string;
  class: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  exam: {
    id: number;
    exam_name: string;
  };
}

const AdminLogs = () => {
  const router = useRouter();
  const [logs, setLogs] = useState<QuizTerminationLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortField, setSortField] = useState<'terminatedAt' | 'reason'>('terminatedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  useEffect(() => {
    const fetchLogs = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await getQuizTerminationLogs();
        if (!Array.isArray(data)) {
          console.error('Expected array, got:', data);
          setLogs([]);
          setError('Invalid data format received from server');
          return;
        }
        setLogs(data);
      } catch {
        setError('Failed to fetch logs. Please try again.');
        setLogs([]);
      } finally {
        setLoading(false);
      }
    };
    fetchLogs();
  }, [router]);

  const handleSort = (field: 'terminatedAt' | 'reason') => {
    if (field === sortField) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const sortedLogs = [...logs].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    if (sortField === 'terminatedAt') {
      const aDate = new Date(aValue).getTime();
      const bDate = new Date(bValue).getTime();
      return sortOrder === 'asc' ? aDate - bDate : bDate - aDate;
    }
    return sortOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <p>Loading logs...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-6">
        <h1 className="text-2xl font-bold mb-6">Quiz Termination Logs</h1>
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6">
      <h1 className="text-3xl font-bold mb-6">Quiz Termination Logs</h1>
      {logs.length === 0 ? (
        <p>No violation logs found.</p>
      ) : (
        <div className="border rounded-lg overflow-hidden">
          <Table>
            <TableHeader className="bg-gray-200 ">
              <TableRow>
                <TableHead>User Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Exam Name</TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('reason')}
                    className="flex items-center gap-2"
                  >
                    Reason
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                </TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('terminatedAt')}
                    className="flex items-center gap-2"
                  >
                    Terminated At
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedLogs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell>{`${log.class.firstName} ${log.class.lastName}`}</TableCell>
                  <TableCell>{log.class.email}</TableCell>
                  <TableCell>{log.exam.exam_name}</TableCell>
                  <TableCell>{log.reason}</TableCell>
                  <TableCell>
                    {new Date(log.terminatedAt).toLocaleString('en-IN', {
                      timeZone: 'Asia/Kolkata',
                    })}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
};

export default AdminLogs;
