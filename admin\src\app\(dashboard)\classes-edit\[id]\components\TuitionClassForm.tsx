'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { MultiSelect } from '@/components/ui/multi-select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useForm } from 'react-hook-form';

interface TuitionClassFormProps {
  showAddForm: boolean;
  setShowAddForm: (show: boolean) => void;
  newTuitionClass: {
    education: string;
    coachingType: string[];
    boardType: string[];
    medium: string[];
    section: string[];
    subject: string[];
    details: string[];
  };
  handleNewTuitionChange: (field: string, value: string | string[]) => void;
  handleAddTuitionClass: () => void;
  constants: any[];
  getConstantValues: (name: string) => any[];
}

const TuitionClassForm: React.FC<TuitionClassFormProps> = ({
  showAddForm,
  setShowAddForm,
  newTuitionClass,
  handleNewTuitionChange,
  handleAddTuitionClass,
  constants,
  getConstantValues,
}) => {
  const tuitionForm = useForm({
    defaultValues: {
      education: '',
      coachingType: [],
      boardType: [],
      medium: [],
      section: [],
      subject: [],
      details: [],
    },
  });

  return (
    <>
      <div className="flex justify-between items-center border-b border-gray-200 pb-2">
        <h2 className="text-lg font-semibold text-black">
          Tuition Classes
        </h2>
        <Button
          type="button"
          onClick={() => setShowAddForm(!showAddForm)}
          className="bg-black hover:bg-gray-800 text-white"
        >
          <Plus className="w-4 h-4 mr-2" />
          {showAddForm ? 'Cancel' : 'Add Tuition Class'}
        </Button>
      </div>

      {showAddForm && (
        <Form {...tuitionForm}>
          <div className="border border-gray-200 rounded-lg p-4 space-y-4 bg-gray-50">
            <h3 className="text-md font-medium text-gray-700">Add New Tuition Class</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Education Category */}
              <FormField
                control={tuitionForm.control}
                name="education"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black font-medium">Category</FormLabel>
                    <FormControl>
                      <select
                        value={newTuitionClass.education}
                        onChange={(e) => {
                          handleNewTuitionChange('education', e.target.value);
                          handleNewTuitionChange('coachingType', []);
                          handleNewTuitionChange('boardType', []);
                          handleNewTuitionChange('medium', []);
                          handleNewTuitionChange('section', []);
                          handleNewTuitionChange('subject', []);
                          handleNewTuitionChange('details', []);
                          field.onChange(e.target.value);
                        }}
                        className="w-full p-2 border border-gray-300 rounded-md focus:border-black focus:ring-black bg-white text-black"
                      >
                        <option value="">Select Category</option>
                        {constants
                          .filter((cat: any) =>
                            [
                              "Education",
                              "Drama",
                              "Music",
                              "Art & Craft",
                              "Sports",
                              "Languages",
                              "Technology",
                              "Dance",
                              "Computer Classes",
                              "Cooking Classes",
                              "Garba Classes",
                              "Vaidik Maths",
                              "Gymnastic Classes",
                              "Yoga Classes",
                              "Aviation Classes",
                              "Designing Classes"
                            ].includes(cat.name)
                          )
                          .map((cat: any) => (
                            <option key={cat.id} value={cat.name}>
                              {cat.name}
                            </option>
                          ))}
                      </select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Coaching Type */}
              <FormField
                control={tuitionForm.control}
                name="coachingType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black font-medium">Coaching Type</FormLabel>
                    <FormControl>
                      <MultiSelect
                        options={getConstantValues("Coaching Type").map((item: any) => ({
                          label: item.value,
                          value: item.value
                        }))}
                        value={newTuitionClass.coachingType}
                        onChange={(values: string[]) => {
                          handleNewTuitionChange('coachingType', values);
                          field.onChange(values);
                        }}
                        placeholder="Select Coaching Type"
                        className="border-gray-300 focus:border-black focus:ring-black"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {newTuitionClass.education === 'Education' && (
                <>
                  <FormField
                    control={tuitionForm.control}
                    name="boardType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-black font-medium">Board Type</FormLabel>
                        <FormControl>
                          <MultiSelect
                            options={getConstantValues("Board Type").map((item: any) => ({
                              label: item.value,
                              value: item.value
                            }))}
                            value={newTuitionClass.boardType}
                            onChange={(values: string[]) => {
                              handleNewTuitionChange('boardType', values);
                              field.onChange(values);
                            }}
                            placeholder="Select Board Type"
                            className="border-gray-300 focus:border-black focus:ring-black"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={tuitionForm.control}
                    name="medium"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-black font-medium">Medium</FormLabel>
                        <FormControl>
                          <MultiSelect
                            options={getConstantValues("Medium").map((item: any) => ({
                              label: item.value,
                              value: item.value
                            }))}
                            value={newTuitionClass.medium}
                            onChange={(values: string[]) => {
                              handleNewTuitionChange('medium', values);
                              field.onChange(values);
                            }}
                            placeholder="Select Medium"
                            className="border-gray-300 focus:border-black focus:ring-black"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={tuitionForm.control}
                    name="section"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-black font-medium">Section</FormLabel>
                        <FormControl>
                          <MultiSelect
                            options={getConstantValues("Section").map((item: any) => ({
                              label: item.value,
                              value: item.value
                            }))}
                            value={newTuitionClass.section}
                            onChange={(values: string[]) => {
                              handleNewTuitionChange('section', values);
                              field.onChange(values);
                            }}
                            placeholder="Select Section"
                            className="border-gray-300 focus:border-black focus:ring-black"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={tuitionForm.control}
                    name="subject"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-black font-medium">Subject</FormLabel>
                        <FormControl>
                          <MultiSelect
                            options={getConstantValues("Subject").map((item: any) => ({
                              label: item.value,
                              value: item.value
                            }))}
                            value={newTuitionClass.subject}
                            onChange={(values: string[]) => {
                              handleNewTuitionChange('subject', values);
                              field.onChange(values);
                            }}
                            placeholder="Select Subject"
                            className="border-gray-300 focus:border-black focus:ring-black"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}

              {newTuitionClass.education && newTuitionClass.education !== 'Education' && (
                <FormField
                  control={tuitionForm.control}
                  name="details"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel className="text-black font-medium">{newTuitionClass.education} Details</FormLabel>
                      <FormControl>
                        <MultiSelect
                          options={getConstantValues(newTuitionClass.education).map((item: any) => ({
                            label: item.value,
                            value: item.value
                          }))}
                          value={newTuitionClass.details}
                          onChange={(values: string[]) => {
                            handleNewTuitionChange('details', values);
                            field.onChange(values);
                          }}
                          placeholder={`Select ${newTuitionClass.education} Details`}
                          className="border-gray-300 focus:border-black focus:ring-black"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowAddForm(false)}
                className="border-gray-300 text-black hover:bg-gray-100"
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleAddTuitionClass}
                className="bg-black hover:bg-gray-800 text-white"
              >
                Add Tuition Class
              </Button>
            </div>
          </div>
        </Form>
      )}
    </>
  );
};

export default TuitionClassForm;
