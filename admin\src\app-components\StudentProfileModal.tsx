'use client';

import React, { useState, useEffect } from 'react';
import { Student, StudentProfile } from '@/lib/types';
import { getStudentProfile, updateStudentProfileStatus } from '@/services/studentApi';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, FileText, Calendar, School, Home, User, Mail, Phone, Check, X } from 'lucide-react';
import Image from 'next/image';
import { format } from 'date-fns';
import { toast } from 'sonner';

interface StudentProfileModalProps {
  student: Student;
  isOpen: boolean;
  onClose: () => void;
  onStatusUpdate: () => void;
}

export function StudentProfileModal({ student, isOpen, onClose, onStatusUpdate }: StudentProfileModalProps) {
  const [profile, setProfile] = useState<StudentProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    const fetchProfile = async () => {
      if (isOpen && student) {
        try {
          setLoading(true);
          if (student.profile) {
            setProfile(student.profile);
            setLoading(false);
            return;
          }

          const profileData = await getStudentProfile(student.id);
          setProfile(profileData);
        } catch (error) {
          console.error('Failed to fetch student profile:', error);
          toast.error('Failed to load student profile');
        } finally {
          setLoading(false);
        }
      }
    };

    fetchProfile();
  }, [isOpen, student]);

  const handleStatusChange = async (status: 'PENDING' | 'APPROVED' | 'REJECTED') => {
    if (!student || !profile) return;

    try {
      setUpdating(true);
      await updateStudentProfileStatus(student.id, status);
      setProfile({ ...profile, status });
      toast.success(`Student profile status updated to ${status}`);
      onStatusUpdate();
    } catch (error) {
      console.error('Failed to update status:', error);
      toast.error('Failed to update student profile status');
    } finally {
      setUpdating(false);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'REJECTED':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      default:
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl w-[95vw] p-0 overflow-hidden">
        <DialogHeader className="p-6 pb-2">
          <DialogTitle className="text-2xl font-bold">Student Profile Details</DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex justify-center items-center p-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : !profile ? (
          <div className="p-6 text-center">
            <p className="text-lg text-gray-500">No profile information available for this student.</p>
          </div>
        ) : (
          <Tabs defaultValue="details" className="w-full">
            <div className="px-6">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="details">Profile Details</TabsTrigger>
                <TabsTrigger value="documents">Documents</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="details" className="p-6 pt-4 min-h-[600px]">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <User className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Full Name</p>
                      <p className="font-medium">{student.firstName} {student.lastName}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Mail className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium">{student.email}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Phone className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Contact</p>
                      <p className="font-medium">{student.contact}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Birthday</p>
                      <p className="font-medium">
                        {profile.birthday ?
                          (() => {
                            try {
                              return format(new Date(profile.birthday), 'dd MMMM yyyy');
                            } catch {
                              return 'Invalid date';
                            }
                          })()
                          : 'Not provided'}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Medium</p>
                      <p className="font-medium">{profile.medium}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Classroom</p>
                      <p className="font-medium">{profile.classroom}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <School className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">School</p>
                      <p className="font-medium">{profile.school}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <Home className="h-5 w-5 text-gray-500 mt-1" />
                    <div className="flex-1">
                      <p className="text-sm text-gray-500">Address</p>
                      <div className="max-w-[400px]">
                        <p className="font-medium break-words whitespace-normal overflow-hidden"
                           style={{ wordBreak: 'break-word', overflowWrap: 'break-word' }}>
                          {profile.address}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {profile.photo && (
                <div className="mt-6">
                  <p className="text-sm text-gray-500 mb-2">Profile Photo</p>
                  <div className="flex justify-center">
                    <div className="border rounded-lg shadow-md bg-gray-50 p-4 max-w-full">
                      <div className="flex justify-center">
                        <Image
                          src={profile.photo.startsWith('http') ? profile.photo : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${profile.photo}?t=${new Date().getTime()}`}
                          alt="Student Photo"
                          className="max-w-full max-h-80 object-contain rounded-lg"
                          height={1000}
                          width={1000}
                          style={{ height: 'auto', width: 'auto' }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="documents" className="p-6 pt-4 min-h-[600px]">
              {profile.documentUrl ? (
                <div className="space-y-4">
                  <p className="text-sm text-gray-500">Student Document</p>
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <FileText className="h-5 w-5 text-gray-500" />
                        <span className="font-medium">Student Document</span>
                      </div>
                      <a
                        href={profile.documentUrl.startsWith('http') ? profile.documentUrl : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${profile.documentUrl}?t=${new Date().getTime()}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline text-sm"
                      >
                        View Full Size
                      </a>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No documents uploaded</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        )}

        {profile && (
          <div className="p-6 pt-0 border-t mt-4 flex flex-col sm:flex-row gap-3 justify-between items-center">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Current Status:</span>
              <Badge className={getStatusBadgeClass(profile.status)}>
                {profile.status}
              </Badge>
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                className="border-green-500 text-green-600 hover:bg-green-50"
                onClick={() => handleStatusChange('APPROVED')}
                disabled={updating || profile.status === 'APPROVED'}
              >
                {updating && profile.status !== 'APPROVED' ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Check className="h-4 w-4 mr-2" />
                )}
                Approve
              </Button>

              <Button
                variant="outline"
                className="border-red-500 text-red-600 hover:bg-red-50"
                onClick={() => handleStatusChange('REJECTED')}
                disabled={updating || profile.status === 'REJECTED'}
              >
                {updating && profile.status !== 'REJECTED' ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <X className="h-4 w-4 mr-2" />
                )}
                Reject
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

