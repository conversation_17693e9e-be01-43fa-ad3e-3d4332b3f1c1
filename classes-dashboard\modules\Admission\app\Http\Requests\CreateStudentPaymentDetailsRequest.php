<?php

namespace Admission\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateStudentPaymentDetailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'payment_mode' => 'required|string|max:200',
            "paid_amount"    => "required|numeric",
            "payment_taken_by"    => "required|string|max:200",
            "check_no"    => "nullable|string|max:200",
            "payment_category" => "required",
            "installment_name" => "required",
            "student_id" => "required",
        ];
    }
}
