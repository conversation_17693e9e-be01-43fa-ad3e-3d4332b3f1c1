<?php

namespace Circulars\Repositories;

use Circulars\Models\Circulars;
use Circulars\Interfaces\CircularInterface;
use Illuminate\Support\Facades\Auth;

class CircularRepository implements CircularInterface
{
    protected $circular;
    public function __construct(Circulars $circular)
    {
        $this->circular = $circular;
    }

    public function getAll($request)
    {
        $circulars = $this->circular::where('class_uuid', Auth::id());
        
        searchColumn($request->input('columns'), $circulars);
        orderColumn($request, $circulars, 'circulars.id');

        return $circulars;
    }

    public function getDatatable($list)
    {
        return datatables()->of($list)
            ->addColumn('title', function ($data) {
                return $data->title;
            })
            ->addColumn('file', function ($data) {
                return '<a href="' . route('circulars.download', $data->id) . '">' . $data->file . '</a>';
            })
            ->addColumn('created_by', function ($data) {
                return $data->created_by;
            })
            ->addColumn('action', function ($data) {
                $button = '';
                if (Auth::user()->can('delete circular')) {
                    $button .= '<button type="button"
                    class="deleteCircularEntry btn" title="Delete"
                    data-circularid="' . $data->id . '"><i class="fa fa-trash"></i></button>';
                }
                return $button;
            })->rawColumns(['file', 'action'])
            ->make(true);
    }

    public function storeCircular($request)
    {
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $id = Auth::id();

            $fileName = $file->getClientOriginalName();
            $file->storeAs("public/circulars/{$id}", $fileName);

            $this->circular::create([
                'title' => $request->title,
                'created_by' => Auth::user()->first_name . " " . Auth::user()->last_name,
                'file' =>  $fileName,
                'class_uuid' => Auth::id()
            ]);
        }
    }

    public function getCircularById($id)
    {
        return $this->circular::Find($id);
    }
}
