<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Support\Facades\Auth;
use App\Models\Classes;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function loginClassViaToken(Request $request)
    {
        $uid = $request->query('uid');
        $token = $request->query('token');

        if (!$uid || !$token) {
            return abort(403, 'Missing UID or Token');
        }

        try {
            $decoded = JWT::decode($token, new Key('secret123', 'HS256'));
            if ((string) $decoded->id !== (string) $uid) {
                return abort(403, 'Token mismatch');
            }

            $classUser = Classes::findOrFail($uid);
            if (!$classUser) {
                return abort(403, 'Class not found');
            }

            $classStatus = DB::table('ClassesStatus')->where('classId', $classUser->id)->first();

            if (!$classStatus || $classStatus->status != "APPROVED") {
                return abort(403, 'Class is not approved yet');
            }

            // Log in the user
            Auth::guard('class_users')->login($classUser); // Remember the user

            // Save session explicitly
            $request->session()->put('class_users_id', $classUser->id);
            $request->session()->save();

            return redirect()->route('home');
        } catch (\Exception $e) {
            return abort(403, 'Authentication failed: ' . $e->getMessage());
        }
    }
    public function checkByMobile(Request $request)
    {
        $contactNo = $request->input('contact_no');

        if (!preg_match('/^[6-9]\d{9}$/', $contactNo)) {
            return response()->json(['error' => true, 'message' => 'Invalid mobile number']);
        }

        $class = Classes::where('contactNo', $contactNo)->first();
        if (!$class) {
            return response()->json(['error' => true, 'message' => 'Class not found']);
        }

        $classStatus = DB::table('ClassesStatus')->where('classId', $class->id)->first();

        if (!$classStatus || $classStatus->status != "APPROVED") {
            return response()->json(['error' => true, 'message' => 'Class Is Not Approved Yet']);
        }

        $throttleKey = "otp_throttle_{$contactNo}";
        if (Cache::has($throttleKey)) {
            return response()->json(['error' => true, 'message' => 'Please wait before requesting another OTP.']);
        }

        $otp = rand(100000, 999999);
        $cacheKey = "otp_class_{$contactNo}";
        Cache::put($cacheKey, $otp, now()->addMinutes(5));
        Cache::put($throttleKey, true, now()->addSeconds(60));

        $message = "Hi {$class->firstName}, Please use the code {$otp} to log in to Uest EdTech. For your security, please do not share this code with anyone. Thank you!";
        try {
            Http::get(env('SHREE_TRIPADA_API_URL'), [
                'auth_key'   => env('SHREE_TRIPADA_AUTH_KEY'),
                'mobiles'    => '91' . $contactNo,
                'templateid' => env('SHREE_TRIPADA_TEMPLATE_ID'),
                'message'    => $message,
                'sender'     => env('SHREE_TRIPADA_SENDER'),
                'route'      => '4',
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => true, 'message' => 'Failed to send OTP']);
        }

        return response()->json(['success' => true, 'message' => 'OTP sent']);
    }

    public function verifyOtp(Request $request)
    {
        $contactNo = $request->input('contact_no');
        $otp = $request->input('otp');

        if (!preg_match('/^\d{6}$/', $otp)) {
            return response()->json(['success' => false, 'message' => 'Invalid OTP format']);
        }

        $cachedOtp = Cache::get("otp_class_{$contactNo}");

        if (!$cachedOtp || $cachedOtp != $otp) {
            return response()->json(['success' => false, 'message' => 'Invalid or expired OTP']);
        }

        $classUser = Classes::where('contactNo', $contactNo)->first();
        if (!$classUser) {
            return response()->json(['success' => false, 'message' => 'Class not found']);
        }

        Auth::guard('class_users')->login($classUser);
        $request->session()->put('class_users_id', $classUser->id);
        $request->session()->save();

        Cache::put("otp_verified_class_{$contactNo}", true, now()->addMinutes(10));
        Cache::forget("otp_class_{$contactNo}");

        return response()->json(['success' => true, 'message' => 'Logged in successfully']);
    }
}
