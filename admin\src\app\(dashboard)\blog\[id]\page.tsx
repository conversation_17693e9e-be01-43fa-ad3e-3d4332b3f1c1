"use client";

import React, { useState, useEffect } from 'react';
import { getBlogById, updateBlogStatus } from '@/services/blogApi';
import { Blog } from '@/lib/types';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Loader2, ArrowLeft } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface BlogDetailPageProps {
 params: Promise<{ id: string }>;
}

const BlogDetailPage = ({ params }: BlogDetailPageProps) => {
  const [blog, setBlog] = useState<Blog | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [blogId, setBlogId] = useState<string>('');

  useEffect(() => {
    const getParamsId = async () => {
      const { id } = await params;
      setBlogId(id);
    };

    getParamsId();
  }, [params]);

  useEffect(() => {
    const fetchBlog = async () => {
      if (!blogId) return;

      try {
        setIsLoading(true);
        const data = await getBlogById(blogId);
        setBlog(data);
      } catch  {
        toast.error('Failed to fetch blog');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlog();
  }, [blogId]);

  const handleStatusChange = async (status: 'PENDING' | 'APPROVED' | 'REJECTED') => {
    if (!blog) return;

    try {
      setIsUpdating(true);
      await updateBlogStatus(blog.id, status);
      setBlog({ ...blog, status });
      toast.success(`Blog status updated to ${status}`);
    } catch  {
      toast.error('Failed to update blog status');
    } finally {
      setIsUpdating(false);
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMMM dd, yyyy HH:mm');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'text-green-600 bg-green-100 border-green-600';
      case 'REJECTED':
        return 'text-red-600 bg-red-100 border-red-600';
      default:
        return 'text-yellow-600 bg-yellow-100 border-yellow-600';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!blog) {
    return (
      <div className="container mx-auto py-6 px-4">
        <div className="flex items-center mb-6">
          <Link href="/blog" passHref>
            <Button variant="outline" size="icon" className="mr-2">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Blog Not Found</h1>
        </div>
        <p>The requested blog could not be found.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="flex items-center mb-6">
        <Link href="/blog" passHref>
          <Button variant="outline" size="icon" className="mr-2">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Blog Details</h1>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl">{blog.blogTitle}</CardTitle>
              <CardDescription className="flex flex-col gap-1">
                {blog.class && (
                  <>
                    <span className="text-sm">
                      <span className="font-medium">Author:</span> {blog.class.firstName} {blog.class.lastName}
                    </span>
                    <span className="text-sm">
                      <span className="font-medium">Classes Name:</span> {blog.class.className}
                    </span>
                  </>
                )}
                <span>
                  Created on {formatDate(blog.createdAt)}
                  {blog.createdAt !== blog.updatedAt && ` • Updated on ${formatDate(blog.updatedAt)}`}
                </span>
              </CardDescription>
            </div>
            <div>
              <Select
                defaultValue={blog.status}
                onValueChange={(value) =>
                  handleStatusChange(value as 'PENDING' | 'APPROVED' | 'REJECTED')
                }
                disabled={isUpdating}
              >
                <SelectTrigger className={`w-[130px] ${getStatusColor(blog.status)}`}>
                  <SelectValue placeholder={blog.status} />
                  {isUpdating && <Loader2 className="h-4 w-4 animate-spin ml-2" />}
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PENDING">PENDING</SelectItem>
                  <SelectItem value="APPROVED">APPROVED</SelectItem>
                  <SelectItem value="REJECTED">REJECTED</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="mb-6 flex justify-center">
            <div className="relative max-w-full overflow-hidden rounded-lg">
              {blog.blogImage ? (
                <Image
                  src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${blog.blogImage}`}
                  alt={blog.blogTitle}
                  className="max-h-[400px] object-contain"
                  width={400}
                  height={400}
                  loading="lazy"
                  placeholder="blur"
                  blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEtAJJXIDTjwAAAABJRU5ErkJggg=="
                  onError={(e) => {
                    e.currentTarget.src = '/logo.jpeg';
                  }}
                />
              ) : (
                <div className="h-[200px] w-[300px] bg-gray-200 flex items-center justify-center rounded-md">
                  <span className="text-gray-500">No image available</span>
                </div>
              )}
            </div>
          </div>
          <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: blog.blogDescription }} />
        </CardContent>
      </Card>
    </div>
  );
};

export default BlogDetailPage;
