<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\View;

class IdentifyTenant
{
    public function handle(Request $request, Closure $next)
    {
        $tenantData = [
            'tenantLogo' => asset("images/logo.png"),
            'tenantAddress' => "UEST, Morbi - 363641",
            'tenantName' => "UEST",
            'subdomain' => "uest",
        ];

        View::share($tenantData);
        App::instance('TenantData', $tenantData);


        return $next($request);
    }
}
