'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormLabel,
} from '@/components/ui/form';
import { Loader2, Trash2, Plus } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { ExperienceFormValues } from '@/lib/validations/classesEditSchema';
import { deleteExperienceByAdmin } from '@/services/classesApi';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import AddExperienceForm from './AddExperienceForm';

interface WorkTabProps {
  experienceForm: UseFormReturn<ExperienceFormValues>;
  formData: any;
  isSaving: boolean;
  onSubmit: () => Promise<void>;
  onExperienceUpdate?: () => Promise<void>;
}

const WorkTab: React.FC<WorkTabProps> = ({
  experienceForm,
  formData,
  onSubmit,
  onExperienceUpdate,
}) => {
  const [deletingExperience, setDeletingExperience] = React.useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [experienceToDelete, setExperienceToDelete] = React.useState<any>(null);
  const [isAddExperienceOpen, setIsAddExperienceOpen] = React.useState(false);

  const handleDeleteExperience = async (experience: any) => {
    if (!experience.id) {
      toast.error('Experience ID is required for deletion');
      return;
    }

    try {
      setDeletingExperience(experience.id);
      await deleteExperienceByAdmin(experience.id, formData.id);
      toast.success('Experience record deleted successfully!');

      // Refresh the data
      if (onExperienceUpdate) {
        await onExperienceUpdate();
      } else {
        window.location.reload();
      }
    } catch (error: any) {
      console.error('Delete experience error:', error);
      toast.error(error.message || 'Failed to delete experience record');
    } finally {
      setDeletingExperience(null);
      setIsDeleteDialogOpen(false);
      setExperienceToDelete(null);
    }
  };

  return (
    <Form {...experienceForm}>
      <form onSubmit={experienceForm.handleSubmit(onSubmit)} className="space-y-6">
        <div className="flex justify-between items-center border-b border-gray-200 pb-2">
          <h2 className="text-lg font-semibold text-black">Experience</h2>
          <Button
            type="button"
            variant="outline"
            onClick={() => setIsAddExperienceOpen(true)}
            className="bg-black hover:text-white hover:bg-gray-800 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Experience
          </Button>
        </div>

        {formData.experience.length > 0 ? (
          formData.experience.map((exp: any, index: number) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-md font-medium text-gray-700">Experience {index + 1}</h3>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setExperienceToDelete(exp);
                      setIsDeleteDialogOpen(true);
                    }}
                    disabled={deletingExperience === exp.id}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    {deletingExperience === exp.id ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Trash2 className="w-4 h-4 mr-1" />
                    )}
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                <div>
                  <FormLabel className="text-black font-medium">Is Experience</FormLabel>
                  <div className="p-2 mt-2 bg-gray-50 border border-gray-200 rounded-md">
                    <span className="text-gray-700">{exp.isExperience ? 'Yes' : 'No'}</span>
                  </div>
                </div>

                {exp.isExperience && (
                  <>
                    <div>
                      <FormLabel className="text-black font-medium">Title</FormLabel>
                      <div className="p-2 mt-2 bg-gray-50 border border-gray-200 rounded-md">
                        <span className="text-gray-700">{exp.title || 'Not specified'}</span>
                      </div>
                    </div>

                    <div>
                      <FormLabel className="text-black font-medium">From Date</FormLabel>
                      <div className="p-2 mt-2 bg-gray-50 border border-gray-200 rounded-md">
                        <span className="text-gray-700">{exp.from ? new Date(exp.from).toLocaleDateString() : 'Not specified'}</span>
                      </div>
                    </div>

                    <div>
                      <FormLabel className="text-black font-medium">To Date</FormLabel>
                      <div className="p-2 mt-2 bg-gray-50 border border-gray-200 rounded-md">
                        <span className="text-gray-700">{exp.to ? new Date(exp.to).toLocaleDateString() : 'Not specified'}</span>
                      </div>
                    </div>

                    {/* Certificate Display */}
                    <div className="md:col-span-2">
                      <FormLabel className="text-black font-medium">Certificate</FormLabel>
                      <div className="mt-2">
                        {/* Current Certificate Display */}
                        {exp.certificateUrl ? (
                          <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-600">Current certificate: {exp.certificateUrl}</span>
                              <a
                                href={`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}uploads/classes/${formData.id}/experience/${exp.certificateUrl}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 underline text-sm font-medium"
                              >
                                View Certificate
                              </a>
                            </div>
                          </div>
                        ) : (
                          <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
                            <span className="text-sm text-gray-500">No certificate uploaded</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          ))
        ) : (
          <p className="text-gray-500">No experience records found.</p>
        )}

        {/* Delete Experience Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Experience Record</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this experience record? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsDeleteDialogOpen(false);
                  setExperienceToDelete(null);
                }}
              >
                Cancel
              </Button>
              <Button
                type="button"
                variant="destructive"
                onClick={() => experienceToDelete && handleDeleteExperience(experienceToDelete)}
                disabled={deletingExperience === experienceToDelete?.id}
              >
                {deletingExperience === experienceToDelete?.id ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Add Experience Form */}
        <AddExperienceForm
          classId={formData.id}
          isOpen={isAddExperienceOpen}
          onClose={() => setIsAddExperienceOpen(false)}
          onSuccess={async () => {
            if (onExperienceUpdate) {
              await onExperienceUpdate();
            } else {
              window.location.reload();
            }
          }}
        />
      </form>
    </Form>
  );
};

export default WorkTab;
