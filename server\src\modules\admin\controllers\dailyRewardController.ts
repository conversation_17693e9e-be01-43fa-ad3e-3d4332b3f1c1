import { Request, Response } from 'express';
import { sendSuccess, sendError } from '@/utils/response';
import { distributeDailyRewards, checkTodayRewardsDistributed } from '@/services/dailyLeaderboardRewardService';
import { dailyRewardCron } from '@/jobs/dailyRewardCron';

export const manualDistributeRewards = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('🔧 Manual reward distribution triggered by admin');
    
    const result = await distributeDailyRewards();
    
    if (result.success) {
      sendSuccess(res, result.details, result.message);
    } else {
      sendError(res, result.message, 400);
    }
  } catch (error: any) {
    console.error('Error in manual reward distribution:', error);
    sendError(res, error.message || 'Failed to distribute rewards', 500);
  }
};

export const checkRewardStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const alreadyDistributed = await checkTodayRewardsDistributed();
    const cronStatus = dailyRewardCron.getStatus();
    
    sendSuccess(res, {
      todayRewardsDistributed: alreadyDistributed,
      cronJob: cronStatus
    }, 'Reward status retrieved successfully');
  } catch (error: any) {
    console.error('Error checking reward status:', error);
    sendError(res, error.message || 'Failed to check reward status', 500);
  }
};

export const getCronJobStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const status = dailyRewardCron.getStatus();
    sendSuccess(res, status, 'Cron job status retrieved successfully');
  } catch (error: any) {
    console.error('Error getting cron job status:', error);
    sendError(res, error.message || 'Failed to get cron job status', 500);
  }
};
