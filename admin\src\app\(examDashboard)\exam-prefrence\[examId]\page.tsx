'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import { Ta<PERSON>, <PERSON>bsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import SubjectPreferenceManager from '@/components/SubjectPreferenceManager'; 
import LevelPreferenceManager from '@/components/LevelPrenceManager'; 

export default function PreferenceManager() {
  const { examId } = useParams();
  const [activeTab, setActiveTab] = useState('subject');

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Preferences </h1>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="subject">Subject Preferences</TabsTrigger>
          <TabsTrigger value="level">Level Preferences</TabsTrigger>
        </TabsList>
        <TabsContent value="subject">
          <SubjectPreferenceManager examId={examId as string} />
        </TabsContent>
        <TabsContent value="level">
          <LevelPreferenceManager examId={examId as string} />
        </TabsContent>
      </Tabs>
    </div>
  );
}