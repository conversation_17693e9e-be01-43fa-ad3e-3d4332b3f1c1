import axiosInstance from '../lib/axios';

export const getReviews = async (page: number = 1, limit: number = 10) => {
  try {
    const response = await axiosInstance.get(`/reviews?page=${page}&limit=${limit}`);
    return response.data;
  } catch (error: any) {
    throw new Error(`Failed to fetch reviews: ${error.message}`);
  }
};

export const deleteReview = async (id: string) => {
  try {
    const response = await axiosInstance.delete(`/reviews/admin/${id}`);
    return response.data;
  } catch (error: any) {
    throw new Error(`Failed to delete review: ${error.message}`);
  }
};