<?php

namespace Batches\Models;

use Classroom\Models\Classroom;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Resources\Models\Resource;

class Batches extends Model
{
    use HasFactory;

    public $table = 'batches';
    protected $fillable = [
        'batch_name',
        'resource_id',
        'year_id',
        'class_uuid',
    ];

    public function batchSubjects()
    {
        return $this->hasMany(BatchesSubjects::class, 'batch_id')->with('subject');;
    }

    public function days()
    {
        return $this->hasMany(BatchesDays::class, 'batch_id');
    }

    public function batchTimeslots()
    {
        return $this->hasMany(BatchesTimeslots::class, 'batch_id')->with('timeslot');;
    }

    public function batchClassroom()
    {
        return $this->hasMany(BatchesClassroom::class, 'batch_id')->with('classroom');
    }

    public function resource()
    {
        return $this->belongsTo(Resource::class, 'resource_id');
    }
}
