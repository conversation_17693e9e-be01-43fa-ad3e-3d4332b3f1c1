<?php

namespace Admission\Models;

use App\Models\Classes;
use Classroom\Models\Classroom;
use Department\Models\Department;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Years\Models\Years;

class StudentAcademicInfo extends Model
{
    use HasFactory, SoftDeletes;
    public $table = 'student_academic_info';

    protected $fillable = [
        'class_uuid',
        'student_id',
        'department',
        'classroom',
        'year',
        'status',
        'joining_date'
    ];

    protected $casts = [
        'student_id' => 'string',
    ];

    public function getDepartment()
    {
        return $this->belongsTo(Department::class, 'department', 'id')->select('name', 'id');
    }

    public function getClassroom()
    {
        return $this->belongsTo(Classroom::class, 'classroom', 'id')->select('class_name', 'id');
    }

    public function getYear()
    {
        return $this->belongsTo(Years::class, 'year', 'id');
    }

    public function getStudent()
    {
        return $this->belongsTo(Student::class, 'student_id', 'id');
    }

    public function getStudentProfile()
    {
        return $this->belongsTo(StudentDetails::class, 'student_id', 'studentId');
    }

    public function getClass()
    {
        return $this->belongsTo(Classes::class, 'class_uuid', 'id');
    }
}
