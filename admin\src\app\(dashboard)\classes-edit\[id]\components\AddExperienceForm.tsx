'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Loader2, Plus, X } from 'lucide-react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { addExperienceByAdmin } from '@/services/classesApi';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

// Zod schema for experience validation
const experienceSchema = z.object({
  noExperience: z.boolean(),
  experiences: z.array(z.object({
    title: z.string(),
    from: z.string(),
    to: z.string(),
    certificate: z.any().optional(),
  })).optional(),
}).superRefine((data, ctx) => {
  // If noExperience is true, skip all validation
  if (data.noExperience) {
    return;
  }

  // If noExperience is false, validate experiences array
  if (!data.experiences || data.experiences.length === 0) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Please add at least one experience record or check "I don\'t have work experience"',
      path: ['experiences'],
    });
    return;
  }

  // Validate each experience record
  data.experiences.forEach((exp, index) => {
    if (!exp.title || exp.title.trim().length < 2) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Job title is required',
        path: ['experiences', index, 'title'],
      });
    }
    if (!exp.from || exp.from.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Start date is required',
        path: ['experiences', index, 'from'],
      });
    }
    if (!exp.to || exp.to.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date is required',
        path: ['experiences', index, 'to'],
      });
    }
    if (!exp.certificate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Certificate file is required',
        path: ['experiences', index, 'certificate'],
      });
    }
  });
});

type ExperienceFormData = z.infer<typeof experienceSchema>;

interface AddExperienceFormProps {
  classId: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const AddExperienceForm: React.FC<AddExperienceFormProps> = ({
  classId,
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const form = useForm<ExperienceFormData>({
    resolver: zodResolver(experienceSchema),
    defaultValues: {
      noExperience: false,
      experiences: [
        {
          title: '',
          from: '',
          to: '',
          certificate: undefined,
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'experiences',
  });

  const watchNoExperience = form.watch('noExperience');

  const handleNoExperienceChange = async (checked: boolean) => {
    form.setValue('noExperience', checked);

    if (checked) {
      try {
        setIsSubmitting(true);
        await addExperienceByAdmin(classId, { noExperience: true }, []);
        toast.success('No experience status added successfully!');
        onSuccess();
        handleClose();
      } catch (error: any) {
        toast.error(error.message || 'Failed to add experience');
      } finally {
        setIsSubmitting(false);
      }
    }
  };



  const onSubmit = async (data: ExperienceFormData) => {
    if (data.noExperience) {
      return;
    }

    setIsSubmitting(true);

    try {
      const certificateFiles: File[] = [];
      data.experiences?.forEach((exp: any) => {
        if (exp.certificate?.[0]) {
          certificateFiles.push(exp.certificate[0]);
        }
      });

      await addExperienceByAdmin(classId, { noExperience: false, experiences: data.experiences }, certificateFiles);

      onSuccess();
      handleClose();
      toast.success('Experience records added successfully!');
    } catch (error: any) {
      console.error('Add experience error:', error);
      toast.error(error.message || 'Failed to add experience');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Experience Record</DialogTitle>
          <DialogDescription>
            Add work experience details for this class.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form  className="space-y-6">
            {/* No Experience Checkbox */}
            <div className="flex items-start space-x-3">
              <input
                type="checkbox"
                id="noExperience"
                checked={watchNoExperience}
                onChange={(e) => handleNoExperienceChange(e.target.checked)}
                className="mt-1"
              />
              <label htmlFor="noExperience" className="text-sm font-medium">
                I don&apos;t have work experience
              </label>
            </div>

            {/* Experience Records */}
            {!watchNoExperience && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Experience Records</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      append({
                        title: '',
                        from: '',
                        to: '',
                        certificate: undefined,
                      })
                    }
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Experience
                  </Button>
                </div>

                {fields.map((field, index) => (
                  <div key={field.id} className="border border-gray-200 rounded-lg p-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Experience {index + 1}</h4>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => remove(index)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`experiences.${index}.title`}
                        render={({ field }) => (
                          <FormItem className="md:col-span-2">
                            <FormLabel>Job Title</FormLabel>
                            <FormControl>
                              <input
                                {...field}
                                placeholder="Enter job title"
                                className="flex h-9 w-full rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-black disabled:cursor-not-allowed disabled:opacity-50"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`experiences.${index}.from`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>From Date</FormLabel>
                            <FormControl>
                              <input
                                {...field}
                                type="date"
                                className="flex h-9 w-full rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-black disabled:cursor-not-allowed disabled:opacity-50"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`experiences.${index}.to`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>To Date</FormLabel>
                            <FormControl>
                              <input
                                {...field}
                                type="date"
                                className="flex h-9 w-full rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-black disabled:cursor-not-allowed disabled:opacity-50"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`experiences.${index}.certificate`}
                        render={({ field: { onChange, name } }) => (
                          <FormItem className="md:col-span-2">
                            <FormLabel>Certificate</FormLabel>
                            <FormControl>
                              <input
                                name={name}
                                type="file"
                                accept=".pdf,.jpg,.jpeg,.png"
                                onChange={(e) => onChange(e.target.files)}
                                className="flex h-9 w-full rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-black disabled:cursor-not-allowed disabled:opacity-50"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                ))}
            </div>
          )}

          <DialogFooter className="gap-2">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-black hover:bg-gray-800 text-white disabled:bg-gray-400"
              onClick={form.handleSubmit(onSubmit)}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Adding...
                </>
              ) : (
                'Add Experience'
              )}
            </Button>
          </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddExperienceForm;
