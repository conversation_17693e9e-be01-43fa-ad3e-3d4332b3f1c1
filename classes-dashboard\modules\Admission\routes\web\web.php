<?php

use Admission\Http\Controllers\AdmissionController;
use Illuminate\Support\Facades\Route;

Route::middleware(['web', 'auth'])->group(function () {
    Route::resource('student', AdmissionController::class);
    Route::post('/create-students-details', [AdmissionController::class, 'storeStudentDetails'])->name('storeStudentDetails');
    Route::post('/create-students-parents-details', [AdmissionController::class, 'storeStudentParentsDetails'])->name('storeStudentParentsDetails');
    Route::post('/export-student', [AdmissionController::class, 'exportStudents'])->name('export-student');

    Route::get('/search-student', [AdmissionController::class, 'searchStudent'])->name('searchStudent');
    Route::post('/leaving-certificare/{student_id}', [AdmissionController::class, 'generateLeavingCertificate'])->name('generateLC');
    Route::get('/student-active-inactive/{student_id}', [AdmissionController::class, 'activeInactive'])->name('student.activeInactive');
    Route::get('/get-students-by-classroom', [AdmissionController::class, 'getStudentsByClassroom'])->name('getStudentsByClassroom');

    Route::post('/check-student-by-mobile', [AdmissionController::class, 'checkByMobile'])->name('checkStudentByMobile');
    Route::post('/verify-otp', [AdmissionController::class, 'verifyOtp'])->name('verifyOtp');
});
