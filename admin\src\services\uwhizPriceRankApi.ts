import axiosInstance from "../lib/axios";

export const createPriceRank = async (data: {
  examId: number;
  rank: number;
  price: number;
}) => {
  try {
    const response = await axiosInstance.post("/uwhizPriceRank", data,{
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.error ||
      error.response?.data?.message ||
      error.message ||
      "Failed to create price rank";
    throw new Error(errorMessage);
  }
};

export const getPriceRanksByExamId = async (examId: number) => {
  try {
    const response = await axiosInstance.get(`/uwhizPriceRank/${examId}`,{
      headers:{
        "Server-Select": "uwhizServer",
      }
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch question bank: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

export const updatePriceRank = async (
  id: string,
  data: Partial<{ rank: number; price: number }>
) => {
  try {
    const response = await axiosInstance.put(`/uwhizPriceRank/${id}`, data,{
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch question bank: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

export const deletePriceRank = async (id: string) => {
  try {
    await axiosInstance.delete(`/uwhizPriceRank/${id}`,{
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch question bank: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};
