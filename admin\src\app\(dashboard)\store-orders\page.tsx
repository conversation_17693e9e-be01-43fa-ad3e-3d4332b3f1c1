'use client';

import React, { useState, useEffect } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { Package, Search, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/app-components/dataTable';
import Pagination from '@/app-components/pagination';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import * as storeOrderApi from '@/services/storeOrderApi';
import { StoreOrder, StoreOrderFilters, StoreOrderPaginationResponse, StoreOrderStats } from '@/lib/types';

const StoreOrdersPage = () => {
  const [orders, setOrders] = useState<StoreOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [modelTypeFilter, setModelTypeFilter] = useState('all');
  const [stats, setStats] = useState<StoreOrderStats | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const limit = 10;

  useEffect(() => {
    loadStoreOrders();
    loadOrderStats();
  }, []);

  useEffect(() => {
    loadStoreOrders(1);
  }, [statusFilter, searchQuery, modelTypeFilter]);

  const loadStoreOrders = async (page: number = currentPage) => {
    try {
      setLoading(true);
      const filters: StoreOrderFilters = {};
      if (statusFilter && statusFilter !== 'all') filters.status = statusFilter;
      if (searchQuery) filters.search = searchQuery;
      if (modelTypeFilter && modelTypeFilter !== 'all') filters.modelType = modelTypeFilter;

      const response = await storeOrderApi.getAllStoreOrders(page, limit, filters);

      if ('success' in response && response.success === false) {
        toast.error(response.error);
        setOrders([]);
        setTotalCount(0);
        setTotalPages(1);
        return;
      }

      const ordersData = response as StoreOrderPaginationResponse;
      setOrders(ordersData.orders || []);
      setTotalCount(ordersData.pagination.totalCount);
      setTotalPages(ordersData.pagination.totalPages);
      setCurrentPage(ordersData.pagination.page);

      toast.success(`Loaded ${ordersData.orders?.length || 0} orders`);
    } catch (error: any) {
      setOrders([]);
      setTotalCount(0);
      setTotalPages(1);
      toast.error('Failed to load store orders: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const loadOrderStats = async () => {
    try {
      const statsData = await storeOrderApi.getStoreOrderStats();
      setStats(statsData);
    } catch (error: any) {
      console.error('Error loading order stats:', error);
    }
  };

  const updateOrderStatus = async (orderId: string, newStatus: string) => {
    try {
      const result = await storeOrderApi.updateStoreOrderStatus(orderId, newStatus);
      if (result.success === false) {
        toast.error(result.error || 'Failed to update order status');
        return;
      }

      let message = `Order status updated to ${newStatus}`;
      if (newStatus === 'COMPLETED') {
        message += '. Stock deducted from inventory.';
      } else if (newStatus === 'CANCELLED') {
        message += '. Coins refunded to customer.';
      }

      toast.success(message);
      loadStoreOrders();
      loadOrderStats();
    } catch (error: any) {
      toast.error('Failed to update order status: ' + error.message);
    }
  };

  const orderColumns: ColumnDef<StoreOrder>[] = [
    {
      accessorKey: 'id',
      header: 'Order ID',
      cell: ({ row }) => (
        <span className="font-mono text-sm">#{row.original.id.slice(-8)}</span>
      ),
    },
    {
      accessorKey: 'buyerName',
      header: 'Buyer',
      cell: ({ row }) => (
        <div className="space-y-1">
          <div className="font-medium text-sm">{row.original.buyerName || 'Loading...'}</div>
          {row.original.buyerEmail && (
            <div className="text-xs text-muted-foreground">{row.original.buyerEmail}</div>
          )}
          <div className="text-xs text-muted-foreground font-mono">
            <span className={`px-2 py-1 rounded text-xs font-medium ${
              row.original.modelType === 'STUDENT' ? 'bg-blue-100 text-blue-800' :
              row.original.modelType === 'CLASS' ? 'bg-green-100 text-green-800' :
              'bg-purple-100 text-purple-800'
            }`}>
              {row.original.modelType}
            </span>
            <span className="ml-2">ID: {row.original.modelId.slice(-8)}</span>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'itemName',
      header: 'Item',
      cell: ({ row }) => (
        <div className="space-y-1">
          <div className="font-medium text-sm">{row.original.itemName}</div>
          <div className="text-xs text-muted-foreground">
            ₹{row.original.itemPrice} × {row.original.quantity} = ₹{row.original.totalCoins}
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'totalCoins',
      header: 'Total Coins',
      cell: ({ row }) => (
        <div className="text-orange-600 flex items-center gap-1">
          <Package className="w-3 h-3" />
          <span className="font-medium">{row.original.totalCoins} coins</span>
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <Badge variant={
          row.original.status === 'COMPLETED' ? 'default' :
          row.original.status === 'PENDING' ? 'secondary' : 'destructive'
        }>
          {row.original.status}
        </Badge>
      ),
    },
    {
      accessorKey: 'createdAt',
      header: 'Order Date',
      cell: ({ row }) => (
        <span className="text-sm">
          {new Date(row.original.createdAt).toLocaleDateString()}
        </span>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Select
            value={row.original.status}
            onValueChange={(newStatus) => updateOrderStatus(row.original.id, newStatus)}
          >
            <SelectTrigger className="w-36">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="PENDING">
                <div className="flex items-center gap-2">
                  Pending
                </div>
              </SelectItem>
              <SelectItem value="COMPLETED">
                <div className="flex items-center gap-2">
                  Completed
                </div>
              </SelectItem>
              <SelectItem value="CANCELLED">
                <div className="flex items-center gap-2">
                  Cancelled
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      ),
    },
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Store Orders</h1>
          <p className="text-muted-foreground">View and manage customer orders</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => loadStoreOrders()} disabled={loading} variant="outline">
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Loading...' : 'Refresh Orders'}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalOrders}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.completedOrders}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{stats.pendingOrders}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{stats.totalRevenue} coins</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search by order ID, student name, email, or item name..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="PENDING">Pending</SelectItem>
            <SelectItem value="COMPLETED">Completed</SelectItem>
            <SelectItem value="CANCELLED">Cancelled</SelectItem>
          </SelectContent>
        </Select>
        <Select value={modelTypeFilter} onValueChange={setModelTypeFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by user type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Users</SelectItem>
            <SelectItem value="STUDENT">Students</SelectItem>
            <SelectItem value="CLASS">Classes</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle>Orders ({totalCount})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-muted-foreground">Loading orders...</div>
            </div>
          ) : orders.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <Package className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium text-muted-foreground mb-2">No orders found</h3>
                <p className="text-sm text-muted-foreground">
                  {orders.length === 0 ? 'No orders have been placed yet.' : 'No orders match your current filters.'}
                </p>
              </div>
            </div>
          ) : (
            <>
              <DataTable
                columns={orderColumns}
                data={orders}
                isLoading={loading}
              />
            </>
          )}
        </CardContent>
      </Card>

      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={(page) => {
          setCurrentPage(page);
          loadStoreOrders(page);
        }}
        entriesText={`${totalCount} entries`}
      />
    </div>
  );
};

export default StoreOrdersPage;
