import { axiosInstance } from '@/lib/axios';
import { CreateStoreItemData, StoreFilters, StoreItem, StoreStats, UpdateStoreItemData } from '@/lib/types';

export const getAllStoreItems = async (filters?: StoreFilters): Promise<StoreItem[]> => {
  try {
    const params = new URLSearchParams();
    if (filters?.category) params.append('category', filters.category);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.search) params.append('search', filters.search);

    const response = await axiosInstance.get(`/admin/store?${params.toString()}`);
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch store items');
  }
};

export const getStoreItemById = async (id: string): Promise<StoreItem> => {
  try {
    const response = await axiosInstance.get(`/admin/store/${id}`);
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch store item');
  }
};

export const createStoreItem = async (data: CreateStoreItemData | FormData): Promise<StoreItem> => {
  try {
    const config = data instanceof FormData ? {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    } : {};

    const response = await axiosInstance.post('/admin/store', data, config);
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to create store item');
  }
};

export const updateStoreItem = async (id: string, data: UpdateStoreItemData | FormData): Promise<StoreItem> => {
  try {
    const config = data instanceof FormData ? {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    } : {};

    const response = await axiosInstance.put(`/admin/store/${id}`, data, config);
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to update store item');
  }
};

export const deleteStoreItem = async (id: string): Promise<void> => {
  try {
    await axiosInstance.delete(`/admin/store/${id}`);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to delete store item');
  }
};

export const getStoreStats = async (): Promise<StoreStats> => {
  try {
    const response = await axiosInstance.get('/admin/store/stats');
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch store statistics');
  }
};
