<?php

namespace App\Http\APIControllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Admission\Repositories\AdmissionRepository;
use Circulars\Models\Circulars;
use Fees\Repositories\StudentFeePaymentRepository;
use StudentAttendance\Repositories\AttendanceRepository;
use ClassWork\Repositories\ClassWorkRepository;
use Document\Models\Document;
use HomeWork\Repositories\HomeWorkRepository;

class StudentAPIController extends Controller
{
    protected $admissionRepository;
    protected $studentFeePaymentRepository;
    protected $attendanceRepository;
    protected $classWorkRepository;
    protected $homeWorkRepository;

    public function __construct(
        AdmissionRepository $admissionRepository,
        StudentFeePaymentRepository $studentFeePaymentRepository,
        AttendanceRepository $attendanceRepository,
        ClassWorkRepository $classWorkRepository,
        HomeWorkRepository $homeWorkRepository
    ) {
        $this->admissionRepository = $admissionRepository;
        $this->studentFeePaymentRepository = $studentFeePaymentRepository;
        $this->attendanceRepository = $attendanceRepository;
        $this->classWorkRepository = $classWorkRepository;
        $this->homeWorkRepository = $homeWorkRepository;
    }

    public function getStudentDetails(Request $request)
    {
        $student = $request->get('student');
        return response()->json(apiResonse("Student Details Fetched Successfully", $student));
    }

    public function logout(Request $request)
    {
        return response()->json(apiResonse("Successfully logged out", ''));
    }

    public function getTimetable(Request $request)
    {
        $request->validate(['date' => 'required|date']);

        $academicInfo = $request->get('academic_info');
        $date = $request->get('date');

        $timetable = [];

        return response()->json(apiResonse("Timetable Fetched Successfully", $timetable));
    }

    public function getAttendance(Request $request)
    {
        $studentId = $request->get('academic_info')->id;
        $filters = $request->all();

        $attendance = $this->attendanceRepository
            ->getAttendanceForAPI($filters, $studentId);

        return response()->json(apiResonse("Attendance Fetched Successfully", $attendance));
    }

    public function getDisciplineIssue(Request $request)
    {
        $studentId = $request->get('academic_info')->id;
        $filters = $request->all();

        $issues = $this->attendanceRepository
            ->getDisciplineIssue($filters, $studentId);

        return response()->json(apiResonse("Discipline Issue Fetched Successfully", $issues));
    }

    public function getClassWork(Request $request)
    {
        $request->validate(['date' => 'required|date']);
        $academicInfo = $request->get('academic_info');

        $classwork = $this->classWorkRepository
            ->getClassWorkByClassroom($academicInfo->classroom, $request->date);

        return response()->json(apiResonse("Classwork Fetched Successfully", $classwork));
    }

    public function getHomeWork(Request $request)
    {
        $request->validate(['date' => 'required|date']);
        $academicInfo = $request->get('academic_info');

        $homework = $this->homeWorkRepository
            ->getHomeWorkByClassroom($academicInfo->classroom, $request->date);

        return response()->json(apiResonse("Homework Fetched Successfully", $homework));
    }

    public function getCirculars(Request $request)
    {
        $academicInfo = $request->get('academic_info');

        $circulars = Circulars::where('class_uuid', $academicInfo->class_uuid)->orderBy('id', 'desc')->get();

        return response()->json(apiResonse("Circulars Fetched Successfully", $circulars));
    }

    public function checkIfStudentInClass(Request $request)
    {
        $academicInfo = $request->get('academic_info');

        if (!$academicInfo) {
            return response()->json(apiErrorResonse("Students Not Found In class", []), 404);
        }

        return response()->json(apiResonse("Students Fetched Successfully", $academicInfo));
    }

    public function getDocuments(Request $request)
    {
        $studentId = $request->get('academic_info')->id;

        $documents = Document::with('category')->where('student_id', $studentId)->orderBy('id', 'desc')->get();

        return response()->json(apiResonse("Documents Fetched Successfully", $documents));
    }
}