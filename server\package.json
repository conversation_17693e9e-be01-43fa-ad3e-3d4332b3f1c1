{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev"}, "prisma": {"seed": "ts-node prisma/questionBankTimeStampSeed.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.7.0", "@types/xlsx": "^0.0.35", "axios": "^1.9.0", "cookie-parse": "^0.4.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^3.1.7", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "http-errors": "^2.0.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "nodemon": "^3.1.10", "prisma": "^6.7.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.4"}, "devDependencies": {"@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/http-errors": "^2.0.4", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.17", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "eslint": "^9.26.0", "prettier": "^3.5.3", "tsconfig-paths": "^4.2.0"}}