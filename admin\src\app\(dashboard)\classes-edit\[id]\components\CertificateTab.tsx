'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormLabel,
} from '@/components/ui/form';
import { Loader2, Trash2, Plus } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { CertificateFormValues } from '@/lib/validations/classesEditSchema';
import { deleteCertificateByAdmin } from '@/services/classesApi';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import AddCertificateForm from './AddCertificateForm';

interface CertificateTabProps {
  certificateForm: UseFormReturn<CertificateFormValues>;
  formData: any;
  isSaving: boolean;
  onSubmit: () => Promise<void>;
  onCertificateUpdate?: () => Promise<void>;
}

const CertificateTab: React.FC<CertificateTabProps> = ({
  certificateForm,
  formData,
  onSubmit,
  onCertificateUpdate,
}) => {
  const [deletingCertificate, setDeletingCertificate] = React.useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [certificateToDelete, setCertificateToDelete] = React.useState<any>(null);
  const [isAddCertificateOpen, setIsAddCertificateOpen] = React.useState(false);

  const handleDeleteCertificate = async (certificate: any) => {
    if (!certificate.id) {
      toast.error('Certificate ID is required for deletion');
      return;
    }

    try {
      setDeletingCertificate(certificate.id);
      await deleteCertificateByAdmin(certificate.id, formData.id);
      toast.success('Certificate record deleted successfully!');

      // Refresh the data
      if (onCertificateUpdate) {
        await onCertificateUpdate();
      } else {
        window.location.reload();
      }
    } catch (error: any) {
      console.error('Delete certificate error:', error);
      toast.error(error.message || 'Failed to delete certificate record');
    } finally {
      setDeletingCertificate(null);
      setIsDeleteDialogOpen(false);
      setCertificateToDelete(null);
    }
  };

  return (
    <Form {...certificateForm}>
      <form onSubmit={certificateForm.handleSubmit(onSubmit)} className="space-y-6">
        <div className="flex justify-between items-center border-b border-gray-200 pb-2">
          <h2 className="text-lg font-semibold text-black">Certificates</h2>
          <Button
            type="button"
            variant="outline"
            onClick={() => setIsAddCertificateOpen(true)}
            className="bg-black hover:bg-gray-800 hover:text-white text-white "
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Certificate
          </Button>
        </div>

        {formData.certificates.length > 0 ? (
          formData.certificates.map((cert: any, index: number) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-md font-medium text-gray-700">Certificate {index + 1}</h3>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setCertificateToDelete(cert);
                      setIsDeleteDialogOpen(true);
                    }}
                    disabled={deletingCertificate === cert.id}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    {deletingCertificate === cert.id ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Trash2 className="w-4 h-4 mr-1" />
                    )}
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                <div>
                  <FormLabel className="text-black font-medium">Is Certificate</FormLabel>
                  <div className="p-2 mt-2 bg-gray-50 border border-gray-200 rounded-md">
                    <span className="text-gray-700">{cert.isCertificate ? 'Yes' : 'No'}</span>
                  </div>
                </div>

                {cert.isCertificate && (
                  <>
                    <div>
                      <FormLabel className="text-black font-medium">Title</FormLabel>
                      <div className="p-2 mt-2 bg-gray-50 border border-gray-200 rounded-md">
                        <span className="text-gray-700">{cert.title || 'Not specified'}</span>
                      </div>
                    </div>

                    {/* Certificate Display */}
                    <div className="md:col-span-2">
                      <FormLabel className="text-black font-medium">Certificate File</FormLabel>
                      <div className="mt-2">
                        {/* Current Certificate Display */}
                        {cert.certificateUrl ? (
                          <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-600">Current certificate: {cert.certificateUrl}</span>
                              <a
                                href={`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}uploads/classes/${formData.id}/certificates/${cert.certificateUrl}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 underline text-sm font-medium"
                              >
                                View Certificate
                              </a>
                            </div>
                          </div>
                        ) : (
                          <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
                            <span className="text-sm text-gray-500">No certificate uploaded</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          ))
        ) : (
          <p className="text-gray-500">No certificate records found.</p>
        )}


        {/* Delete Certificate Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Certificate Record</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this certificate record? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsDeleteDialogOpen(false);
                  setCertificateToDelete(null);
                }}
              >
                Cancel
              </Button>
              <Button
                type="button"
                variant="destructive"
                onClick={() => certificateToDelete && handleDeleteCertificate(certificateToDelete)}
                disabled={deletingCertificate === certificateToDelete?.id}
              >
                {deletingCertificate === certificateToDelete?.id ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Add Certificate Form */}
        <AddCertificateForm
          classId={formData.id}
          isOpen={isAddCertificateOpen}
          onClose={() => setIsAddCertificateOpen(false)}
          onSuccess={async () => {
            if (onCertificateUpdate) {
              await onCertificateUpdate();
            } else {
              window.location.reload();
            }
          }}
        />
      </form>
    </Form>
  );
};

export default CertificateTab;