const { <PERSON>ronJob } = require('cron');
import { distributeDailyRewards, checkTodayRewardsDistributed } from '../services/dailyLeaderboardRewardService';

class DailyRewardCronJob {
  private job: any = null;

  constructor() {
    this.initializeCronJob();
  }

  private initializeCronJob() {
    // Run every day at 12:00 PM (noon)
    // Cron pattern: '0 12 * * *' means "at 12:00 PM every day"
    // Format: second minute hour day month dayOfWeek
    this.job = new CronJob(
      '0 12 * * *', // At 12:00 PM every day
      this.executeRewardDistribution.bind(this),
      null, // onComplete callback
      false, // start immediately
      'Asia/Kolkata' // timezone (adjust as needed)
    );

    console.log('📅 Daily reward cron job initialized - will run at 12:00 PM every day');
  }

  private async executeRewardDistribution() {
    try {
      console.log('⏰ Daily reward cron job triggered at:', new Date().toISOString());

      // Check if rewards have already been distributed today
      const alreadyDistributed = await checkTodayRewardsDistributed();
      
      if (alreadyDistributed) {
        console.log('ℹ️ Daily rewards have already been distributed today, skipping...');
        return;
      }

      // Distribute rewards to top 3 daily leaderboard winners
      const result = await distributeDailyRewards();

      if (result.success) {
        console.log('✅ Daily reward distribution successful:', result.message);
        if (result.details?.winners) {
          console.log('🏆 Rewards distributed to:', result.details.winners.map((w: any) => 
            `Rank ${w.rank}: ${w.studentName} (${w.studentId})`
          ));
        }
        if (result.details?.errors && result.details.errors.length > 0) {
          console.log('⚠️ Some errors occurred:', result.details.errors);
        }
      } else {
        console.error('❌ Daily reward distribution failed:', result.message);
      }

    } catch (error: any) {
      console.error('💥 Critical error in daily reward cron job:', error);
    }
  }

  public start() {
    if (this.job) {
      this.job.start();
      console.log('🚀 Daily reward cron job started');
    }
  }

  public stop() {
    if (this.job) {
      this.job.stop();
      console.log('🛑 Daily reward cron job stopped');
    }
  }

  public getStatus() {
    return {
      isRunning: this.job?.running || false,
      nextRun: this.job?.nextDate()?.toISOString() || null,
      cronPattern: '0 12 * * *',
      timezone: 'Asia/Kolkata'
    };
  }

  // Method to manually trigger reward distribution (for testing)
  public async manualTrigger() {
    console.log('🔧 Manual trigger for daily reward distribution...');
    await this.executeRewardDistribution();
  }
}

// Export singleton instance
export const dailyRewardCron = new DailyRewardCronJob();
