import { axiosInstance } from '@/lib/axios';
import { Thought } from '@/lib/types';

export interface ThoughtPaginationResponse {
  thoughts: Thought[];
  total: number;
  pages: number;
  currentPage: number;
}

// Get all thoughts
export const getThought = async (
  status?: 'PENDING' | 'APPROVED' | 'REJECTED',
  classId?: string,
  page: number = 1,
  limit: number = 10
): Promise<ThoughtPaginationResponse> => {
  try {
    const response = await axiosInstance.get('/classes-thought/admin/', {
      params: {
        status: status ? status : undefined,
        classId: classId ? classId : undefined,
        page,
        limit
      },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to fetch thoughts: ${error.message}`);
  }
};

// Update thought status
export const updateThoughtStatus = async (
    id: string,
    status: 'PENDING' | 'APPROVED' | 'REJECTED'
  ): Promise<Thought> => {
    try {
      const response = await axiosInstance.patch(`/classes-thought/${id}/status`, { status });
      return response.data;
    } catch (error: any) {
      throw new Error(`Failed to update thought status: ${error.message}`);
    }
  };

export const deleteThought = async (id: string): Promise<void> => {
  try {
    await axiosInstance.delete(`/classes-thought/admin/${id}`);
  } catch (error: any) {
    throw new Error(`Failed to delete thought: ${error.message}`);
  }
};