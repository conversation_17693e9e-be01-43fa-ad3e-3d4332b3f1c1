$("#submit-student-details").click(function() {
    studentDetailsSubmit('#student-details-forms', admissionCreateRoute.storeStudentDetails)
});


function studentDetailsSubmit(formid, url) {
    event.preventDefault();
    let form = $(formid);
    if (form.valid()) {
        let formData = new FormData();
        let serializedForm = form.serializeArray();
        $.each(serializedForm, function(index, obj){
            formData.append(obj.name, obj.value);
        });
        let fileInput = form.find('input[type="file"]');
        if (fileInput.length > 0) {
            let file = fileInput[0].files[0];
            formData.append('photo', file);
        }

        $.ajax({
            url: url,
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            beforeSend: function() {
                $('.page-loader').show();
            },
            success: function (result) {
                if(result.success) {
                    $("html, body").animate({ scrollTop: 0 }, "slow");
                    stepper.next();
                    toastr.success(result.success);
                    if (formid == "#student-details-forms") {
                        $('[name="student_id"]').val(result.id);
                    }
                } else {
                    toastr.error(result.error);
                }
            },
            complete: function() {
                $('.page-loader').hide();
            },
            error: err => {
                if (err.status === 422) {
                    showErrors(err.responseJSON.errors);
                }
                toastr.error('Something went wrong!');
                showLoader();
                return false;
            }
        });
    }
}


$(document).ready(function () {
    let url = window.location.href;
    if (url.indexOf("enq") !== -1) {
        let params = $.extend({}, doAjax_params_default);
        let queryString = url.split('?')[1];
        let enqValue = queryString.split('enq=')[1];
        params["requestType"] = `GET`;
        params["url"] =     params["url"] = window.location.origin + checkHost() + '/enquiry/' + enqValue;;
        params["successCallbackFunction"] = function successCallbackFunction(
            result
        ) {
            $('#first_name').val(result.student_first_name);
            $('#middle_name').val(result.student_middle_name);
            $('#last_name').val(result.student_last_name);
            $('#contact_no_1').val(result.contact_number);
            $('#contact_no_1').val(result.contact_number);
            $('#fathers_name').val(result.student_middle_name);
            $('#fathers_last_name').val(result.student_last_name);
            $('#department').val(result.department).trigger('change');
            setTimeout(function(){
                $('#classroom').val(result.classroom).trigger('change');
            },1000)
            $('#email').val(result.email);
            $('#student-details-forms').append("<input type='hidden' name='enquiry' value="+ enqValue +">");
        };
        commonAjax(params);
    }
});

$('#date_of_birth').change(function() {
    var age = moment().diff(moment($(this).val(), "YYYY-MM-DD"), 'years');
    $("#age").val(age);
});

$("#date_of_birth").datepicker({
    dateFormat: "yy-mm-dd",
    changeMonth: true,
    changeYear: true,
    yearRange: "-100:+10",
 });