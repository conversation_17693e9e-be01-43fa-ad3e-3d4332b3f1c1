"use client";

import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/app-components/dataTable";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Loader2, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import React, { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import {
  deleteThought,
  getThought,
  updateThoughtStatus,
} from "@/services/classesThoughtApi";
import { truncateThought } from "@/lib/utils";
import { Thought } from "@/lib/types";

import Pagination from "@/app-components/pagination";
import ConfirmDialog from "@/app-components/ConfirmDialog";

const PAGE_SIZE = 10;

const AdminThoughtPage = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [thoughts, setThoughts] = useState<Thought[]>([]);
  const [updatingThoughtId, setUpdatingThoughtId] = useState<string | null>(
    null
  );
  const [statusFilter, setStatusFilter] = useState<
    "ALL" | "PENDING" | "APPROVED" | "REJECTED"
  >("ALL");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [thoughtToDelete, setThoughtToDelete] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchThoughts = useCallback(
    async (page: number, status?: "PENDING" | "APPROVED" | "REJECTED") => {
      try {
        setIsLoading(true);
        const response = await getThought(status, undefined, page, PAGE_SIZE);

        setThoughts(response.thoughts);
        setTotalPages(response.pages || 1);
      } catch (error: any) {
        toast.error(error.message || "Failed to fetch thoughts");
        setThoughts([]);
        setTotalPages(1);
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  useEffect(() => {
    fetchThoughts(1);
  }, [fetchThoughts]);

  const handleFilter = () => {
    const status = statusFilter !== "ALL" ? statusFilter : undefined;
    setCurrentPage(1);
    fetchThoughts(1, status);
  };

  const handleDeleteThought = async () => {
    if (!thoughtToDelete) return;

    setIsDeleting(true);
    try {
      await deleteThought(thoughtToDelete);
      toast.success("Thought deleted successfully!");
      fetchThoughts(
        currentPage,
        statusFilter !== "ALL" ? statusFilter : undefined
      );
    } catch (error: any) {
      toast.error(error.message || "Failed to delete thought");
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
      setThoughtToDelete(null);
    }
  };

  const openDeleteConfirmation = (id: string) => {
    setThoughtToDelete(id);
    setIsDeleteDialogOpen(true);
  };

  const handleStatusChange = async (
    thoughtId: string,
    status: "PENDING" | "APPROVED" | "REJECTED"
  ) => {
    const previousThoughts = [...thoughts];
    setThoughts(
      thoughts.map((t) =>
        t.id === thoughtId
          ? { ...t, status, updatedAt: new Date().toISOString() }
          : t
      )
    );
    try {
      setUpdatingThoughtId(thoughtId);
      const updatedThought = await updateThoughtStatus(thoughtId, status);
      setThoughts(
        thoughts.map((t) => (t.id === thoughtId ? updatedThought : t))
      );
      toast.success(`Thought status updated to ${status}`);
    } catch (error: any) {
      setThoughts(previousThoughts);
      toast.error(
        error.response?.data?.message || "Failed to update thought status"
      );
    } finally {
      setUpdatingThoughtId(null);
    }
  };

  const columns: ColumnDef<Thought>[] = [
    {
      accessorKey: "class.className",
      header: "Class Name",
      cell: ({ row }) => row.original.class?.className || "N/A",
    },
    {
      accessorKey: "class.contactNo",
      header: "Contact Number",
      cell: ({ row }) => row.original.class?.contactNo || "N/A",
    },
    {
      accessorKey: "thoughts",
      header: "Thought",
      cell: ({ row }) => (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className="cursor-pointer">
                {truncateThought(row.original.thoughts)}
              </span>
            </TooltipTrigger>
            <TooltipContent
              className="max-w-[300px] p-3 bg-slate-200 text-black rounded-lg shadow-md border border-gray-600 text-sm leading-relaxed whitespace-normal break-words"
              side="bottom"
            >
              <p>{row.original.thoughts}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <Select
          value={row.original.status}
          onValueChange={(value: "PENDING" | "APPROVED" | "REJECTED") =>
            handleStatusChange(row.original.id, value)
          }
          disabled={updatingThoughtId === row.original.id}
        >
          <SelectTrigger
            className="w-[150px] mb-2"
            aria-label={`Status for thought ${truncateThought(
              row.original.thoughts
            )}`}
          >
            {updatingThoughtId === row.original.id ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <SelectValue placeholder="Select status" />
            )}
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="PENDING">Pending</SelectItem>
            <SelectItem value="APPROVED">Approved</SelectItem>
            <SelectItem value="REJECTED">Rejected</SelectItem>
          </SelectContent>
        </Select>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => openDeleteConfirmation(row.original.id)}
          className="text-red-500 hover:text-red-700 hover:bg-red-100"
        >
          <Trash2 className="h-4 w-4" />
          <span className="sr-only">Delete</span>
        </Button>
      ),
    },
  ];

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mx-2 mb-2">
        <h1 className="text-2xl font-bold ms-2">Classes Thoughts</h1>
        <div className="flex gap-4">
          <Select
            value={statusFilter}
            onValueChange={(
              value: "ALL" | "PENDING" | "APPROVED" | "REJECTED"
            ) => setStatusFilter(value)}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All</SelectItem>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="APPROVED">Approved</SelectItem>
              <SelectItem value="REJECTED">Rejected</SelectItem>
            </SelectContent>
          </Select>
          <div className="mt-1 flex items-center space-x-5 me-3">
            <Button onClick={handleFilter}>Filter</Button>
          </div>
        </div>
      </div>

      <DataTable
        columns={columns}
        data={thoughts}
        isLoading={isLoading}
      />

      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={(page) => {
          setCurrentPage(page);
          const status = statusFilter !== "ALL" ? statusFilter : undefined;
          fetchThoughts(page, status);
        }}
        entriesText={`${thoughts.length} entries`}
      />

      <ConfirmDialog
        open={isDeleteDialogOpen}
        setOpen={setIsDeleteDialogOpen}
        title="Are you sure?"
        description="This will permanently delete the thought."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDeleteThought}
        isLoading={isDeleting}
      />
    </div>
  );
};

export default AdminThoughtPage;
