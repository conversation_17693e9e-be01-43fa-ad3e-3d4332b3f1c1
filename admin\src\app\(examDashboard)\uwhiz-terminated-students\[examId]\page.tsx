'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { toast } from 'sonner';
import { getTerminatedStudents } from '@/services/uwhizQuizTerminationLogApi';
import { terminatedStudents, ApiResponse } from '@/lib/types';
import { createColumnHelper, ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/app-components/dataTable';
import Pagination from '@/app-components/pagination';

const columnHelper = createColumnHelper<terminatedStudents>();

const columns: ColumnDef<terminatedStudents, any>[] = [
  columnHelper.accessor(row => `${row.firstName} ${row.lastName}`, {
    id: 'fullName',
    header: 'Name',
    cell: info => info.getValue(),
  }),
  columnHelper.accessor('email', {
    header: 'Email',
    cell: info => info.getValue(),
  }),
  columnHelper.accessor('reason', {
    header: 'Reason',
    cell: info => info.getValue(),
  }),
  columnHelper.accessor('createdAt', {
    header: 'Terminated At',
    cell: info =>
      new Date(info.getValue()).toLocaleString('en-IN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      }),
  }),
];

export default function TerminatedStudentsPage() {
  const { examId } = useParams();
  const [data, setData] = useState<terminatedStudents[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const limit = 10;
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      if (typeof examId !== 'string') return;

      setLoading(true);
      try {
        const response: ApiResponse = await getTerminatedStudents(parseInt(examId), page, limit);
        if (!response.success) {
          console.log('Failed to fetch data.');
          return;
        }

        if (
          Array.isArray(response.data) &&
          response.data.every(item => 'reason' in item)
        ) {
          setData(response.data as terminatedStudents[]);
        } else {
          setData([]);
          toast.error('Invalid data received for terminated students.');
        }

        setTotal(response.total || response.data.length || 0);
      } catch (err: any) {
        toast.error('Something went wrong.');
        console.log(err);
        setData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [examId, page]);

  const totalPages = Math.max(1, Math.ceil(total / limit));

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Terminated Students</h1>

      <DataTable
        columns={columns}
        data={data}
        isLoading={loading}
      />

      <Pagination
        page={page}
        totalPages={totalPages}
        setPage={setPage}
        entriesText={`${total} entries`}
      />
    </div>
  );
}
