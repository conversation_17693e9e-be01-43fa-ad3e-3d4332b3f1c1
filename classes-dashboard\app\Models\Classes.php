<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class Classes extends Authenticatable
{
    use <PERSON><PERSON><PERSON>Tokens, HasFactory, HasRoles;

    protected $guard = 'class_users';
    protected $table = 'Classes';

    protected $primaryKey = 'id';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id',
        'first_name',
        'last_name',
        'contact_number',
        'email',
        'class_name',
        'username',
        'password',
    ];

    protected $hidden = [
        'password',
        'reset_token',
    ];

    /**
     * Treat all Classes users as Super Admin.
     *
     * @return bool
     */
    public function getIsSuperAdmin()
    {
        return true; // All Classes users are super admins
    }

    /**
     * Override hasPermissionTo to grant all permissions.
     *
     * @param string|\Spatie\Permission\Contracts\Permission $permission
     * @param string|null $guardName
     * @return bool
     */
    public function hasPermissionTo($permission, $guardName = null): bool
    {
        return true; // Grant all permissions to Classes users
    }

    /**
     * Override hasRole to grant all roles (for compatibility).
     *
     * @param string|array|\Spatie\Permission\Contracts\Role $roles
     * @param string|null $guardName
     * @return bool
     */
    public function hasRole($roles, $guardName = null): bool
    {
        return true; // Grant all roles to Classes users
    }

    public function about()
    {
        return $this->hasOne(ClassesLogo::class, 'classId', 'id')->withDefault();
    }
}