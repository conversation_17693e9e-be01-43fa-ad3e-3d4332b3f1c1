'use client';

import { type LucideIcon } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

interface NavItem {
  title: string;
  url?: string;
  icon?: LucideIcon;
  children?: NavItem[];
}

export function NavMain({
  items,
}: {
  items: NavItem[];
}) {
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({});

  const toggleExpand = (title: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [title]: !prev[title]
    }));
  };

  return (
    <SidebarGroup>
      <SidebarGroupContent className="flex flex-col gap-2">
        <SidebarMenu>
          {items.map((item) => {
            const hasChildren = item.children && item.children.length > 0;
            const isExpanded = expandedItems[item.title];

            return (
              <SidebarMenuItem key={item.title}>
                {hasChildren ? (
                  <div className="w-full">
                    <div
                      onClick={() => toggleExpand(item.title)}
                      className="cursor-pointer"
                    >
                      <SidebarMenuButton
                        tooltip={item.title}
                        className={`${isExpanded ? 'bg-gray-100' : ''} hover:bg-gray-100 transition-colors`}
                      >
                        {item.icon && <item.icon />}
                        <span>{item.title}</span>
                        <span className="ml-auto">
                          {isExpanded ?
                            <ChevronDown className="h-4 w-4 text-gray-500" /> :
                            <ChevronRight className="h-4 w-4 text-gray-500" />
                          }
                        </span>
                      </SidebarMenuButton>
                    </div>

                    {isExpanded && (
                      <div className="ml-6 mt-2 border-l-2 border-gray-200 pl-2 space-y-1 animate-in fade-in-50 duration-200">
                        {item.children?.map((child) => (
                          <Link href={child.url || '#'} key={child.title}>
                            <SidebarMenuButton tooltip={child.title} className="py-1.5 hover:bg-gray-100 rounded-md transition-colors">
                              {child.icon && <child.icon className="h-4 w-4 mr-2" />}
                              <span className="text-sm">{child.title}</span>
                            </SidebarMenuButton>
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link href={item.url || '#'}>
                    <SidebarMenuButton tooltip={item.title}>
                      {item.icon && <item.icon />}
                      <span>{item.title}</span>
                    </SidebarMenuButton>
                  </Link>
                )}
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
