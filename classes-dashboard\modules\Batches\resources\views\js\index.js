var columns = [
    {
        data: "action",
        name: "action",
        orderable: false,
        searchable: false,
    },
    {
        data: "batch_name",
        name: "batch_name",
    },
    {
        data: "subjects",
        name: "subjects",
        orderable: false,
        searchable: false,
    },
    {
        data: "days",
        name: "days",
        orderable: false,
        searchable: false,
    },
    {
        data: "timeslots",
        name: "timeslots",
        orderable: false, // optional
        searchable: false,
    },
    {
        data: "classroom",
        name: "classroom",
        orderable: false,
        searchable: false,
    },
    {
        data: "resource",
        name: "resource",
        orderable: false,
        searchable: false,
    }
];

var table = commonDatatable(
    "#batches_table",
    batchesRoute.index,
    columns
);

$(document).on("click", "#addBatchesEntry", function () {
    var params = $.extend({}, doAjax_params_default);
    params["url"] = batchesRoute.create;
    params["requestType"] = `GET`;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        $("#modeltitle").html("Add New Batches");
        $("#createContent").html(result);
    };
    commonAjax(params);
});

$(document).on("click", ".deleteBatchesEntry", function () {
    var did = $(this).attr("data-deletebatchesid");
    var url = batchesRoute.delete;
    url = url.replace(":did", did);

    var params = $.extend({}, doAjax_params_default);
    params["url"] = url;
    params["requestType"] = `DELETE`;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        toastr.success(result.success);
        table.draw();
    };
    var calert = function calert() {
        commonAjax(params);
    };
    commonAlert(calert);
});

$(document).ready(function () {
    function noSunday(date) {
        return [date.getDay() != 0, ""];
    }
    var year = new Date().getFullYear();
    $("body").delegate("#batches_date", "focusin", function () {
        $(this).datepicker({
            dateFormat: "yy-mm-dd",
            changeMonth: true,
            changeYear: false,
            minDate:new Date(startYearDate),
            maxDate:new Date(endYearDate),
            beforeShowDay: noSunday,
        });
    });
});

$(document).on("click", ".editBatchesEntry", function () {
    var editid = $(this).attr("data-editbatchesid");
    var url = batchesRoute.edit;
    url = url.replace(":editid", editid);

    var params = $.extend({}, doAjax_params_default);
    params["url"] = url;
    params["requestType"] = `GET`;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        $("#modeltitle").html("Edit Event");
        $("#createContent").html(result);
    };
    commonAjax(params);
});

$(document).on("click", ".exportData", function () {
    var url = batchesRoute.export;
    var data = {};

    exportData(url, data);
});

$(document).on("change", "#classroom", function () {
    const selectedClassroomIds = $(this).val();
    loadSubjects(selectedClassroomIds);
});
