import axiosInstance from '../lib/axios';
interface ExamApplicantFilters {
  firstName?: string;
  lastName?: string;
  email?: string;
  contact?: string;
  search?: string;

}

export const getExamApplicant = async (
  examId: number,
  page: number = 1,
  limit: number = 10,
  filters: ExamApplicantFilters = {}
) => {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });
    if (filters.firstName) params.append('firstName', filters.firstName);
    if (filters.lastName) params.append('lastName', filters.lastName);
    if (filters.email) params.append('email', filters.email);
    if (filters.contact) params.append('contact', filters.contact);
    if (filters.search) params.append('search', filters.search);

    const response = await axiosInstance.get(
      `/examApplication/${examId}?${params.toString()}`,
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get applicants: ${error.response?.data?.message || error.message}`,
    };
  }
};
export const resetExamApplicantFilters = async (
  examId: number,
  page: number = 1,
  limit: number = 10
) => {
  return await getExamApplicant(examId, page, limit, {});
};

export const downloadExamApplicantsExcel = async (  
  examId: number,
  filters: {
    firstName?: string;
    lastName?: string;
    email?: string;
    contact?: string;
  } = {}
) => {
  try {
    const queryParams = new URLSearchParams({
      ...(filters.firstName && { firstName: filters.firstName }),
      ...(filters.lastName && { lastName: filters.lastName }),
      ...(filters.email && { email: filters.email }),
      ...(filters.contact && { contact: filters.contact }),
    }).toString();

    const url = `/export/exam-applicants/${examId}${queryParams ? `?${queryParams}` : ''}`;

    const response = await axiosInstance.get(url, {
      headers: {
        "Server-Select": "uwhizServer",
      },
      responseType: 'blob',
    });

    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.setAttribute('download', `exam-applicants-${examId}.xlsx`);
    document.body.appendChild(link);
    link.click();

    link.parentNode?.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    return true;
  } catch (error: any) {
    console.error('Error downloading Excel file:', error);
    throw new Error(`Failed to download Excel file: ${error.message}`);
  }
};

export type { ExamApplicantFilters };