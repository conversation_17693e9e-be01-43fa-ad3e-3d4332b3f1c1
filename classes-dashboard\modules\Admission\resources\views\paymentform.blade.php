<div class="modal" id="makePayment" role="dialog" aria-labelledby="roleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modeltitle" class="box-title popup-title m-0">Add New Entry</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="payment-forms">
                    @csrf
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <input type="hidden" id="fee_student_id" name="student_id" />
                                        <input type="hidden" id="fee_paid_category" name="payment_category" />
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="month"
                                                    class="form-label">Month *</label>
                                                <input id="month"
                                                    placeholder="Enter month"
                                                    readonly
                                                    class="form-control" name="month_name"
                                                    type="text">
                                            </div>
                                        </div>
                                        @if (isset($categoryData))
                                            @foreach ($categoryData as $cat)
                                                @php
                                                    $inputId = str_replace(' ', '', $cat['fees_category']) . '_input';
                                                @endphp
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="{{ $inputId }}" class="form-label">{{ $cat['fees_category'] }} *</label>
                                                        <input id="{{ $inputId }}"
                                                            placeholder="Enter {{ $cat['fees_category'] }}"
                                                            data-category="{{ $cat['fees_category'] }}"
                                                            class="form-control categoryInput"
                                                            name="category_type[]"
                                                            type="number"
                                                            readonly>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @endif
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Payment Mode *</label>
                                                <select id="payment_mode" class="form-control select2"
                                                    name="payment_mode">
                                                    <option value="">Select Payment Mode</option>
                                                    <option value="{{ config('constants.PAYMENT_MODE.CASH') }}">
                                                        {{ config('constants.PAYMENT_MODE.CASH') }}</option>
                                                    <option value="{{ config('constants.PAYMENT_MODE.ONLINE') }}">
                                                        {{ config('constants.PAYMENT_MODE.ONLINE') }}</option>
                                                    <option value="{{ config('constants.PAYMENT_MODE.CHEQUE') }}">
                                                        {{ config('constants.PAYMENT_MODE.CHEQUE') }}</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                {!! Form::label('paid_amount', 'Paid Amount *', ['class' => 'form-label']) !!}
                                                {!! Form::number('paid_amount', null, [
                                                    'readonly',
                                                    'id' => 'paid_amount',
                                                    'placeholder' => 'Enter Paid Amount',
                                                    'class' => 'form-control',
                                                ]) !!}
                                            </div>
                                        </div>
                                        <div class="col-md-6 check-field">
                                            <div class="form-group">
                                                {!! Form::label('cheque_no', 'Cheque No *', ['class' => 'form-label']) !!}
                                                {!! Form::text('cheque_no', null, [
                                                    'id' => 'cheque_no',
                                                    'placeholder' => 'Enter Cheque No',
                                                    'class' => 'form-control',
                                                ]) !!}
                                            </div>
                                        </div>
                                        <div class="col-md-6 reference-field">
                                            <div class="form-group">
                                                {!! Form::label('reference_no', 'Reference No *', ['class' => 'form-label']) !!}
                                                {!! Form::text('reference_no', null, [
                                                    'id' => 'reference_no',
                                                    'placeholder' => 'Enter Reference No',
                                                    'class' => 'form-control',
                                                ]) !!}
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <button class="btn btn-primary">Submit</button>
                                                <button data-dismiss="modal"
                                                    class="btn btn-secondary ml-2">Cancel</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                </form>
            </div>
        </div>
    </div>
</div>
