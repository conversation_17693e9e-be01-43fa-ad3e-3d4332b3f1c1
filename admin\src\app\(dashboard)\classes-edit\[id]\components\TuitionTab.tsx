'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import Link from 'next/link';
import TuitionClassForm from './TuitionClassForm';
import TuitionClassList from './TuitionClassList';

interface TuitionTabProps {
  showAddForm: boolean;
  setShowAddForm: (show: boolean) => void;
  newTuitionClass: {
    education: string;
    coachingType: string[];
    boardType: string[];
    medium: string[];
    section: string[];
    subject: string[];
    details: string[];
  };
  handleNewTuitionChange: (field: string, value: string | string[]) => void;
  handleAddTuitionClass: () => void;
  constants: any[];
  getConstantValues: (name: string) => any[];
  formData: any;
  handleDeleteTuitionClass: (tuitionId: string) => void;
  parseFieldValue: (value: string | null | undefined) => string;
  isSaving: boolean;
  onSave: () => Promise<void>;
}

const TuitionTab: React.FC<TuitionTabProps> = ({
  showAddForm,
  setShowAddForm,
  newTuitionClass,
  handleNewTuitionChange,
  handleAddTuitionClass,
  constants,
  getConstantValues,
  formData,
  handleDeleteTuitionClass,
  parseFieldValue,
  isSaving,
  onSave,
}) => {
  return (
    <div className="space-y-6">
      <TuitionClassForm
        showAddForm={showAddForm}
        setShowAddForm={setShowAddForm}
        newTuitionClass={newTuitionClass}
        handleNewTuitionChange={handleNewTuitionChange}
        handleAddTuitionClass={handleAddTuitionClass}
        constants={constants}
        getConstantValues={getConstantValues}
      />

      <TuitionClassList
        tuitionClasses={formData.tuitionClasses}
        handleDeleteTuitionClass={handleDeleteTuitionClass}
        parseFieldValue={parseFieldValue}
      />
      
      {/* Save Button for Tuition */}
      <div className="flex justify-end gap-4 pt-6">
        <Link href="/dashboard">
          <Button type="button" variant="outline" className="border-gray-300 text-black hover:bg-black hover:text-white">
            Cancel
          </Button>
        </Link>
        <Button
          type="button"
          onClick={onSave}
          disabled={isSaving}
          className="bg-black hover:bg-gray-800 text-white disabled:bg-gray-400"
        >
          {isSaving ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            'Save Changes'
          )}
        </Button>
      </div>
    </div>
  );
};

export default TuitionTab;
