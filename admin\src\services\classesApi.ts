import { axiosInstance } from '@/lib/axios';

export const downloadExcel = async (
  endpoint: string,
  fileName: string,
  filters?: Record<string, string>
) => {
  try {
    let url = endpoint;
    const params = new URLSearchParams();

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== 'all') {
          params.append(key, value);
        }
      });
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    const response = await axiosInstance.get(url, {
      responseType: 'blob',
    });

    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const downloadUrl = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = downloadUrl;
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();

    link.parentNode?.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    return true;
  } catch (error: any) {
    console.error('Error downloading Excel file:', error);
    throw new Error(`Failed to download Excel file: ${error.message}`);
  }
};

export const downloadClassesExcel = async (filters?: { status?: string; search?: string }) => {
  return downloadExcel('/export/classes/excel', 'classes.xlsx', filters);
};

export const downloadExamApplicationsExcel = async (filters?: {
  search?: string;
  filterType?: string;
}) => {
  return downloadExcel('/export/exam-applications/excel', 'exam-applications.xlsx', filters);
};

export const updateClassByAdmin = async (classId: string, updateData: any) => {
  try {
    const response = await axiosInstance.put(`/classes/admin/${classId}`, updateData);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to update class');
  }
};

export const updateClassImagesByAdmin = async (classId: string, imageData: FormData) => {
  try {
    const response = await axiosInstance.post(`/classes-profile/admin/${classId}/images`, imageData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to update class images');
  }
};

export const fetchClassDataByAdmin = async (classId: string) => {
  try {
    const response = await axiosInstance.get(`/classes/details/${classId}/admin`);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch class data');
  }
};

export const fetchConstants = async () => {
  try {
    const response = await axiosInstance.get('/constant');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch constants');
  }
};

export const addTuitionClassByAdmin = async (formattedData: any) => {
  try {
    const response = await axiosInstance.post(`/classes-profile/admin/tuition-classes`, formattedData);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to add tuition class');
  }
};

export const deleteTuitionClassByAdmin = async (tuitionId: string, classId: string) => {
  try {
    const response = await axiosInstance.delete(`/classes-profile/admin/tuition-class/${tuitionId}`, {
      data: { classId },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to delete tuition class');
  }
};



export const addEducationByAdmin = async (classId: string, educationData: any, certificateFiles?: File[]) => {
  try {
    const formData = new FormData();

    // Add noDegree flag
    formData.append('noDegree', educationData.noDegree ? 'true' : 'false');

    // Add education data if not noDegree
    if (!educationData.noDegree && educationData.education) {
      formData.append('education', JSON.stringify(educationData.education));

      // Add certificate files
      if (certificateFiles && certificateFiles.length > 0) {
        certificateFiles.forEach((file) => {
          formData.append('files', file);
        });
      }
    }

    const response = await axiosInstance.post(`/classes-profile/admin/${classId}/education`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to add education record');
  }
};

export const addExperienceByAdmin = async (classId: string, experienceData: any, certificateFiles?: File[]) => {
  try {
    const formData = new FormData();

    // Add noExperience flag
    formData.append('noExperience', experienceData.noExperience ? 'true' : 'false');

    // Add experience data if not noExperience
    if (!experienceData.noExperience && experienceData.experiences) {
      formData.append('experiences', JSON.stringify(experienceData.experiences));

      // Add certificate files
      if (certificateFiles && certificateFiles.length > 0) {
        certificateFiles.forEach((file) => {
          formData.append('files', file);
        });
      }
    }

    const response = await axiosInstance.post(`/classes-profile/admin/${classId}/experience`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to add experience record');
  }
};

export const addCertificateByAdmin = async (classId: string, certificateData: any, certificateFiles?: File[]) => {
  try {
    const formData = new FormData();

    // Add noCertificates flag
    formData.append('noCertificates', certificateData.noCertificates ? 'true' : 'false');

    // Add certificate data if not noCertificates
    if (!certificateData.noCertificates && certificateData.certificates) {
      formData.append('certificates', JSON.stringify(certificateData.certificates));

      // Add certificate files
      if (certificateFiles && certificateFiles.length > 0) {
        certificateFiles.forEach((file) => {
          formData.append('files', file);
        });
      }
    }

    const response = await axiosInstance.post(`/classes-profile/admin/${classId}/certificates`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to add certificate record');
  }
};

export const deleteEducationByAdmin = async (educationId: string, classId: string) => {
  try {
    const response = await axiosInstance.delete(`/classes-profile/admin/education/${educationId}`, {
      data: { classId },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to delete education record');
  }
};

// Experience API functions

export const deleteExperienceByAdmin = async (experienceId: string, classId: string) => {
  try {
    const response = await axiosInstance.delete(`/classes-profile/admin/experience/${experienceId}`, {
      data: { classId },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to delete experience record');
  }
};

// Certificate API functions

export const deleteCertificateByAdmin = async (certificateId: string, classId: string) => {
  try {
    const response = await axiosInstance.delete(`/classes-profile/admin/certificate/${certificateId}`, {
      data: { classId },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to delete certificate record');
  }
};
