<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    {!! Form::model($data, ['id' => 'editbatches_form', 'route' => ['batches.update', $data->id]]) !!}
                    @include('Batches::fields')
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
@if (isset($data))
    <script>
        window.preloadedSubjectIds = {!! json_encode($data->batchSubjects->pluck('subject_id')) !!};
        window.preloadedClassroomIds = {!! json_encode($data->batchClassroom->pluck('classroom_id')) !!};

        $(document).ready(function() {
            setTimeout(() => {
                if (window.preloadedClassroomIds) {
                    $('#classroom').val(window.preloadedClassroomIds).trigger('change');
                    loadSubjects(window.preloadedClassroomIds, window.preloadedSubjectIds);
                }
            }, 500);

        });
    </script>
@endif
{!! JsValidator::formRequest('Batches\Http\Requests\CreateBatchesRequest', '#editbatches_form') !!}
<script>
    $("#editbatches_form").submit(function() {
        event.preventDefault();
        var form = $(this);
        if ($(this).valid()) {
            var url = "{{ route('batches.update', $data->id) }}";
            ajaxHandler(form, url, 'PATCH', '#editbatches_form', '#savebatches', '#newBatchesEntry',
                '#batches_table');
            return false;
        }
    });
</script>
