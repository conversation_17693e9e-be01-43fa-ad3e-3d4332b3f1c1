import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import AdminNotificationBell from './AdminNotificationBell';

export function SiteHeader() {
  return (
    <header className="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear bg-black rounded-tl-lg">
      <div className="flex w-full items-center justify-between gap-1 px-4 lg:gap-2 lg:px-6">
        <div className="flex items-center gap-1 lg:gap-2">
          <SidebarTrigger className="-ml-1 text-white" />
          <Separator orientation="vertical" className="mx-2 data-[orientation=vertical]:h-4 bg-white" />
          <h1 className="text-base font-medium text-white">Dashboard</h1>
        </div>
        <div className="flex items-center gap-2">
          <AdminNotificationBell />
        </div>
      </div>
    </header>
  );
}
