<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DocumentCategoriesTableSeeder extends Seeder
{
    /**
     * Run the seeder.
     *
     * @return void
     */
    public function run()
    {
        $categories = [
            ['category_name' => 'Birth Certificate'],
            ['category_name' => 'Adhaar Card'],
            ['category_name' => 'Passport'],
            ['category_name' => 'Address Proof'],
            ['category_name' => 'Bonafide Certificate'],
            ['category_name' => 'Medical Records'],
            ['category_name' => 'Allergy/Condition Reports'],
            ['category_name' => 'Parental/Guardian Documents'],
            ['category_name' => 'ID Proof (Parent/Guardian)'],
            ['category_name' => 'Income Certificate'],
            ['category_name' => 'Caste/Category Certificate'],
            ['category_name' => 'Fees'],
            ['category_name' => 'Others'],
        ];

        // Insert data into the document_categories table
        DB::table('document_categories')->insert($categories);
    }
}