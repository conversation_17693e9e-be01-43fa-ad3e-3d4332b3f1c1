<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class PermissionsSeeder extends Seeder
{
    public function run()
    {
        $permissionsConfig = config('constants.PERMISSIONS');

        foreach ($permissionsConfig as $module => $actions) {
            foreach (array_keys($actions) as $permissionName) {
                Permission::firstOrCreate(['name' => $permissionName]);
            }
        }
        echo "Permission seeding completed!\n";
    }
}
