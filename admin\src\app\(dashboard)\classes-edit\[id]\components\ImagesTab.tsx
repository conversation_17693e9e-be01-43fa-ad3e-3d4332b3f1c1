'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { X } from 'lucide-react';

interface ImagesTabProps {
  isSaving: boolean;
  formData: any;
  profilePhotoPreview: string;
  classesLogoPreview: string;
  handleImageChange: (type: 'profilePhoto' | 'classesLogo', file: File | null) => void;
  handleClassAboutChange: (field: string, value: string) => void;
  setProfilePhotoPreview: (preview: string) => void;
  setProfilePhotoFile: (file: File | null) => void;
  setClassesLogoPreview: (preview: string) => void;
  setClassesLogoFile: (file: File | null) => void;
  onSave: () => Promise<void>;
}

const ImagesTab: React.FC<ImagesTabProps> = ({
  isSaving,
  formData,
  profilePhotoPreview,
  classesLogoPreview,
  handleImageChange,
  handleClassAboutChange,
  setProfilePhotoPreview,
  setProfilePhotoFile,
  setClassesLogoPreview,
  setClassesLogoFile,
  onSave,
}) => {
  const profilePhotoInputRef = React.useRef<HTMLInputElement>(null);
  const classesLogoInputRef = React.useRef<HTMLInputElement>(null);

  const handleSave = async () => {
    await onSave();
  };

  return (
    <div className="space-y-8 pt-6">
      <h2 className="text-lg font-semibold text-black border-b border-gray-200 pb-2">
        Images
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Profile Photo */}
        <div className="space-y-2">
          <Label htmlFor="profilePhoto" className="text-black font-medium">
            Profile Photo <span className="text-gray-500 text-sm font-normal">(Optional)</span>
          </Label>
          <div className="space-y-3">
            <Input
              ref={profilePhotoInputRef}
              id="profilePhoto"
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  handleImageChange('profilePhoto', file);
                }
              }}
              className="border-gray-300 focus:border-black focus:ring-black"
            />
            {(profilePhotoPreview || formData.ClassAbout.profilePhoto) && (
              <div className="relative w-32 h-32 border border-gray-300 rounded-lg overflow-hidden">
                <Image
                  src={profilePhotoPreview || `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${formData.ClassAbout.profilePhoto}`}
                  alt="Profile Photo Preview"
                  fill
                  className="object-cover"
                />
                <button
                  type="button"
                  onClick={() => {
                    setProfilePhotoPreview('');
                    setProfilePhotoFile(null);
                    handleClassAboutChange('profilePhoto', '');
                    if (profilePhotoInputRef.current) {
                      profilePhotoInputRef.current.value = '';
                    }
                  }}
                  className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Classes Logo */}
        <div className="space-y-2">
          <Label htmlFor="classesLogo" className="text-black font-medium">
            Classes Logo <span className="text-gray-500 text-sm font-normal">(Optional)</span>
          </Label>
          <div className="space-y-3">
            <Input
              ref={classesLogoInputRef}
              id="classesLogo"
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  handleImageChange('classesLogo', file);
                }
              }}
              className="border-gray-300 focus:border-black focus:ring-black"
            />
            {(classesLogoPreview || formData.ClassAbout.classesLogo) && (
              <div className="relative w-32 h-32 border border-gray-300 rounded-lg overflow-hidden">
                <Image
                  src={classesLogoPreview || `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${formData.ClassAbout.classesLogo}`}
                  alt="Classes Logo Preview"
                  fill
                  className="object-cover"
                />
                <button
                  type="button"
                  onClick={() => {
                    setClassesLogoPreview('');
                    setClassesLogoFile(null);
                    handleClassAboutChange('classesLogo', '');
                    if (classesLogoInputRef.current) {
                      classesLogoInputRef.current.value = '';
                    }
                  }}
                  className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Save Button for Images */}
      <div className="flex justify-end gap-4 pt-6">
        <Link href="/dashboard">
          <Button type="button" variant="outline" className="border-gray-300 text-black hover:bg-black hover:text-white">
            Cancel
          </Button>
        </Link>
        <Button
          onClick={handleSave}
          disabled={isSaving}
          className="bg-black hover:bg-gray-800 text-white disabled:bg-gray-400"
        >
          {isSaving ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            'Save Changes'
          )}
        </Button>
      </div>
    </div>
  );
};

export default ImagesTab;
