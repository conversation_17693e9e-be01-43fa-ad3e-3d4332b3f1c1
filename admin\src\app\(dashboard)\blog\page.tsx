"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Blog } from "@/lib/types";
import { toast } from "sonner";
import { format } from "date-fns";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/app-components/dataTable";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Button } from "@/components/ui/button";
import { Loader2, Trash2, Eye } from "lucide-react";
import Link from "next/link";
import { deleteBlog, getBlogs, updateBlogStatus } from "@/services/blogApi";

import Pagination from "@/app-components/pagination";
import ConfirmDialog from "@/app-components/ConfirmDialog";

const BlogPage = () => {
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [updatingBlogId, setUpdatingBlogId] = useState<string | null>(null);
  const [deletingBlogId, setDeletingBlogId] = useState<string | null>(null);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);

  const fetchBlogs = useCallback(
    async (page: number) => {
      try {
        if (blogs.length === 0) {
          setIsLoading(true);
        }
        const response = await getBlogs(page);
        setBlogs(response.blogs);
        setTotalPages(response.totalPages);
        setCurrentPage(page);
      } catch (error: any) {
        toast.error(error.message || "Failed to fetch blogs");
      } finally {
        setIsLoading(false);
      }
    },
    [blogs.length]
  );

  useEffect(() => {
    fetchBlogs(currentPage);
  }, [fetchBlogs, currentPage]);

  const handleStatusChange = async (
    blogId: string,
    status: "PENDING" | "APPROVED" | "REJECTED"
  ) => {
    const previousBlogs = [...blogs];
    setBlogs(
      blogs.map((blog) =>
        blog.id === blogId
          ? { ...blog, status, updatedAt: new Date().toISOString() }
          : blog
      )
    );
    try {
      setUpdatingBlogId(blogId);
      await updateBlogStatus(blogId, status);
      toast.success(`Blog status updated to ${status}`);
    } catch (error: any) {
      setBlogs(previousBlogs);
      toast.error(error.message || "Failed to update blog status");
    } finally {
      setUpdatingBlogId(null);
    }
  };

  const handleDeleteClick = (id: string) => {
    setDeletingBlogId(id);
    setIsConfirmDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingBlogId) return;

    try {
      await deleteBlog(deletingBlogId);
      setBlogs(blogs.filter((blog) => blog.id !== deletingBlogId));
      toast.success("Blog deleted successfully");
    } catch (error: any) {
      toast.error(error.message || "Failed to delete blog");
    } finally {
      setDeletingBlogId(null);
      setIsConfirmDialogOpen(false);
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM dd, yyyy HH:mm");
  };

  const truncateDescription = (text: string, maxLength: number = 100) => {
    const decodedText = text.replace(/<[^>]*>/g, "");
    if (decodedText.length <= maxLength) return decodedText;
    return decodedText.substring(0, maxLength) + "...";
  };

  const columns: ColumnDef<Blog>[] = [
    {
      accessorKey: "blogTitle",
      header: "Title",
      cell: ({ row }) => (
        <span className="font-medium">{row.original.blogTitle}</span>
      ),
    },
    {
      accessorKey: "class.firstName",
      header: "Author",
      cell: ({ row }) =>
        row.original.class
          ? `${row.original.class.firstName} ${row.original.class.lastName}`
          : "N/A",
    },
    {
      accessorKey: "class.className",
      header: "Classes Name",
      cell: ({ row }) =>
        row.original.class ? row.original.class.className : "N/A",
    },
    {
      accessorKey: "blogDescription",
      header: "Description",
      cell: ({ row }) => truncateDescription(row.original.blogDescription),
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => formatDate(row.original.createdAt),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <Select
          defaultValue={row.original.status}
          onValueChange={(value) =>
            handleStatusChange(
              row.original.id,
              value as "PENDING" | "APPROVED" | "REJECTED"
            )
          }
          disabled={updatingBlogId === row.original.id}
        >
          <SelectTrigger className="w-[150px] mb-2">
            <SelectValue placeholder={row.original.status} />
            {updatingBlogId === row.original.id && (
              <Loader2 className="h-4 w-4 animate-spin ml-2" />
            )}
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="PENDING">Pending</SelectItem>
            <SelectItem value="APPROVED">Approved</SelectItem>
            <SelectItem value="REJECTED">Rejected</SelectItem>
          </SelectContent>
        </Select>
      ),
    },
    {
      id: "actions",
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => (
        <div className="flex justify-end gap-2">
          <Link href={`/blog/${row.original.id}`} passHref>
            <Button variant="ghost" size="icon">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <Button
            variant="ghost"
            size="icon"
            className="p-1 text-red-500 hover:text-red-700 hover:bg-red-100"
            onClick={() => handleDeleteClick(row.original.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-3">
        <h1 className="text-2xl font-bold ms-4">Blogs Managements</h1>
      </div>

      <DataTable
        columns={columns}
        data={blogs}
        isLoading={isLoading}
      />

      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={(page) => {
          setCurrentPage(page);
          fetchBlogs(page);
        }}
        entriesText={`${blogs.length} entries`}
      />

      <ConfirmDialog
        open={isConfirmDialogOpen}
        setOpen={setIsConfirmDialogOpen}
        title="Are you sure?"
        description="This will permanently delete the blog."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDeleteConfirm}
        isLoading={false}
      />
    </div>
  );
};

export default BlogPage;
