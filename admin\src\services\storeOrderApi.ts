import { axiosInstance } from '@/lib/axios';
import { StoreOrderFilters, StoreOrderPaginationResponse } from '@/lib/types';

export const getAllStoreOrders = async (
  page: number = 1,
  limit: number = 10,
  filters?: StoreOrderFilters
): Promise<StoreOrderPaginationResponse | { success: false; error: string }> => {
  try {
    const params: any = { page, limit };
    if (filters?.status) params.status = filters.status;
    if (filters?.search) params.search = filters.search;
    if (filters?.modelType) params.modelType = filters.modelType;

    const response = await axiosInstance.get('/admin/store/orders', { params });
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store orders'
    };
  }
};

export const getStoreOrderStats = async () => {
  try {
    const response = await axiosInstance.get('/admin/store/orders/stats');
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store order statistics'
    };
  }
};

export const getStoreOrderById = async (orderId: string) => {
  try {
    const response = await axiosInstance.get(`/admin/store/orders/${orderId}`);
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store order'
    };
  }
};

export const updateStoreOrderStatus = async (orderId: string, status: string) => {
  try {
    const response = await axiosInstance.put(`/admin/store/orders/${orderId}`, { status });
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to update store order status'
    };
  }
};
