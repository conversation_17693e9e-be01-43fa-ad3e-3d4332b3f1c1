import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { getExams, deleteExam, createExam, updateExam } from '../services/examApi';
import { Exam, TransformedExam } from '@/lib/types';
import { ExamFormValues } from '@/lib/validations/examSchema';
import { toast } from 'sonner';

interface ExamState {
  exams: TransformedExam[];
  currentPage: number;
  totalPages: number;
  totalRecords: number;
  limit: number;
  isDeleteDialogOpen: boolean;
  isFormDialogOpen: boolean;
  selectedExam: TransformedExam | null;
  examToDelete: number | null;
  loading: boolean;
  error: string | null;
}

const initialState: ExamState = {
  exams: [],
  currentPage: 1,
  totalPages: 1,
  totalRecords: 0,
  limit: 10,
  isDeleteDialogOpen: false,
  isFormDialogOpen: false,
  selectedExam: null,
  examToDelete: null,
  loading: false,
  error: null,
};

// Async thunks
export const fetchExams = createAsyncThunk(
  'exam/fetchExams',
  async ({ page, limit }: { page: number; limit: number }, { rejectWithValue }) => {
    try {
      const response = await getExams(page, limit);
      const examsData = response.exams || [];
      const examsWithNumericMarks = examsData.map((exam: Exam) => ({
        ...exam,
        marks: Number(exam.marks),
      }));
      return {
        exams: examsWithNumericMarks,
        total: response.total || 0,
        totalPages: response.totalPages || 1,
      };
    } catch (error: any) {
      const message = error.message || 'Failed to fetch exams';
      return rejectWithValue(message);
    }
  }
);

export const deleteExamAsync = createAsyncThunk(
  'exam/deleteExam',
  async (examId: number, { rejectWithValue }) => {
    try {
      await deleteExam(examId);
      return examId;
    } catch (error: any) {
      // Extract message from error response if available
      const message = error.message || 'Failed to delete exam';
      return rejectWithValue(message);
    }
  }
);

export const createExamAsync = createAsyncThunk(
  'exam/createExam',
  async (values: ExamFormValues, { rejectWithValue }) => {
    try {
      const submitValues = {
        ...values,
        start_date: new Date(values.start_date).toISOString(),
      };
      const createdExam = await createExam(submitValues);
      return createdExam;
    } catch (error: any) {
      const message = error.message || 'Failed to create exam';
      return rejectWithValue(message);
    }
  }
);

export const updateExamAsync = createAsyncThunk(
  'exam/updateExam',
  async ({ id, values }: { id: number; values: ExamFormValues }, { rejectWithValue }) => {
    try {
      const submitValues = {
        ...values,
        start_date: new Date(values.start_date).toISOString(),
      };
      const updatedExam = await updateExam(id, submitValues);
      return updatedExam;
    } catch (error: any) {
      const message = error.message || 'Failed to update exam';
      return rejectWithValue(message);
    }
  }
);

const examSlice = createSlice({
  name: 'exam',
  initialState,
  reducers: {
    setCurrentPage: (state, action) => {
      state.currentPage = action.payload;
    },
    openDeleteDialog: (state, action) => {
      state.isDeleteDialogOpen = true;
      state.examToDelete = action.payload;
    },
    closeDeleteDialog: (state) => {
      state.isDeleteDialogOpen = false;
      state.examToDelete = null;
    },
    openFormDialog: (state, action) => {
      state.isFormDialogOpen = true;
      state.selectedExam = action.payload || null;
    },
    closeFormDialog: (state) => {
      state.isFormDialogOpen = false;
      state.selectedExam = null;
    },
    resetExams: (state) => {
      state.exams = [];
      state.currentPage = 1;
      state.totalPages = 1;
      state.totalRecords = 0;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Exams
      .addCase(fetchExams.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchExams.fulfilled, (state, action) => {
        state.exams = action.payload.exams;
        state.totalRecords = action.payload.total;
        state.totalPages = action.payload.totalPages;
        state.loading = false;
      })
      .addCase(fetchExams.rejected, (state, action) => {
        state.error = action.payload as string;
        state.loading = false;
        toast.error(action.payload as string);
      })
      // Delete Exam
      .addCase(deleteExamAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteExamAsync.fulfilled, (state, action) => {
        state.exams = state.exams.filter((exam) => exam.id !== action.payload);
        state.totalRecords = Math.max(0, state.totalRecords - 1);
        state.isDeleteDialogOpen = false;
        state.examToDelete = null;
        state.loading = false;
        toast.success('Exam deleted successfully');
      })
      .addCase(deleteExamAsync.rejected, (state, action) => {
        state.error = action.payload as string;
        state.loading = false;
        state.isDeleteDialogOpen = false;
        state.examToDelete = null;
        toast.error(action.payload as string);
      })
      // Create Exam
      .addCase(createExamAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createExamAsync.fulfilled, (state) => {
        state.isFormDialogOpen = false;
        state.currentPage = 1;
        state.loading = false;
        toast.success('Exam created successfully');
      })
      .addCase(createExamAsync.rejected, (state, action) => {
        state.error = action.payload as string;
        state.loading = false;
        toast.error(action.payload as string);
      })
      // Update Exam
      .addCase(updateExamAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateExamAsync.fulfilled, (state) => {
        state.isFormDialogOpen = false;
        state.selectedExam = null;
        state.currentPage = 1;
        state.loading = false;
        toast.success('Exam updated successfully');
      })
      .addCase(updateExamAsync.rejected, (state, action) => {
        state.error = action.payload as string;
        state.loading = false;
        toast.error(action.payload as string);
      });
  },
});

export const {
  setCurrentPage,
  openDeleteDialog,
  closeDeleteDialog,
  openFormDialog,
  closeFormDialog,
  resetExams,
} = examSlice.actions;

export default examSlice.reducer;
