'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Loader2, Plus, X } from 'lucide-react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { addCertificateByAdmin } from '@/services/classesApi';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

const certificateSchema = z.object({
  noCertificates: z.boolean(),
  certificates: z.array(z.object({
    title: z.string(),
    certificate: z.any().optional(),
  })).optional(),
}).superRefine((data, ctx) => {
  if (data.noCertificates) {
    return;
  }

  if (!data.certificates || data.certificates.length === 0) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Please add at least one certificate record or check "I don\'t have certificates"',
      path: ['certificates'],
    });
    return;
  }

  data.certificates.forEach((cert, index) => {
    if (!cert.title || cert.title.trim().length < 2) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Certificate title is required',
        path: ['certificates', index, 'title'],
      });
    }
    if (!cert.certificate || (Array.isArray(cert.certificate) && cert.certificate.length === 0)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Certificate file is required',
        path: ['certificates', index, 'certificate'],
      });
    }
  });
});

type CertificateFormData = z.infer<typeof certificateSchema>;

interface AddCertificateFormProps {
  classId: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const AddCertificateForm: React.FC<AddCertificateFormProps> = ({
  classId,
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const form = useForm<CertificateFormData>({
    resolver: zodResolver(certificateSchema),
    defaultValues: {
      noCertificates: false,
      certificates: [
        {
          title: '',
          certificate: undefined,
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'certificates',
  });

  const watchNoCertificates = form.watch('noCertificates');

  const handleNoCertificatesChange = async (checked: boolean) => {
    form.setValue('noCertificates', checked);

    if (checked) {
      try {
        setIsSubmitting(true);
        await addCertificateByAdmin(classId, { noCertificates: true }, []);
        toast.success('No certificates status added successfully!');
        onSuccess();
        handleClose();
      } catch (error: any) {
        toast.error(error.message || 'Failed to add certificates');
      } finally {
        setIsSubmitting(false);
      }
    }
  };



  const onSubmit = async (data: CertificateFormData) => {
    if (data.noCertificates) {
      return;
    }

    setIsSubmitting(true);

    try {
      const certificateFiles: File[] = [];
      data.certificates?.forEach((cert: any) => {
        if (cert.certificate?.[0]) {
          certificateFiles.push(cert.certificate[0]);
        }
      });

      await addCertificateByAdmin(classId, { noCertificates: false, certificates: data.certificates }, certificateFiles);

      onSuccess();
      handleClose();
      toast.success('Certificate records added successfully!');
    } catch (error: any) {
      console.error('Add certificate error:', error);
      toast.error(error.message || 'Failed to add certificates');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Certificate Record</DialogTitle>
          <DialogDescription>
            Add certificate details for this class.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form  className="space-y-6">
            {/* No Certificates Checkbox */}
            <div className="flex items-start space-x-3">
              <input
                type="checkbox"
                id="noCertificates"
                checked={watchNoCertificates}
                onChange={(e) => handleNoCertificatesChange(e.target.checked)}
                className="mt-1"
              />
              <label htmlFor="noCertificates" className="text-sm font-medium">
                I don&apos;t have certificates
              </label>
            </div>

            {/* Certificate Records */}
            {!watchNoCertificates && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Certificate Records</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      append({
                        title: '',
                        certificate: undefined,
                      })
                    }
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Certificate
                  </Button>
                </div>

                {fields.map((field, index) => (
                  <div key={field.id} className="border border-gray-200 rounded-lg p-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Certificate {index + 1}</h4>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => remove(index)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 gap-4">
                      <FormField
                        control={form.control}
                        name={`certificates.${index}.title`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Certificate Title</FormLabel>
                            <FormControl>
                              <input
                                {...field}
                                placeholder="Enter certificate title"
                                className="flex h-9 w-full rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-black disabled:cursor-not-allowed disabled:opacity-50"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`certificates.${index}.certificate`}
                        render={({ field: { onChange, name } }) => (
                          <FormItem>
                            <FormLabel>Certificate File</FormLabel>
                            <FormControl>
                              <input
                                name={name}
                                type="file"
                                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                onChange={(e) => onChange(e.target.files)}
                                className="flex h-9 w-full rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-black disabled:cursor-not-allowed disabled:opacity-50"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                ))}
            </div>
          )}

          <DialogFooter className="gap-2">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-black hover:bg-gray-800 text-white disabled:bg-gray-400"
              onClick={form.handleSubmit(onSubmit)}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Adding...
                </>
              ) : (
                'Add Certificates'
              )}
            </Button>
          </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddCertificateForm;
