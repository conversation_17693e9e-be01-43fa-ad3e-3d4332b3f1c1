import Link from 'next/link';

export default function NotFound() {
  return (
    <>
      <div className="min-h-screen flex items-center justify-center bg-white overflow-hidden">
        <div className="text-center px-4">
          <h1 className="text-9xl font-extrabold text-[#FD904B] tracking-widest mb-5">404</h1>
          <h2 className="mt-4 text-3xl font-semibold text-gray-800">Page Not Found</h2>
          <p className="mt-2 text-lg text-gray-600">
            Oops! The page you&apos;re looking for doesn&apos;t exist.
          </p>
          <Link href="/">
            <button className="mt-8 px-6 py-3 bg-[#FD904B] text-white font-medium rounded-lg hover:bg-[#974813] transition-colors duration-300">
              Back to Home
            </button>
          </Link>
        </div>
      </div>
    </>
  );
}