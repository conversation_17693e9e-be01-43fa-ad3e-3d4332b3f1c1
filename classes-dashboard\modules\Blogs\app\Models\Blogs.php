<?php

namespace Blogs\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Blogs extends Model
{
    use HasFactory;

    protected $table = 'Blog';
    protected $primaryKey = 'id';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id',
        'classId',
        'blogTitle',
        'blogImage',
        'blogDescription',
        'updatedAt',
        'createdAt',
    ];
    public $timestamps = false;
}
