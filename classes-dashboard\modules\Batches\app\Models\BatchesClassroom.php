<?php

namespace Batches\Models;

use Classroom\Models\Classroom;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BatchesClassroom extends Model
{
    use HasFactory;

    public $table = 'batches_classrooms';
    protected $fillable = [
        'batch_id',
        'classroom_id',
    ];

    public function classroom()
    {
        return $this->belongsTo(Classroom::class, 'classroom_id');
    }
}
