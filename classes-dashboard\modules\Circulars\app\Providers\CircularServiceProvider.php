<?php

namespace Circulars\Providers;

use <PERSON><PERSON>le\ModulesServiceProvider;

class CircularServiceProvider extends ModulesServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        parent::register('Circulars');
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot('Circulars');
    }
}
