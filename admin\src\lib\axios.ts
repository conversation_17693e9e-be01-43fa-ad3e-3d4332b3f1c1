import axios from 'axios';

const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4005/api/v1';
const baseURL2 = process.env.NEXT_PUBLIC_UWHIZ_API_URL || 'http://localhost:4006';

console.log('Axios baseURL:', baseURL);

export const axiosInstance = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

axiosInstance.interceptors.request.use(
  (config) => {
    const serverSelect = config.headers['Server-Select'];
    if (serverSelect === 'uwhizServer') {
      config.baseURL = baseURL2;
    } else {
      config.baseURL = baseURL;
    }

    if (config.data instanceof FormData) {
      delete config.headers['Content-Type'];
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
