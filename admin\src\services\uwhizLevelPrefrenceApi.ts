import axiosInstance from "../lib/axios";
import { LevelPreference } from "../lib/types";

//insert into levelPrefrenceTable
export const createLevelPrefrence = async (
  data: LevelPreference
): Promise<any> => {
  try {
    const response = await axiosInstance.post("/levelPrefrence", data, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to create subject Prefrence: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

//delete from levelPrefrence
export const deleteLevelPrefrence = async (id: string): Promise<any> => {
  try {
    const response = await axiosInstance.delete(`/levelPrefrence/${id}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to delete subject Prefrence: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

//get from levelPrefrence
export const getLevelPrefrence = async (examId: number): Promise<any> => {
  try {
    const response = await axiosInstance.get(`/levelPrefrence/${examId}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch level prefrence: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};