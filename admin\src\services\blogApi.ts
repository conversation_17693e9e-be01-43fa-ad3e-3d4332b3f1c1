import { axiosInstance } from '@/lib/axios';
import { Blog, PaginatedBlogResponse } from '@/lib/types';

export const getBlogs = async (page: number = 1, limit: number = 10): Promise<PaginatedBlogResponse> => {
  try {
    const response = await axiosInstance.get('/blogs', {
      params: { page, limit }
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to fetch blogs: ${error.message}`);
  }
};


export const getBlogById = async (id: string): Promise<Blog> => {
  try {
    const response = await axiosInstance.get(`/blogs/${id}`);
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to fetch blog: ${error.message}`);
  }
};

export const updateBlogStatus = async (id: string, status: 'PENDING' | 'APPROVED' | 'REJECTED'): Promise<Blog> => {
  try {
    const response = await axiosInstance.patch(`/blogs/${id}/status`, { status });
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to update blog status: ${error.message}`);
  }
};

export const deleteBlog = async (id: string): Promise<void> => {
  try {
    await axiosInstance.delete(`/blogs/${id}`);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to delete blog: ${error.message}`);
  }
};