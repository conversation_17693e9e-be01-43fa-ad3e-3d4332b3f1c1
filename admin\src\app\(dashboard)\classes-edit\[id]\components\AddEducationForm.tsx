'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Loader2, Plus, X } from 'lucide-react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { addEducationByAdmin } from '@/services/classesApi';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';

// Zod schema for education validation
const educationSchema = z.object({
  noDegree: z.boolean(),
  education: z.array(z.object({
    degree: z.string(),
    university: z.string(),
    passoutYear: z.string(),
    degreeType: z.string(),
    certificate: z.any().optional(),
  })).optional(),
}).superRefine((data, ctx) => {
  if (data.noDegree) {
    return;
  }

  if (!data.education || data.education.length === 0) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Please add at least one education record or check "I don\'t have education"',
      path: ['education'],
    });
    return;
  }

  // Validate each education record
  data.education.forEach((edu, index) => {
    if (!edu.degree || edu.degree.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Degree is required',
        path: ['education', index, 'degree'],
      });
    }
    if (!edu.university || edu.university.trim().length < 2) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'University is required',
        path: ['education', index, 'university'],
      });
    }
    if (!edu.passoutYear || !/^\d{4}$/.test(edu.passoutYear)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Enter a valid year (e.g., 2022)',
        path: ['education', index, 'passoutYear'],
      });
    }
    if (!edu.degreeType || edu.degreeType.trim().length < 2) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Degree type is required',
        path: ['education', index, 'degreeType'],
      });
    }
    if (!edu.certificate || (Array.isArray(edu.certificate) && edu.certificate.length === 0)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Certificate file is required',
        path: ['education', index, 'certificate'],
      });
    }
  });
});

type EducationFormData = z.infer<typeof educationSchema>;

interface AddEducationFormProps {
  classId: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const AddEducationForm: React.FC<AddEducationFormProps> = ({
  classId,
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const form = useForm<EducationFormData>({
    resolver: zodResolver(educationSchema),
    defaultValues: {
      noDegree: false,
      education: [
        {
          degree: '',
          university: '',
          passoutYear: '',
          degreeType: '',
          certificate: undefined,
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'education',
  });

  const watchNoDegree = form.watch('noDegree');

  const handleNoDegreeChange = async (checked: boolean) => {
    form.setValue('noDegree', checked);

    if (checked) {
      try {
        setIsSubmitting(true);
        await addEducationByAdmin(classId, { noDegree: true }, []);
        toast.success('No education status added successfully!');
        onSuccess();
        onClose();
        form.reset();
      } catch (error: any) {
        toast.error(error.message || 'Failed to add education');
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const onSubmit = async (data: EducationFormData) => {
    if (data.noDegree) {
      return;
    }

    setIsSubmitting(true);

    try {
      const certificateFiles: File[] = [];
      data.education?.forEach((edu: any) => {
        if (edu.certificate?.[0]) {
          certificateFiles.push(edu.certificate[0]);
        }
      });

      await addEducationByAdmin(classId, { noDegree: false, education: data.education }, certificateFiles);

      onSuccess();
      onClose();
      form.reset();
      toast.success('Education records added successfully!');
    } catch (error: any) {
      toast.error(error.message || 'Failed to add education');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Education Record</DialogTitle>
          <DialogDescription>
            Add education details for this class.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form  className="space-y-6">
            {/* No Degree Checkbox */}
            <FormField
              control={form.control}
              name="noDegree"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={handleNoDegreeChange}
                    />
                  </FormControl>
                  <FormLabel>I don&apos;t have education</FormLabel>
                </FormItem>
              )}
            />

            {/* Education Records */}
            {!watchNoDegree && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Education Records</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      append({
                        degree: '',
                        university: '',
                        passoutYear: '',
                        degreeType: '',
                        certificate: undefined,
                      })
                    }
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Education
                  </Button>
                </div>

                {fields.map((field, index) => (
                  <div key={field.id} className="border border-gray-200 rounded-lg p-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Education {index + 1}</h4>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => remove(index)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`education.${index}.degree`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Degree</FormLabel>
                            <FormControl>
                              <select
                                {...field}
                                className="w-full p-2 border border-gray-300 rounded-md focus:border-black focus:ring-black bg-white text-black"
                              >
                                <option value="">Select Degree</option>
                                <option value="B.Sc Mathematics">B.Sc Mathematics</option>
                                <option value="B.Sc Computer Science">B.Sc Computer Science</option>
                                <option value="B.Com">B.Com</option>
                                <option value="BBA">BBA</option>
                                <option value="BCA">BCA</option>
                                <option value="BA">BA</option>
                                <option value="M.Sc">M.Sc</option>
                                <option value="M.Com">M.Com</option>
                                <option value="MBA">MBA</option>
                                <option value="MCA">MCA</option>
                                <option value="MA">MA</option>
                                <option value="Ph.D">Ph.D</option>
                                <option value="B.Tech">B.Tech</option>
                                <option value="M.Tech">M.Tech</option>
                              </select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`education.${index}.university`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>University</FormLabel>
                            <FormControl>
                              <input
                                {...field}
                                placeholder="Enter university"
                                className="flex h-9 w-full rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-black disabled:cursor-not-allowed disabled:opacity-50"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`education.${index}.passoutYear`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Passout Year</FormLabel>
                            <FormControl>
                              <input
                                {...field}
                                placeholder="Enter passout year"
                                className="flex h-9 w-full rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-black disabled:cursor-not-allowed disabled:opacity-50"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`education.${index}.degreeType`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Degree Type</FormLabel>
                            <FormControl>
                              <input
                                {...field}
                                placeholder="Enter degree type"
                                className="flex h-9 w-full rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-black disabled:cursor-not-allowed disabled:opacity-50"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`education.${index}.certificate`}
                        render={({ field: { onChange, name } }) => (
                          <FormItem className="md:col-span-2">
                            <FormLabel>Certificate</FormLabel>
                            <FormControl>
                              <input
                                name={name}
                                type="file"
                                accept=".pdf,.jpg,.jpeg,.png"
                                onChange={(e) => onChange(e.target.files)}
                                className="flex h-9 w-full rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-black disabled:cursor-not-allowed disabled:opacity-50"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}

            <DialogFooter className="gap-2">
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-black hover:bg-gray-800 text-white disabled:bg-gray-400"
                onClick={form.handleSubmit(onSubmit)}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Adding...
                  </>
                ) : (
                  'Add Education'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddEducationForm;
