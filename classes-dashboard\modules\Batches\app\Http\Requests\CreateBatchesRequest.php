<?php

namespace Batches\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateBatchesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules()
    {
        return [
            'batch_name'  => 'required|string|max:200',
            'classroom'     => 'required|array|min:1',
            'classroom.*'   => 'exists:classrooms,id',
            'subjects'      => 'required|array|min:1',
            'subjects.*'    => 'exists:subjects,id',
            'timeslots'     => 'required|array|min:1',
            'timeslots.*'   => 'exists:timeslots,id',
            'resource'      => 'required|exists:resources,id',
            'days'          => 'required|array|min:1',
            'days.*'        => 'in:Monday,Tuesday,Wednesday,Thursday,Friday,Saturday',
        ];
    }

    /**
     * Custom messages (optional).
     */
    public function messages()
    {
        return [
            'batches_name.required' => 'Batch name is required.',
            'classroom.required'    => 'Please select at least one classroom.',
            'subjects.required'     => 'Please select at least one subject.',
            'timeslots.required'    => 'Please select at least one timeslot.',
            'resource.required'     => 'Please select a resource.',
            'days.required'         => 'Please select at least one day.',
        ];
    }
}