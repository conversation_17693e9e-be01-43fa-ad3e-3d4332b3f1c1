{"version": 3, "file": "luxon.js", "sources": ["../../src/errors.js", "../../src/impl/formats.js", "../../src/zone.js", "../../src/zones/systemZone.js", "../../src/zones/IANAZone.js", "../../src/impl/locale.js", "../../src/zones/fixedOffsetZone.js", "../../src/zones/invalidZone.js", "../../src/impl/zoneUtil.js", "../../src/impl/digits.js", "../../src/settings.js", "../../src/impl/invalid.js", "../../src/impl/conversions.js", "../../src/impl/util.js", "../../src/impl/english.js", "../../src/impl/formatter.js", "../../src/impl/regexParser.js", "../../src/duration.js", "../../src/interval.js", "../../src/info.js", "../../src/impl/diff.js", "../../src/impl/tokenParser.js", "../../src/datetime.js", "../../src/luxon.js"], "sourcesContent": ["// these aren't really private, but nor are they really useful to document\n\n/**\n * @private\n */\nclass LuxonError extends Error {}\n\n/**\n * @private\n */\nexport class InvalidDateTimeError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid DateTime: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidIntervalError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid Interval: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidDurationError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid Duration: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class ConflictingSpecificationError extends LuxonError {}\n\n/**\n * @private\n */\nexport class InvalidUnitError extends LuxonError {\n  constructor(unit) {\n    super(`Invalid unit ${unit}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidArgumentError extends LuxonError {}\n\n/**\n * @private\n */\nexport class ZoneIsAbstractError extends LuxonError {\n  constructor() {\n    super(\"Zone is an abstract class\");\n  }\n}\n", "/**\n * @private\n */\n\nconst n = \"numeric\",\n  s = \"short\",\n  l = \"long\";\n\nexport const DATE_SHORT = {\n  year: n,\n  month: n,\n  day: n,\n};\n\nexport const DATE_MED = {\n  year: n,\n  month: s,\n  day: n,\n};\n\nexport const DATE_MED_WITH_WEEKDAY = {\n  year: n,\n  month: s,\n  day: n,\n  weekday: s,\n};\n\nexport const DATE_FULL = {\n  year: n,\n  month: l,\n  day: n,\n};\n\nexport const DATE_HUGE = {\n  year: n,\n  month: l,\n  day: n,\n  weekday: l,\n};\n\nexport const TIME_SIMPLE = {\n  hour: n,\n  minute: n,\n};\n\nexport const TIME_WITH_SECONDS = {\n  hour: n,\n  minute: n,\n  second: n,\n};\n\nexport const TIME_WITH_SHORT_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: s,\n};\n\nexport const TIME_WITH_LONG_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: l,\n};\n\nexport const TIME_24_SIMPLE = {\n  hour: n,\n  minute: n,\n  hourCycle: \"h23\",\n};\n\nexport const TIME_24_WITH_SECONDS = {\n  hour: n,\n  minute: n,\n  second: n,\n  hourCycle: \"h23\",\n};\n\nexport const TIME_24_WITH_SHORT_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  hourCycle: \"h23\",\n  timeZoneName: s,\n};\n\nexport const TIME_24_WITH_LONG_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  hourCycle: \"h23\",\n  timeZoneName: l,\n};\n\nexport const DATETIME_SHORT = {\n  year: n,\n  month: n,\n  day: n,\n  hour: n,\n  minute: n,\n};\n\nexport const DATETIME_SHORT_WITH_SECONDS = {\n  year: n,\n  month: n,\n  day: n,\n  hour: n,\n  minute: n,\n  second: n,\n};\n\nexport const DATETIME_MED = {\n  year: n,\n  month: s,\n  day: n,\n  hour: n,\n  minute: n,\n};\n\nexport const DATETIME_MED_WITH_SECONDS = {\n  year: n,\n  month: s,\n  day: n,\n  hour: n,\n  minute: n,\n  second: n,\n};\n\nexport const DATETIME_MED_WITH_WEEKDAY = {\n  year: n,\n  month: s,\n  day: n,\n  weekday: s,\n  hour: n,\n  minute: n,\n};\n\nexport const DATETIME_FULL = {\n  year: n,\n  month: l,\n  day: n,\n  hour: n,\n  minute: n,\n  timeZoneName: s,\n};\n\nexport const DATETIME_FULL_WITH_SECONDS = {\n  year: n,\n  month: l,\n  day: n,\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: s,\n};\n\nexport const DATETIME_HUGE = {\n  year: n,\n  month: l,\n  day: n,\n  weekday: l,\n  hour: n,\n  minute: n,\n  timeZoneName: l,\n};\n\nexport const DATETIME_HUGE_WITH_SECONDS = {\n  year: n,\n  month: l,\n  day: n,\n  weekday: l,\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: l,\n};\n", "import { ZoneIsAbstractError } from \"./errors.js\";\n\n/**\n * @interface\n */\nexport default class Zone {\n  /**\n   * The type of zone\n   * @abstract\n   * @type {string}\n   */\n  get type() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * The name of this zone.\n   * @abstract\n   * @type {string}\n   */\n  get name() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * The IANA name of this zone.\n   * Defaults to `name` if not overwritten by a subclass.\n   * @abstract\n   * @type {string}\n   */\n  get ianaName() {\n    return this.name;\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year.\n   * @abstract\n   * @type {boolean}\n   */\n  get isUniversal() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Returns the offset's common name (such as EST) at the specified timestamp\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to get the name\n   * @param {Object} opts - Options to affect the format\n   * @param {string} opts.format - What style of offset to return. Accepts 'long' or 'short'.\n   * @param {string} opts.locale - What locale to return the offset name in.\n   * @return {string}\n   */\n  offsetName(ts, opts) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to compute the offset\n   * @return {number}\n   */\n  offset(ts) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone\n   * @abstract\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return whether this Zone is valid.\n   * @abstract\n   * @type {boolean}\n   */\n  get isValid() {\n    throw new ZoneIsAbstractError();\n  }\n}\n", "import { formatOffset, parseZoneInfo } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nlet singleton = null;\n\n/**\n * Represents the local zone for this JavaScript environment.\n * @implements {Zone}\n */\nexport default class SystemZone extends Zone {\n  /**\n   * Get a singleton instance of the local zone\n   * @return {SystemZone}\n   */\n  static get instance() {\n    if (singleton === null) {\n      singleton = new SystemZone();\n    }\n    return singleton;\n  }\n\n  /** @override **/\n  get type() {\n    return \"system\";\n  }\n\n  /** @override **/\n  get name() {\n    return new Intl.DateTimeFormat().resolvedOptions().timeZone;\n  }\n\n  /** @override **/\n  get isUniversal() {\n    return false;\n  }\n\n  /** @override **/\n  offsetName(ts, { format, locale }) {\n    return parseZoneInfo(ts, format, locale);\n  }\n\n  /** @override **/\n  formatOffset(ts, format) {\n    return formatOffset(this.offset(ts), format);\n  }\n\n  /** @override **/\n  offset(ts) {\n    return -new Date(ts).getTimezoneOffset();\n  }\n\n  /** @override **/\n  equals(otherZone) {\n    return otherZone.type === \"system\";\n  }\n\n  /** @override **/\n  get isValid() {\n    return true;\n  }\n}\n", "import { formatOffset, parseZoneInfo, isUndefined, objToLocalTS } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nlet dtfCache = {};\nfunction makeDTF(zone) {\n  if (!dtfCache[zone]) {\n    dtfCache[zone] = new Intl.DateTimeFormat(\"en-US\", {\n      hour12: false,\n      timeZone: zone,\n      year: \"numeric\",\n      month: \"2-digit\",\n      day: \"2-digit\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n      second: \"2-digit\",\n      era: \"short\",\n    });\n  }\n  return dtfCache[zone];\n}\n\nconst typeToPos = {\n  year: 0,\n  month: 1,\n  day: 2,\n  era: 3,\n  hour: 4,\n  minute: 5,\n  second: 6,\n};\n\nfunction hackyOffset(dtf, date) {\n  const formatted = dtf.format(date).replace(/\\u200E/g, \"\"),\n    parsed = /(\\d+)\\/(\\d+)\\/(\\d+) (AD|BC),? (\\d+):(\\d+):(\\d+)/.exec(formatted),\n    [, fMonth, fDay, fYear, fadOrBc, fHour, fMinute, fSecond] = parsed;\n  return [fYear, fMonth, fDay, fadOrBc, fHour, fMinute, fSecond];\n}\n\nfunction partsOffset(dtf, date) {\n  const formatted = dtf.formatToParts(date);\n  const filled = [];\n  for (let i = 0; i < formatted.length; i++) {\n    const { type, value } = formatted[i];\n    const pos = typeToPos[type];\n\n    if (type === \"era\") {\n      filled[pos] = value;\n    } else if (!isUndefined(pos)) {\n      filled[pos] = parseInt(value, 10);\n    }\n  }\n  return filled;\n}\n\nlet ianaZoneCache = {};\n/**\n * A zone identified by an IANA identifier, like America/New_York\n * @implements {Zone}\n */\nexport default class IANAZone extends Zone {\n  /**\n   * @param {string} name - Zone name\n   * @return {IANAZone}\n   */\n  static create(name) {\n    if (!ianaZoneCache[name]) {\n      ianaZoneCache[name] = new IANAZone(name);\n    }\n    return ianaZoneCache[name];\n  }\n\n  /**\n   * Reset local caches. Should only be necessary in testing scenarios.\n   * @return {void}\n   */\n  static resetCache() {\n    ianaZoneCache = {};\n    dtfCache = {};\n  }\n\n  /**\n   * Returns whether the provided string is a valid specifier. This only checks the string's format, not that the specifier identifies a known zone; see isValidZone for that.\n   * @param {string} s - The string to check validity on\n   * @example IANAZone.isValidSpecifier(\"America/New_York\") //=> true\n   * @example IANAZone.isValidSpecifier(\"Sport~~blorp\") //=> false\n   * @deprecated For backward compatibility, this forwards to isValidZone, better use `isValidZone()` directly instead.\n   * @return {boolean}\n   */\n  static isValidSpecifier(s) {\n    return this.isValidZone(s);\n  }\n\n  /**\n   * Returns whether the provided string identifies a real zone\n   * @param {string} zone - The string to check\n   * @example IANAZone.isValidZone(\"America/New_York\") //=> true\n   * @example IANAZone.isValidZone(\"Fantasia/Castle\") //=> false\n   * @example IANAZone.isValidZone(\"Sport~~blorp\") //=> false\n   * @return {boolean}\n   */\n  static isValidZone(zone) {\n    if (!zone) {\n      return false;\n    }\n    try {\n      new Intl.DateTimeFormat(\"en-US\", { timeZone: zone }).format();\n      return true;\n    } catch (e) {\n      return false;\n    }\n  }\n\n  constructor(name) {\n    super();\n    /** @private **/\n    this.zoneName = name;\n    /** @private **/\n    this.valid = IANAZone.isValidZone(name);\n  }\n\n  /**\n   * The type of zone. `iana` for all instances of `IANAZone`.\n   * @override\n   * @type {string}\n   */\n  get type() {\n    return \"iana\";\n  }\n\n  /**\n   * The name of this zone (i.e. the IANA zone name).\n   * @override\n   * @type {string}\n   */\n  get name() {\n    return this.zoneName;\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year:\n   * Always returns false for all IANA zones.\n   * @override\n   * @type {boolean}\n   */\n  get isUniversal() {\n    return false;\n  }\n\n  /**\n   * Returns the offset's common name (such as EST) at the specified timestamp\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the name\n   * @param {Object} opts - Options to affect the format\n   * @param {string} opts.format - What style of offset to return. Accepts 'long' or 'short'.\n   * @param {string} opts.locale - What locale to return the offset name in.\n   * @return {string}\n   */\n  offsetName(ts, { format, locale }) {\n    return parseZoneInfo(ts, format, locale, this.name);\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    return formatOffset(this.offset(ts), format);\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to compute the offset\n   * @return {number}\n   */\n  offset(ts) {\n    const date = new Date(ts);\n\n    if (isNaN(date)) return NaN;\n\n    const dtf = makeDTF(this.name);\n    let [year, month, day, adOrBc, hour, minute, second] = dtf.formatToParts\n      ? partsOffset(dtf, date)\n      : hackyOffset(dtf, date);\n\n    if (adOrBc === \"BC\") {\n      year = -Math.abs(year) + 1;\n    }\n\n    // because we're using hour12 and https://bugs.chromium.org/p/chromium/issues/detail?id=1025564&can=2&q=%2224%3A00%22%20datetimeformat\n    const adjustedHour = hour === 24 ? 0 : hour;\n\n    const asUTC = objToLocalTS({\n      year,\n      month,\n      day,\n      hour: adjustedHour,\n      minute,\n      second,\n      millisecond: 0,\n    });\n\n    let asTS = +date;\n    const over = asTS % 1000;\n    asTS -= over >= 0 ? over : 1000 + over;\n    return (asUTC - asTS) / (60 * 1000);\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone\n   * @override\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    return otherZone.type === \"iana\" && otherZone.name === this.name;\n  }\n\n  /**\n   * Return whether this Zone is valid.\n   * @override\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.valid;\n  }\n}\n", "import { hasLocaleWeekInfo, hasRelative, padStart, roundTo, validateWeekSettings } from \"./util.js\";\nimport * as English from \"./english.js\";\nimport Settings from \"../settings.js\";\nimport DateTime from \"../datetime.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\n\n// todo - remap caching\n\nlet intlLFCache = {};\nfunction getCachedLF(locString, opts = {}) {\n  const key = JSON.stringify([locString, opts]);\n  let dtf = intlLFCache[key];\n  if (!dtf) {\n    dtf = new Intl.ListFormat(locString, opts);\n    intlLFCache[key] = dtf;\n  }\n  return dtf;\n}\n\nlet intlDTCache = {};\nfunction getCachedDTF(locString, opts = {}) {\n  const key = JSON.stringify([locString, opts]);\n  let dtf = intlDTCache[key];\n  if (!dtf) {\n    dtf = new Intl.DateTimeFormat(locString, opts);\n    intlDTCache[key] = dtf;\n  }\n  return dtf;\n}\n\nlet intlNumCache = {};\nfunction getCachedINF(locString, opts = {}) {\n  const key = JSON.stringify([locString, opts]);\n  let inf = intlNumCache[key];\n  if (!inf) {\n    inf = new Intl.NumberFormat(locString, opts);\n    intlNumCache[key] = inf;\n  }\n  return inf;\n}\n\nlet intlRelCache = {};\nfunction getCachedRTF(locString, opts = {}) {\n  const { base, ...cacheKeyOpts } = opts; // exclude `base` from the options\n  const key = JSON.stringify([locString, cacheKeyOpts]);\n  let inf = intlRelCache[key];\n  if (!inf) {\n    inf = new Intl.RelativeTimeFormat(locString, opts);\n    intlRelCache[key] = inf;\n  }\n  return inf;\n}\n\nlet sysLocaleCache = null;\nfunction systemLocale() {\n  if (sysLocaleCache) {\n    return sysLocaleCache;\n  } else {\n    sysLocaleCache = new Intl.DateTimeFormat().resolvedOptions().locale;\n    return sysLocaleCache;\n  }\n}\n\nlet weekInfoCache = {};\nfunction getCachedWeekInfo(locString) {\n  let data = weekInfoCache[locString];\n  if (!data) {\n    const locale = new Intl.Locale(locString);\n    // browsers currently implement this as a property, but spec says it should be a getter function\n    data = \"getWeekInfo\" in locale ? locale.getWeekInfo() : locale.weekInfo;\n    weekInfoCache[locString] = data;\n  }\n  return data;\n}\n\nfunction parseLocaleString(localeStr) {\n  // I really want to avoid writing a BCP 47 parser\n  // see, e.g. https://github.com/wooorm/bcp-47\n  // Instead, we'll do this:\n\n  // a) if the string has no -u extensions, just leave it alone\n  // b) if it does, use Intl to resolve everything\n  // c) if Intl fails, try again without the -u\n\n  // private subtags and unicode subtags have ordering requirements,\n  // and we're not properly parsing this, so just strip out the\n  // private ones if they exist.\n  const xIndex = localeStr.indexOf(\"-x-\");\n  if (xIndex !== -1) {\n    localeStr = localeStr.substring(0, xIndex);\n  }\n\n  const uIndex = localeStr.indexOf(\"-u-\");\n  if (uIndex === -1) {\n    return [localeStr];\n  } else {\n    let options;\n    let selectedStr;\n    try {\n      options = getCachedDTF(localeStr).resolvedOptions();\n      selectedStr = localeStr;\n    } catch (e) {\n      const smaller = localeStr.substring(0, uIndex);\n      options = getCachedDTF(smaller).resolvedOptions();\n      selectedStr = smaller;\n    }\n\n    const { numberingSystem, calendar } = options;\n    return [selectedStr, numberingSystem, calendar];\n  }\n}\n\nfunction intlConfigString(localeStr, numberingSystem, outputCalendar) {\n  if (outputCalendar || numberingSystem) {\n    if (!localeStr.includes(\"-u-\")) {\n      localeStr += \"-u\";\n    }\n\n    if (outputCalendar) {\n      localeStr += `-ca-${outputCalendar}`;\n    }\n\n    if (numberingSystem) {\n      localeStr += `-nu-${numberingSystem}`;\n    }\n    return localeStr;\n  } else {\n    return localeStr;\n  }\n}\n\nfunction mapMonths(f) {\n  const ms = [];\n  for (let i = 1; i <= 12; i++) {\n    const dt = DateTime.utc(2009, i, 1);\n    ms.push(f(dt));\n  }\n  return ms;\n}\n\nfunction mapWeekdays(f) {\n  const ms = [];\n  for (let i = 1; i <= 7; i++) {\n    const dt = DateTime.utc(2016, 11, 13 + i);\n    ms.push(f(dt));\n  }\n  return ms;\n}\n\nfunction listStuff(loc, length, englishFn, intlFn) {\n  const mode = loc.listingMode();\n\n  if (mode === \"error\") {\n    return null;\n  } else if (mode === \"en\") {\n    return englishFn(length);\n  } else {\n    return intlFn(length);\n  }\n}\n\nfunction supportsFastNumbers(loc) {\n  if (loc.numberingSystem && loc.numberingSystem !== \"latn\") {\n    return false;\n  } else {\n    return (\n      loc.numberingSystem === \"latn\" ||\n      !loc.locale ||\n      loc.locale.startsWith(\"en\") ||\n      new Intl.DateTimeFormat(loc.intl).resolvedOptions().numberingSystem === \"latn\"\n    );\n  }\n}\n\n/**\n * @private\n */\n\nclass PolyNumberFormatter {\n  constructor(intl, forceSimple, opts) {\n    this.padTo = opts.padTo || 0;\n    this.floor = opts.floor || false;\n\n    const { padTo, floor, ...otherOpts } = opts;\n\n    if (!forceSimple || Object.keys(otherOpts).length > 0) {\n      const intlOpts = { useGrouping: false, ...opts };\n      if (opts.padTo > 0) intlOpts.minimumIntegerDigits = opts.padTo;\n      this.inf = getCachedINF(intl, intlOpts);\n    }\n  }\n\n  format(i) {\n    if (this.inf) {\n      const fixed = this.floor ? Math.floor(i) : i;\n      return this.inf.format(fixed);\n    } else {\n      // to match the browser's numberformatter defaults\n      const fixed = this.floor ? Math.floor(i) : roundTo(i, 3);\n      return padStart(fixed, this.padTo);\n    }\n  }\n}\n\n/**\n * @private\n */\n\nclass PolyDateFormatter {\n  constructor(dt, intl, opts) {\n    this.opts = opts;\n    this.originalZone = undefined;\n\n    let z = undefined;\n    if (this.opts.timeZone) {\n      // Don't apply any workarounds if a timeZone is explicitly provided in opts\n      this.dt = dt;\n    } else if (dt.zone.type === \"fixed\") {\n      // UTC-8 or Etc/UTC-8 are not part of tzdata, only Etc/GMT+8 and the like.\n      // That is why fixed-offset TZ is set to that unless it is:\n      // 1. Representing offset 0 when UTC is used to maintain previous behavior and does not become GMT.\n      // 2. Unsupported by the browser:\n      //    - some do not support Etc/\n      //    - < Etc/GMT-14, > Etc/GMT+12, and 30-minute or 45-minute offsets are not part of tzdata\n      const gmtOffset = -1 * (dt.offset / 60);\n      const offsetZ = gmtOffset >= 0 ? `Etc/GMT+${gmtOffset}` : `Etc/GMT${gmtOffset}`;\n      if (dt.offset !== 0 && IANAZone.create(offsetZ).valid) {\n        z = offsetZ;\n        this.dt = dt;\n      } else {\n        // Not all fixed-offset zones like Etc/+4:30 are present in tzdata so\n        // we manually apply the offset and substitute the zone as needed.\n        z = \"UTC\";\n        this.dt = dt.offset === 0 ? dt : dt.setZone(\"UTC\").plus({ minutes: dt.offset });\n        this.originalZone = dt.zone;\n      }\n    } else if (dt.zone.type === \"system\") {\n      this.dt = dt;\n    } else if (dt.zone.type === \"iana\") {\n      this.dt = dt;\n      z = dt.zone.name;\n    } else {\n      // Custom zones can have any offset / offsetName so we just manually\n      // apply the offset and substitute the zone as needed.\n      z = \"UTC\";\n      this.dt = dt.setZone(\"UTC\").plus({ minutes: dt.offset });\n      this.originalZone = dt.zone;\n    }\n\n    const intlOpts = { ...this.opts };\n    intlOpts.timeZone = intlOpts.timeZone || z;\n    this.dtf = getCachedDTF(intl, intlOpts);\n  }\n\n  format() {\n    if (this.originalZone) {\n      // If we have to substitute in the actual zone name, we have to use\n      // formatToParts so that the timezone can be replaced.\n      return this.formatToParts()\n        .map(({ value }) => value)\n        .join(\"\");\n    }\n    return this.dtf.format(this.dt.toJSDate());\n  }\n\n  formatToParts() {\n    const parts = this.dtf.formatToParts(this.dt.toJSDate());\n    if (this.originalZone) {\n      return parts.map((part) => {\n        if (part.type === \"timeZoneName\") {\n          const offsetName = this.originalZone.offsetName(this.dt.ts, {\n            locale: this.dt.locale,\n            format: this.opts.timeZoneName,\n          });\n          return {\n            ...part,\n            value: offsetName,\n          };\n        } else {\n          return part;\n        }\n      });\n    }\n    return parts;\n  }\n\n  resolvedOptions() {\n    return this.dtf.resolvedOptions();\n  }\n}\n\n/**\n * @private\n */\nclass PolyRelFormatter {\n  constructor(intl, isEnglish, opts) {\n    this.opts = { style: \"long\", ...opts };\n    if (!isEnglish && hasRelative()) {\n      this.rtf = getCachedRTF(intl, opts);\n    }\n  }\n\n  format(count, unit) {\n    if (this.rtf) {\n      return this.rtf.format(count, unit);\n    } else {\n      return English.formatRelativeTime(unit, count, this.opts.numeric, this.opts.style !== \"long\");\n    }\n  }\n\n  formatToParts(count, unit) {\n    if (this.rtf) {\n      return this.rtf.formatToParts(count, unit);\n    } else {\n      return [];\n    }\n  }\n}\n\nconst fallbackWeekSettings = {\n  firstDay: 1,\n  minimalDays: 4,\n  weekend: [6, 7],\n};\n\n/**\n * @private\n */\n\nexport default class Locale {\n  static fromOpts(opts) {\n    return Locale.create(\n      opts.locale,\n      opts.numberingSystem,\n      opts.outputCalendar,\n      opts.weekSettings,\n      opts.defaultToEN\n    );\n  }\n\n  static create(locale, numberingSystem, outputCalendar, weekSettings, defaultToEN = false) {\n    const specifiedLocale = locale || Settings.defaultLocale;\n    // the system locale is useful for human-readable strings but annoying for parsing/formatting known formats\n    const localeR = specifiedLocale || (defaultToEN ? \"en-US\" : systemLocale());\n    const numberingSystemR = numberingSystem || Settings.defaultNumberingSystem;\n    const outputCalendarR = outputCalendar || Settings.defaultOutputCalendar;\n    const weekSettingsR = validateWeekSettings(weekSettings) || Settings.defaultWeekSettings;\n    return new Locale(localeR, numberingSystemR, outputCalendarR, weekSettingsR, specifiedLocale);\n  }\n\n  static resetCache() {\n    sysLocaleCache = null;\n    intlDTCache = {};\n    intlNumCache = {};\n    intlRelCache = {};\n  }\n\n  static fromObject({ locale, numberingSystem, outputCalendar, weekSettings } = {}) {\n    return Locale.create(locale, numberingSystem, outputCalendar, weekSettings);\n  }\n\n  constructor(locale, numbering, outputCalendar, weekSettings, specifiedLocale) {\n    const [parsedLocale, parsedNumberingSystem, parsedOutputCalendar] = parseLocaleString(locale);\n\n    this.locale = parsedLocale;\n    this.numberingSystem = numbering || parsedNumberingSystem || null;\n    this.outputCalendar = outputCalendar || parsedOutputCalendar || null;\n    this.weekSettings = weekSettings;\n    this.intl = intlConfigString(this.locale, this.numberingSystem, this.outputCalendar);\n\n    this.weekdaysCache = { format: {}, standalone: {} };\n    this.monthsCache = { format: {}, standalone: {} };\n    this.meridiemCache = null;\n    this.eraCache = {};\n\n    this.specifiedLocale = specifiedLocale;\n    this.fastNumbersCached = null;\n  }\n\n  get fastNumbers() {\n    if (this.fastNumbersCached == null) {\n      this.fastNumbersCached = supportsFastNumbers(this);\n    }\n\n    return this.fastNumbersCached;\n  }\n\n  listingMode() {\n    const isActuallyEn = this.isEnglish();\n    const hasNoWeirdness =\n      (this.numberingSystem === null || this.numberingSystem === \"latn\") &&\n      (this.outputCalendar === null || this.outputCalendar === \"gregory\");\n    return isActuallyEn && hasNoWeirdness ? \"en\" : \"intl\";\n  }\n\n  clone(alts) {\n    if (!alts || Object.getOwnPropertyNames(alts).length === 0) {\n      return this;\n    } else {\n      return Locale.create(\n        alts.locale || this.specifiedLocale,\n        alts.numberingSystem || this.numberingSystem,\n        alts.outputCalendar || this.outputCalendar,\n        validateWeekSettings(alts.weekSettings) || this.weekSettings,\n        alts.defaultToEN || false\n      );\n    }\n  }\n\n  redefaultToEN(alts = {}) {\n    return this.clone({ ...alts, defaultToEN: true });\n  }\n\n  redefaultToSystem(alts = {}) {\n    return this.clone({ ...alts, defaultToEN: false });\n  }\n\n  months(length, format = false) {\n    return listStuff(this, length, English.months, () => {\n      const intl = format ? { month: length, day: \"numeric\" } : { month: length },\n        formatStr = format ? \"format\" : \"standalone\";\n      if (!this.monthsCache[formatStr][length]) {\n        this.monthsCache[formatStr][length] = mapMonths((dt) => this.extract(dt, intl, \"month\"));\n      }\n      return this.monthsCache[formatStr][length];\n    });\n  }\n\n  weekdays(length, format = false) {\n    return listStuff(this, length, English.weekdays, () => {\n      const intl = format\n          ? { weekday: length, year: \"numeric\", month: \"long\", day: \"numeric\" }\n          : { weekday: length },\n        formatStr = format ? \"format\" : \"standalone\";\n      if (!this.weekdaysCache[formatStr][length]) {\n        this.weekdaysCache[formatStr][length] = mapWeekdays((dt) =>\n          this.extract(dt, intl, \"weekday\")\n        );\n      }\n      return this.weekdaysCache[formatStr][length];\n    });\n  }\n\n  meridiems() {\n    return listStuff(\n      this,\n      undefined,\n      () => English.meridiems,\n      () => {\n        // In theory there could be aribitrary day periods. We're gonna assume there are exactly two\n        // for AM and PM. This is probably wrong, but it's makes parsing way easier.\n        if (!this.meridiemCache) {\n          const intl = { hour: \"numeric\", hourCycle: \"h12\" };\n          this.meridiemCache = [DateTime.utc(2016, 11, 13, 9), DateTime.utc(2016, 11, 13, 19)].map(\n            (dt) => this.extract(dt, intl, \"dayperiod\")\n          );\n        }\n\n        return this.meridiemCache;\n      }\n    );\n  }\n\n  eras(length) {\n    return listStuff(this, length, English.eras, () => {\n      const intl = { era: length };\n\n      // This is problematic. Different calendars are going to define eras totally differently. What I need is the minimum set of dates\n      // to definitely enumerate them.\n      if (!this.eraCache[length]) {\n        this.eraCache[length] = [DateTime.utc(-40, 1, 1), DateTime.utc(2017, 1, 1)].map((dt) =>\n          this.extract(dt, intl, \"era\")\n        );\n      }\n\n      return this.eraCache[length];\n    });\n  }\n\n  extract(dt, intlOpts, field) {\n    const df = this.dtFormatter(dt, intlOpts),\n      results = df.formatToParts(),\n      matching = results.find((m) => m.type.toLowerCase() === field);\n    return matching ? matching.value : null;\n  }\n\n  numberFormatter(opts = {}) {\n    // this forcesimple option is never used (the only caller short-circuits on it, but it seems safer to leave)\n    // (in contrast, the rest of the condition is used heavily)\n    return new PolyNumberFormatter(this.intl, opts.forceSimple || this.fastNumbers, opts);\n  }\n\n  dtFormatter(dt, intlOpts = {}) {\n    return new PolyDateFormatter(dt, this.intl, intlOpts);\n  }\n\n  relFormatter(opts = {}) {\n    return new PolyRelFormatter(this.intl, this.isEnglish(), opts);\n  }\n\n  listFormatter(opts = {}) {\n    return getCachedLF(this.intl, opts);\n  }\n\n  isEnglish() {\n    return (\n      this.locale === \"en\" ||\n      this.locale.toLowerCase() === \"en-us\" ||\n      new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith(\"en-us\")\n    );\n  }\n\n  getWeekSettings() {\n    if (this.weekSettings) {\n      return this.weekSettings;\n    } else if (!hasLocaleWeekInfo()) {\n      return fallbackWeekSettings;\n    } else {\n      return getCachedWeekInfo(this.locale);\n    }\n  }\n\n  getStartOfWeek() {\n    return this.getWeekSettings().firstDay;\n  }\n\n  getMinDaysInFirstWeek() {\n    return this.getWeekSettings().minimalDays;\n  }\n\n  getWeekendDays() {\n    return this.getWeekSettings().weekend;\n  }\n\n  equals(other) {\n    return (\n      this.locale === other.locale &&\n      this.numberingSystem === other.numberingSystem &&\n      this.outputCalendar === other.outputCalendar\n    );\n  }\n\n  toString() {\n    return `Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`;\n  }\n}\n", "import { formatOffset, signedOffset } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nlet singleton = null;\n\n/**\n * A zone with a fixed offset (meaning no DST)\n * @implements {Zone}\n */\nexport default class FixedOffsetZone extends Zone {\n  /**\n   * Get a singleton instance of UTC\n   * @return {FixedOffsetZone}\n   */\n  static get utcInstance() {\n    if (singleton === null) {\n      singleton = new FixedOffsetZone(0);\n    }\n    return singleton;\n  }\n\n  /**\n   * Get an instance with a specified offset\n   * @param {number} offset - The offset in minutes\n   * @return {FixedOffsetZone}\n   */\n  static instance(offset) {\n    return offset === 0 ? FixedOffsetZone.utcInstance : new FixedOffsetZone(offset);\n  }\n\n  /**\n   * Get an instance of FixedOffsetZone from a UTC offset string, like \"UTC+6\"\n   * @param {string} s - The offset string to parse\n   * @example FixedOffsetZone.parseSpecifier(\"UTC+6\")\n   * @example FixedOffsetZone.parseSpecifier(\"UTC+06\")\n   * @example FixedOffsetZone.parseSpecifier(\"UTC-6:00\")\n   * @return {FixedOffsetZone}\n   */\n  static parseSpecifier(s) {\n    if (s) {\n      const r = s.match(/^utc(?:([+-]\\d{1,2})(?::(\\d{2}))?)?$/i);\n      if (r) {\n        return new FixedOffsetZone(signedOffset(r[1], r[2]));\n      }\n    }\n    return null;\n  }\n\n  constructor(offset) {\n    super();\n    /** @private **/\n    this.fixed = offset;\n  }\n\n  /**\n   * The type of zone. `fixed` for all instances of `FixedOffsetZone`.\n   * @override\n   * @type {string}\n   */\n  get type() {\n    return \"fixed\";\n  }\n\n  /**\n   * The name of this zone.\n   * All fixed zones' names always start with \"UTC\" (plus optional offset)\n   * @override\n   * @type {string}\n   */\n  get name() {\n    return this.fixed === 0 ? \"UTC\" : `UTC${formatOffset(this.fixed, \"narrow\")}`;\n  }\n\n  /**\n   * The IANA name of this zone, i.e. `Etc/UTC` or `Etc/GMT+/-nn`\n   *\n   * @override\n   * @type {string}\n   */\n  get ianaName() {\n    if (this.fixed === 0) {\n      return \"Etc/UTC\";\n    } else {\n      return `Etc/GMT${formatOffset(-this.fixed, \"narrow\")}`;\n    }\n  }\n\n  /**\n   * Returns the offset's common name at the specified timestamp.\n   *\n   * For fixed offset zones this equals to the zone name.\n   * @override\n   */\n  offsetName() {\n    return this.name;\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    return formatOffset(this.fixed, format);\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year:\n   * Always returns true for all fixed offset zones.\n   * @override\n   * @type {boolean}\n   */\n  get isUniversal() {\n    return true;\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   *\n   * For fixed offset zones, this is constant and does not depend on a timestamp.\n   * @override\n   * @return {number}\n   */\n  offset() {\n    return this.fixed;\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone (i.e. also fixed and same offset)\n   * @override\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    return otherZone.type === \"fixed\" && otherZone.fixed === this.fixed;\n  }\n\n  /**\n   * Return whether this Zone is valid:\n   * All fixed offset zones are valid.\n   * @override\n   * @type {boolean}\n   */\n  get isValid() {\n    return true;\n  }\n}\n", "import Zone from \"../zone.js\";\n\n/**\n * A zone that failed to parse. You should never need to instantiate this.\n * @implements {Zone}\n */\nexport default class InvalidZone extends Zone {\n  constructor(zoneName) {\n    super();\n    /**  @private */\n    this.zoneName = zoneName;\n  }\n\n  /** @override **/\n  get type() {\n    return \"invalid\";\n  }\n\n  /** @override **/\n  get name() {\n    return this.zoneName;\n  }\n\n  /** @override **/\n  get isUniversal() {\n    return false;\n  }\n\n  /** @override **/\n  offsetName() {\n    return null;\n  }\n\n  /** @override **/\n  formatOffset() {\n    return \"\";\n  }\n\n  /** @override **/\n  offset() {\n    return NaN;\n  }\n\n  /** @override **/\n  equals() {\n    return false;\n  }\n\n  /** @override **/\n  get isValid() {\n    return false;\n  }\n}\n", "/**\n * @private\n */\n\nimport Zone from \"../zone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport InvalidZone from \"../zones/invalidZone.js\";\n\nimport { isUndefined, isString, isNumber } from \"./util.js\";\nimport SystemZone from \"../zones/systemZone.js\";\n\nexport function normalizeZone(input, defaultZone) {\n  let offset;\n  if (isUndefined(input) || input === null) {\n    return defaultZone;\n  } else if (input instanceof Zone) {\n    return input;\n  } else if (isString(input)) {\n    const lowered = input.toLowerCase();\n    if (lowered === \"default\") return defaultZone;\n    else if (lowered === \"local\" || lowered === \"system\") return SystemZone.instance;\n    else if (lowered === \"utc\" || lowered === \"gmt\") return FixedOffsetZone.utcInstance;\n    else return FixedOffsetZone.parseSpecifier(lowered) || IANAZone.create(input);\n  } else if (isNumber(input)) {\n    return FixedOffsetZone.instance(input);\n  } else if (typeof input === \"object\" && \"offset\" in input && typeof input.offset === \"function\") {\n    // This is dumb, but the instanceof check above doesn't seem to really work\n    // so we're duck checking it\n    return input;\n  } else {\n    return new InvalidZone(input);\n  }\n}\n", "const numberingSystems = {\n  arab: \"[\\u0660-\\u0669]\",\n  arabext: \"[\\u06F0-\\u06F9]\",\n  bali: \"[\\u1B50-\\u1B59]\",\n  beng: \"[\\u09E6-\\u09EF]\",\n  deva: \"[\\u0966-\\u096F]\",\n  fullwide: \"[\\uFF10-\\uFF19]\",\n  gujr: \"[\\u0AE6-\\u0AEF]\",\n  hanidec: \"[〇|一|二|三|四|五|六|七|八|九]\",\n  khmr: \"[\\u17E0-\\u17E9]\",\n  knda: \"[\\u0CE6-\\u0CEF]\",\n  laoo: \"[\\u0ED0-\\u0ED9]\",\n  limb: \"[\\u1946-\\u194F]\",\n  mlym: \"[\\u0D66-\\u0D6F]\",\n  mong: \"[\\u1810-\\u1819]\",\n  mymr: \"[\\u1040-\\u1049]\",\n  orya: \"[\\u0B66-\\u0B6F]\",\n  tamldec: \"[\\u0BE6-\\u0BEF]\",\n  telu: \"[\\u0C66-\\u0C6F]\",\n  thai: \"[\\u0E50-\\u0E59]\",\n  tibt: \"[\\u0F20-\\u0F29]\",\n  latn: \"\\\\d\",\n};\n\nconst numberingSystemsUTF16 = {\n  arab: [1632, 1641],\n  arabext: [1776, 1785],\n  bali: [6992, 7001],\n  beng: [2534, 2543],\n  deva: [2406, 2415],\n  fullwide: [65296, 65303],\n  gujr: [2790, 2799],\n  khmr: [6112, 6121],\n  knda: [3302, 3311],\n  laoo: [3792, 3801],\n  limb: [6470, 6479],\n  mlym: [3430, 3439],\n  mong: [6160, 6169],\n  mymr: [4160, 4169],\n  orya: [2918, 2927],\n  tamldec: [3046, 3055],\n  telu: [3174, 3183],\n  thai: [3664, 3673],\n  tibt: [3872, 3881],\n};\n\nconst hanidecChars = numberingSystems.hanidec.replace(/[\\[|\\]]/g, \"\").split(\"\");\n\nexport function parseDigits(str) {\n  let value = parseInt(str, 10);\n  if (isNaN(value)) {\n    value = \"\";\n    for (let i = 0; i < str.length; i++) {\n      const code = str.charCodeAt(i);\n\n      if (str[i].search(numberingSystems.hanidec) !== -1) {\n        value += hanidecChars.indexOf(str[i]);\n      } else {\n        for (const key in numberingSystemsUTF16) {\n          const [min, max] = numberingSystemsUTF16[key];\n          if (code >= min && code <= max) {\n            value += code - min;\n          }\n        }\n      }\n    }\n    return parseInt(value, 10);\n  } else {\n    return value;\n  }\n}\n\n// cache of {numberingSystem: {append: regex}}\nlet digitRegexCache = {};\nexport function resetDigitRegexCache() {\n  digitRegexCache = {};\n}\n\nexport function digitRegex({ numberingSystem }, append = \"\") {\n  const ns = numberingSystem || \"latn\";\n\n  if (!digitRegexCache[ns]) {\n    digitRegexCache[ns] = {};\n  }\n  if (!digitRegexCache[ns][append]) {\n    digitRegexCache[ns][append] = new RegExp(`${numberingSystems[ns]}${append}`);\n  }\n\n  return digitRegexCache[ns][append];\n}\n", "import SystemZone from \"./zones/systemZone.js\";\nimport IANAZone from \"./zones/IANAZone.js\";\nimport Locale from \"./impl/locale.js\";\nimport DateTime from \"./datetime.js\";\n\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\nimport { validateWeekSettings } from \"./impl/util.js\";\nimport { resetDigitRegexCache } from \"./impl/digits.js\";\n\nlet now = () => Date.now(),\n  defaultZone = \"system\",\n  defaultLocale = null,\n  defaultNumberingSystem = null,\n  defaultOutputCalendar = null,\n  twoDigitCutoffYear = 60,\n  throwOnInvalid,\n  defaultWeekSettings = null;\n\n/**\n * Settings contains static getters and setters that control <PERSON><PERSON>'s overall behavior. Luxon is a simple library with few options, but the ones it does have live here.\n */\nexport default class Settings {\n  /**\n   * Get the callback for returning the current timestamp.\n   * @type {function}\n   */\n  static get now() {\n    return now;\n  }\n\n  /**\n   * Set the callback for returning the current timestamp.\n   * The function should return a number, which will be interpreted as an Epoch millisecond count\n   * @type {function}\n   * @example Settings.now = () => Date.now() + 3000 // pretend it is 3 seconds in the future\n   * @example Settings.now = () => 0 // always pretend it's Jan 1, 1970 at midnight in UTC time\n   */\n  static set now(n) {\n    now = n;\n  }\n\n  /**\n   * Set the default time zone to create DateTimes in. Does not affect existing instances.\n   * Use the value \"system\" to reset this value to the system's time zone.\n   * @type {string}\n   */\n  static set defaultZone(zone) {\n    defaultZone = zone;\n  }\n\n  /**\n   * Get the default time zone object currently used to create DateTimes. Does not affect existing instances.\n   * The default value is the system's time zone (the one set on the machine that runs this code).\n   * @type {Zone}\n   */\n  static get defaultZone() {\n    return normalizeZone(defaultZone, SystemZone.instance);\n  }\n\n  /**\n   * Get the default locale to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static get defaultLocale() {\n    return defaultLocale;\n  }\n\n  /**\n   * Set the default locale to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static set defaultLocale(locale) {\n    defaultLocale = locale;\n  }\n\n  /**\n   * Get the default numbering system to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static get defaultNumberingSystem() {\n    return defaultNumberingSystem;\n  }\n\n  /**\n   * Set the default numbering system to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static set defaultNumberingSystem(numberingSystem) {\n    defaultNumberingSystem = numberingSystem;\n  }\n\n  /**\n   * Get the default output calendar to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static get defaultOutputCalendar() {\n    return defaultOutputCalendar;\n  }\n\n  /**\n   * Set the default output calendar to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static set defaultOutputCalendar(outputCalendar) {\n    defaultOutputCalendar = outputCalendar;\n  }\n\n  /**\n   * @typedef {Object} WeekSettings\n   * @property {number} firstDay\n   * @property {number} minimalDays\n   * @property {number[]} weekend\n   */\n\n  /**\n   * @return {WeekSettings|null}\n   */\n  static get defaultWeekSettings() {\n    return defaultWeekSettings;\n  }\n\n  /**\n   * Allows overriding the default locale week settings, i.e. the start of the week, the weekend and\n   * how many days are required in the first week of a year.\n   * Does not affect existing instances.\n   *\n   * @param {WeekSettings|null} weekSettings\n   */\n  static set defaultWeekSettings(weekSettings) {\n    defaultWeekSettings = validateWeekSettings(weekSettings);\n  }\n\n  /**\n   * Get the cutoff year for whether a 2-digit year string is interpreted in the current or previous century. Numbers higher than the cutoff will be considered to mean 19xx and numbers lower or equal to the cutoff will be considered 20xx.\n   * @type {number}\n   */\n  static get twoDigitCutoffYear() {\n    return twoDigitCutoffYear;\n  }\n\n  /**\n   * Set the cutoff year for whether a 2-digit year string is interpreted in the current or previous century. Numbers higher than the cutoff will be considered to mean 19xx and numbers lower or equal to the cutoff will be considered 20xx.\n   * @type {number}\n   * @example Settings.twoDigitCutoffYear = 0 // all 'yy' are interpreted as 20th century\n   * @example Settings.twoDigitCutoffYear = 99 // all 'yy' are interpreted as 21st century\n   * @example Settings.twoDigitCutoffYear = 50 // '49' -> 2049; '50' -> 1950\n   * @example Settings.twoDigitCutoffYear = 1950 // interpreted as 50\n   * @example Settings.twoDigitCutoffYear = 2050 // ALSO interpreted as 50\n   */\n  static set twoDigitCutoffYear(cutoffYear) {\n    twoDigitCutoffYear = cutoffYear % 100;\n  }\n\n  /**\n   * Get whether Luxon will throw when it encounters invalid DateTimes, Durations, or Intervals\n   * @type {boolean}\n   */\n  static get throwOnInvalid() {\n    return throwOnInvalid;\n  }\n\n  /**\n   * Set whether Luxon will throw when it encounters invalid DateTimes, Durations, or Intervals\n   * @type {boolean}\n   */\n  static set throwOnInvalid(t) {\n    throwOnInvalid = t;\n  }\n\n  /**\n   * Reset Luxon's global caches. Should only be necessary in testing scenarios.\n   * @return {void}\n   */\n  static resetCaches() {\n    Locale.resetCache();\n    IANAZone.resetCache();\n    DateTime.resetCache();\n    resetDigitRegexCache();\n  }\n}\n", "export default class Invalid {\n  constructor(reason, explanation) {\n    this.reason = reason;\n    this.explanation = explanation;\n  }\n\n  toMessage() {\n    if (this.explanation) {\n      return `${this.reason}: ${this.explanation}`;\n    } else {\n      return this.reason;\n    }\n  }\n}\n", "import {\n  integerBetween,\n  isLeapYear,\n  timeObject,\n  daysInYear,\n  daysInMonth,\n  weeksInWeekYear,\n  isInteger,\n  isUndefined,\n} from \"./util.js\";\nimport Invalid from \"./invalid.js\";\nimport { ConflictingSpecificationError } from \"../errors.js\";\n\nconst nonLeapLadder = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334],\n  leapLadder = [0, 31, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335];\n\nfunction unitOutOfRange(unit, value) {\n  return new Invalid(\n    \"unit out of range\",\n    `you specified ${value} (of type ${typeof value}) as a ${unit}, which is invalid`\n  );\n}\n\nexport function dayOfWeek(year, month, day) {\n  const d = new Date(Date.UTC(year, month - 1, day));\n\n  if (year < 100 && year >= 0) {\n    d.setUTCFullYear(d.getUTCFullYear() - 1900);\n  }\n\n  const js = d.getUTCDay();\n\n  return js === 0 ? 7 : js;\n}\n\nfunction computeOrdinal(year, month, day) {\n  return day + (isLeapYear(year) ? leapLadder : nonLeapLadder)[month - 1];\n}\n\nfunction uncomputeOrdinal(year, ordinal) {\n  const table = isLeapYear(year) ? leapLadder : nonLeapLadder,\n    month0 = table.findIndex((i) => i < ordinal),\n    day = ordinal - table[month0];\n  return { month: month0 + 1, day };\n}\n\nexport function isoWeekdayToLocal(isoWeekday, startOfWeek) {\n  return ((isoWeekday - startOfWeek + 7) % 7) + 1;\n}\n\n/**\n * @private\n */\n\nexport function gregorianToWeek(gregObj, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const { year, month, day } = gregObj,\n    ordinal = computeOrdinal(year, month, day),\n    weekday = isoWeekdayToLocal(dayOfWeek(year, month, day), startOfWeek);\n\n  let weekNumber = Math.floor((ordinal - weekday + 14 - minDaysInFirstWeek) / 7),\n    weekYear;\n\n  if (weekNumber < 1) {\n    weekYear = year - 1;\n    weekNumber = weeksInWeekYear(weekYear, minDaysInFirstWeek, startOfWeek);\n  } else if (weekNumber > weeksInWeekYear(year, minDaysInFirstWeek, startOfWeek)) {\n    weekYear = year + 1;\n    weekNumber = 1;\n  } else {\n    weekYear = year;\n  }\n\n  return { weekYear, weekNumber, weekday, ...timeObject(gregObj) };\n}\n\nexport function weekToGregorian(weekData, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const { weekYear, weekNumber, weekday } = weekData,\n    weekdayOfJan4 = isoWeekdayToLocal(dayOfWeek(weekYear, 1, minDaysInFirstWeek), startOfWeek),\n    yearInDays = daysInYear(weekYear);\n\n  let ordinal = weekNumber * 7 + weekday - weekdayOfJan4 - 7 + minDaysInFirstWeek,\n    year;\n\n  if (ordinal < 1) {\n    year = weekYear - 1;\n    ordinal += daysInYear(year);\n  } else if (ordinal > yearInDays) {\n    year = weekYear + 1;\n    ordinal -= daysInYear(weekYear);\n  } else {\n    year = weekYear;\n  }\n\n  const { month, day } = uncomputeOrdinal(year, ordinal);\n  return { year, month, day, ...timeObject(weekData) };\n}\n\nexport function gregorianToOrdinal(gregData) {\n  const { year, month, day } = gregData;\n  const ordinal = computeOrdinal(year, month, day);\n  return { year, ordinal, ...timeObject(gregData) };\n}\n\nexport function ordinalToGregorian(ordinalData) {\n  const { year, ordinal } = ordinalData;\n  const { month, day } = uncomputeOrdinal(year, ordinal);\n  return { year, month, day, ...timeObject(ordinalData) };\n}\n\n/**\n * Check if local week units like localWeekday are used in obj.\n * If so, validates that they are not mixed with ISO week units and then copies them to the normal week unit properties.\n * Modifies obj in-place!\n * @param obj the object values\n */\nexport function usesLocalWeekValues(obj, loc) {\n  const hasLocaleWeekData =\n    !isUndefined(obj.localWeekday) ||\n    !isUndefined(obj.localWeekNumber) ||\n    !isUndefined(obj.localWeekYear);\n  if (hasLocaleWeekData) {\n    const hasIsoWeekData =\n      !isUndefined(obj.weekday) || !isUndefined(obj.weekNumber) || !isUndefined(obj.weekYear);\n\n    if (hasIsoWeekData) {\n      throw new ConflictingSpecificationError(\n        \"Cannot mix locale-based week fields with ISO-based week fields\"\n      );\n    }\n    if (!isUndefined(obj.localWeekday)) obj.weekday = obj.localWeekday;\n    if (!isUndefined(obj.localWeekNumber)) obj.weekNumber = obj.localWeekNumber;\n    if (!isUndefined(obj.localWeekYear)) obj.weekYear = obj.localWeekYear;\n    delete obj.localWeekday;\n    delete obj.localWeekNumber;\n    delete obj.localWeekYear;\n    return {\n      minDaysInFirstWeek: loc.getMinDaysInFirstWeek(),\n      startOfWeek: loc.getStartOfWeek(),\n    };\n  } else {\n    return { minDaysInFirstWeek: 4, startOfWeek: 1 };\n  }\n}\n\nexport function hasInvalidWeekData(obj, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const validYear = isInteger(obj.weekYear),\n    validWeek = integerBetween(\n      obj.weekNumber,\n      1,\n      weeksInWeekYear(obj.weekYear, minDaysInFirstWeek, startOfWeek)\n    ),\n    validWeekday = integerBetween(obj.weekday, 1, 7);\n\n  if (!validYear) {\n    return unitOutOfRange(\"weekYear\", obj.weekYear);\n  } else if (!validWeek) {\n    return unitOutOfRange(\"week\", obj.weekNumber);\n  } else if (!validWeekday) {\n    return unitOutOfRange(\"weekday\", obj.weekday);\n  } else return false;\n}\n\nexport function hasInvalidOrdinalData(obj) {\n  const validYear = isInteger(obj.year),\n    validOrdinal = integerBetween(obj.ordinal, 1, daysInYear(obj.year));\n\n  if (!validYear) {\n    return unitOutOfRange(\"year\", obj.year);\n  } else if (!validOrdinal) {\n    return unitOutOfRange(\"ordinal\", obj.ordinal);\n  } else return false;\n}\n\nexport function hasInvalidGregorianData(obj) {\n  const validYear = isInteger(obj.year),\n    validMonth = integerBetween(obj.month, 1, 12),\n    validDay = integerBetween(obj.day, 1, daysInMonth(obj.year, obj.month));\n\n  if (!validYear) {\n    return unitOutOfRange(\"year\", obj.year);\n  } else if (!validMonth) {\n    return unitOutOfRange(\"month\", obj.month);\n  } else if (!validDay) {\n    return unitOutOfRange(\"day\", obj.day);\n  } else return false;\n}\n\nexport function hasInvalidTimeData(obj) {\n  const { hour, minute, second, millisecond } = obj;\n  const validHour =\n      integerBetween(hour, 0, 23) ||\n      (hour === 24 && minute === 0 && second === 0 && millisecond === 0),\n    validMinute = integerBetween(minute, 0, 59),\n    validSecond = integerBetween(second, 0, 59),\n    validMillisecond = integerBetween(millisecond, 0, 999);\n\n  if (!validHour) {\n    return unitOutOfRange(\"hour\", hour);\n  } else if (!validMinute) {\n    return unitOutOfRange(\"minute\", minute);\n  } else if (!validSecond) {\n    return unitOutOfRange(\"second\", second);\n  } else if (!validMillisecond) {\n    return unitOutOfRange(\"millisecond\", millisecond);\n  } else return false;\n}\n", "/*\n  This is just a junk drawer, containing anything used across multiple classes.\n  Because <PERSON>xon is small(ish), this should stay small and we won't worry about splitting\n  it up into, say, parsingUtil.js and basicUtil.js and so on. But they are divided up by feature area.\n*/\n\nimport { InvalidArgumentError } from \"../errors.js\";\nimport Settings from \"../settings.js\";\nimport { dayOfWeek, isoWeekdayToLocal } from \"./conversions.js\";\n\n/**\n * @private\n */\n\n// TYPES\n\nexport function isUndefined(o) {\n  return typeof o === \"undefined\";\n}\n\nexport function isNumber(o) {\n  return typeof o === \"number\";\n}\n\nexport function isInteger(o) {\n  return typeof o === \"number\" && o % 1 === 0;\n}\n\nexport function isString(o) {\n  return typeof o === \"string\";\n}\n\nexport function isDate(o) {\n  return Object.prototype.toString.call(o) === \"[object Date]\";\n}\n\n// CAPABILITIES\n\nexport function hasRelative() {\n  try {\n    return typeof Intl !== \"undefined\" && !!Intl.RelativeTimeFormat;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport function hasLocaleWeekInfo() {\n  try {\n    return (\n      typeof Intl !== \"undefined\" &&\n      !!Intl.Locale &&\n      (\"weekInfo\" in Intl.Locale.prototype || \"getWeekInfo\" in Intl.Locale.prototype)\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n// OBJECTS AND ARRAYS\n\nexport function maybeArray(thing) {\n  return Array.isArray(thing) ? thing : [thing];\n}\n\nexport function bestBy(arr, by, compare) {\n  if (arr.length === 0) {\n    return undefined;\n  }\n  return arr.reduce((best, next) => {\n    const pair = [by(next), next];\n    if (!best) {\n      return pair;\n    } else if (compare(best[0], pair[0]) === best[0]) {\n      return best;\n    } else {\n      return pair;\n    }\n  }, null)[1];\n}\n\nexport function pick(obj, keys) {\n  return keys.reduce((a, k) => {\n    a[k] = obj[k];\n    return a;\n  }, {});\n}\n\nexport function hasOwnProperty(obj, prop) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n\nexport function validateWeekSettings(settings) {\n  if (settings == null) {\n    return null;\n  } else if (typeof settings !== \"object\") {\n    throw new InvalidArgumentError(\"Week settings must be an object\");\n  } else {\n    if (\n      !integerBetween(settings.firstDay, 1, 7) ||\n      !integerBetween(settings.minimalDays, 1, 7) ||\n      !Array.isArray(settings.weekend) ||\n      settings.weekend.some((v) => !integerBetween(v, 1, 7))\n    ) {\n      throw new InvalidArgumentError(\"Invalid week settings\");\n    }\n    return {\n      firstDay: settings.firstDay,\n      minimalDays: settings.minimalDays,\n      weekend: Array.from(settings.weekend),\n    };\n  }\n}\n\n// NUMBERS AND STRINGS\n\nexport function integerBetween(thing, bottom, top) {\n  return isInteger(thing) && thing >= bottom && thing <= top;\n}\n\n// x % n but takes the sign of n instead of x\nexport function floorMod(x, n) {\n  return x - n * Math.floor(x / n);\n}\n\nexport function padStart(input, n = 2) {\n  const isNeg = input < 0;\n  let padded;\n  if (isNeg) {\n    padded = \"-\" + (\"\" + -input).padStart(n, \"0\");\n  } else {\n    padded = (\"\" + input).padStart(n, \"0\");\n  }\n  return padded;\n}\n\nexport function parseInteger(string) {\n  if (isUndefined(string) || string === null || string === \"\") {\n    return undefined;\n  } else {\n    return parseInt(string, 10);\n  }\n}\n\nexport function parseFloating(string) {\n  if (isUndefined(string) || string === null || string === \"\") {\n    return undefined;\n  } else {\n    return parseFloat(string);\n  }\n}\n\nexport function parseMillis(fraction) {\n  // Return undefined (instead of 0) in these cases, where fraction is not set\n  if (isUndefined(fraction) || fraction === null || fraction === \"\") {\n    return undefined;\n  } else {\n    const f = parseFloat(\"0.\" + fraction) * 1000;\n    return Math.floor(f);\n  }\n}\n\nexport function roundTo(number, digits, towardZero = false) {\n  const factor = 10 ** digits,\n    rounder = towardZero ? Math.trunc : Math.round;\n  return rounder(number * factor) / factor;\n}\n\n// DATE BASICS\n\nexport function isLeapYear(year) {\n  return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\n\nexport function daysInYear(year) {\n  return isLeapYear(year) ? 366 : 365;\n}\n\nexport function daysInMonth(year, month) {\n  const modMonth = floorMod(month - 1, 12) + 1,\n    modYear = year + (month - modMonth) / 12;\n\n  if (modMonth === 2) {\n    return isLeapYear(modYear) ? 29 : 28;\n  } else {\n    return [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][modMonth - 1];\n  }\n}\n\n// convert a calendar object to a local timestamp (epoch, but with the offset baked in)\nexport function objToLocalTS(obj) {\n  let d = Date.UTC(\n    obj.year,\n    obj.month - 1,\n    obj.day,\n    obj.hour,\n    obj.minute,\n    obj.second,\n    obj.millisecond\n  );\n\n  // for legacy reasons, years between 0 and 99 are interpreted as 19XX; revert that\n  if (obj.year < 100 && obj.year >= 0) {\n    d = new Date(d);\n    // set the month and day again, this is necessary because year 2000 is a leap year, but year 100 is not\n    // so if obj.year is in 99, but obj.day makes it roll over into year 100,\n    // the calculations done by Date.UTC are using year 2000 - which is incorrect\n    d.setUTCFullYear(obj.year, obj.month - 1, obj.day);\n  }\n  return +d;\n}\n\n// adapted from moment.js: https://github.com/moment/moment/blob/000ac1800e620f770f4eb31b5ae908f6167b0ab2/src/lib/units/week-calendar-utils.js\nfunction firstWeekOffset(year, minDaysInFirstWeek, startOfWeek) {\n  const fwdlw = isoWeekdayToLocal(dayOfWeek(year, 1, minDaysInFirstWeek), startOfWeek);\n  return -fwdlw + minDaysInFirstWeek - 1;\n}\n\nexport function weeksInWeekYear(weekYear, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const weekOffset = firstWeekOffset(weekYear, minDaysInFirstWeek, startOfWeek);\n  const weekOffsetNext = firstWeekOffset(weekYear + 1, minDaysInFirstWeek, startOfWeek);\n  return (daysInYear(weekYear) - weekOffset + weekOffsetNext) / 7;\n}\n\nexport function untruncateYear(year) {\n  if (year > 99) {\n    return year;\n  } else return year > Settings.twoDigitCutoffYear ? 1900 + year : 2000 + year;\n}\n\n// PARSING\n\nexport function parseZoneInfo(ts, offsetFormat, locale, timeZone = null) {\n  const date = new Date(ts),\n    intlOpts = {\n      hourCycle: \"h23\",\n      year: \"numeric\",\n      month: \"2-digit\",\n      day: \"2-digit\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    };\n\n  if (timeZone) {\n    intlOpts.timeZone = timeZone;\n  }\n\n  const modified = { timeZoneName: offsetFormat, ...intlOpts };\n\n  const parsed = new Intl.DateTimeFormat(locale, modified)\n    .formatToParts(date)\n    .find((m) => m.type.toLowerCase() === \"timezonename\");\n  return parsed ? parsed.value : null;\n}\n\n// signedOffset('-5', '30') -> -330\nexport function signedOffset(offHourStr, offMinuteStr) {\n  let offHour = parseInt(offHourStr, 10);\n\n  // don't || this because we want to preserve -0\n  if (Number.isNaN(offHour)) {\n    offHour = 0;\n  }\n\n  const offMin = parseInt(offMinuteStr, 10) || 0,\n    offMinSigned = offHour < 0 || Object.is(offHour, -0) ? -offMin : offMin;\n  return offHour * 60 + offMinSigned;\n}\n\n// COERCION\n\nexport function asNumber(value) {\n  const numericValue = Number(value);\n  if (typeof value === \"boolean\" || value === \"\" || Number.isNaN(numericValue))\n    throw new InvalidArgumentError(`Invalid unit value ${value}`);\n  return numericValue;\n}\n\nexport function normalizeObject(obj, normalizer) {\n  const normalized = {};\n  for (const u in obj) {\n    if (hasOwnProperty(obj, u)) {\n      const v = obj[u];\n      if (v === undefined || v === null) continue;\n      normalized[normalizer(u)] = asNumber(v);\n    }\n  }\n  return normalized;\n}\n\n/**\n * Returns the offset's value as a string\n * @param {number} ts - Epoch milliseconds for which to get the offset\n * @param {string} format - What style of offset to return.\n *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n * @return {string}\n */\nexport function formatOffset(offset, format) {\n  const hours = Math.trunc(Math.abs(offset / 60)),\n    minutes = Math.trunc(Math.abs(offset % 60)),\n    sign = offset >= 0 ? \"+\" : \"-\";\n\n  switch (format) {\n    case \"short\":\n      return `${sign}${padStart(hours, 2)}:${padStart(minutes, 2)}`;\n    case \"narrow\":\n      return `${sign}${hours}${minutes > 0 ? `:${minutes}` : \"\"}`;\n    case \"techie\":\n      return `${sign}${padStart(hours, 2)}${padStart(minutes, 2)}`;\n    default:\n      throw new RangeError(`Value format ${format} is out of range for property format`);\n  }\n}\n\nexport function timeObject(obj) {\n  return pick(obj, [\"hour\", \"minute\", \"second\", \"millisecond\"]);\n}\n", "import * as Formats from \"./formats.js\";\nimport { pick } from \"./util.js\";\n\nfunction stringify(obj) {\n  return JSON.stringify(obj, Object.keys(obj).sort());\n}\n\n/**\n * @private\n */\n\nexport const monthsLong = [\n  \"January\",\n  \"February\",\n  \"March\",\n  \"April\",\n  \"May\",\n  \"June\",\n  \"July\",\n  \"August\",\n  \"September\",\n  \"October\",\n  \"November\",\n  \"December\",\n];\n\nexport const monthsShort = [\n  \"Jan\",\n  \"Feb\",\n  \"Mar\",\n  \"Apr\",\n  \"May\",\n  \"Jun\",\n  \"Jul\",\n  \"Aug\",\n  \"Sep\",\n  \"Oct\",\n  \"Nov\",\n  \"Dec\",\n];\n\nexport const monthsNarrow = [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"];\n\nexport function months(length) {\n  switch (length) {\n    case \"narrow\":\n      return [...monthsNarrow];\n    case \"short\":\n      return [...monthsShort];\n    case \"long\":\n      return [...monthsLong];\n    case \"numeric\":\n      return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"];\n    case \"2-digit\":\n      return [\"01\", \"02\", \"03\", \"04\", \"05\", \"06\", \"07\", \"08\", \"09\", \"10\", \"11\", \"12\"];\n    default:\n      return null;\n  }\n}\n\nexport const weekdaysLong = [\n  \"Monday\",\n  \"Tuesday\",\n  \"Wednesday\",\n  \"Thursday\",\n  \"Friday\",\n  \"Saturday\",\n  \"Sunday\",\n];\n\nexport const weekdaysShort = [\"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\", \"Sun\"];\n\nexport const weekdaysNarrow = [\"M\", \"T\", \"W\", \"T\", \"F\", \"S\", \"S\"];\n\nexport function weekdays(length) {\n  switch (length) {\n    case \"narrow\":\n      return [...weekdaysNarrow];\n    case \"short\":\n      return [...weekdaysShort];\n    case \"long\":\n      return [...weekdaysLong];\n    case \"numeric\":\n      return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    default:\n      return null;\n  }\n}\n\nexport const meridiems = [\"AM\", \"PM\"];\n\nexport const erasLong = [\"Before Christ\", \"Anno Domini\"];\n\nexport const erasShort = [\"BC\", \"AD\"];\n\nexport const erasNarrow = [\"B\", \"A\"];\n\nexport function eras(length) {\n  switch (length) {\n    case \"narrow\":\n      return [...erasNarrow];\n    case \"short\":\n      return [...erasShort];\n    case \"long\":\n      return [...erasLong];\n    default:\n      return null;\n  }\n}\n\nexport function meridiemForDateTime(dt) {\n  return meridiems[dt.hour < 12 ? 0 : 1];\n}\n\nexport function weekdayForDateTime(dt, length) {\n  return weekdays(length)[dt.weekday - 1];\n}\n\nexport function monthForDateTime(dt, length) {\n  return months(length)[dt.month - 1];\n}\n\nexport function eraForDateTime(dt, length) {\n  return eras(length)[dt.year < 0 ? 0 : 1];\n}\n\nexport function formatRelativeTime(unit, count, numeric = \"always\", narrow = false) {\n  const units = {\n    years: [\"year\", \"yr.\"],\n    quarters: [\"quarter\", \"qtr.\"],\n    months: [\"month\", \"mo.\"],\n    weeks: [\"week\", \"wk.\"],\n    days: [\"day\", \"day\", \"days\"],\n    hours: [\"hour\", \"hr.\"],\n    minutes: [\"minute\", \"min.\"],\n    seconds: [\"second\", \"sec.\"],\n  };\n\n  const lastable = [\"hours\", \"minutes\", \"seconds\"].indexOf(unit) === -1;\n\n  if (numeric === \"auto\" && lastable) {\n    const isDay = unit === \"days\";\n    switch (count) {\n      case 1:\n        return isDay ? \"tomorrow\" : `next ${units[unit][0]}`;\n      case -1:\n        return isDay ? \"yesterday\" : `last ${units[unit][0]}`;\n      case 0:\n        return isDay ? \"today\" : `this ${units[unit][0]}`;\n      default: // fall through\n    }\n  }\n\n  const isInPast = Object.is(count, -0) || count < 0,\n    fmtValue = Math.abs(count),\n    singular = fmtValue === 1,\n    lilUnits = units[unit],\n    fmtUnit = narrow\n      ? singular\n        ? lilUnits[1]\n        : lilUnits[2] || lilUnits[1]\n      : singular\n      ? units[unit][0]\n      : unit;\n  return isInPast ? `${fmtValue} ${fmtUnit} ago` : `in ${fmtValue} ${fmtUnit}`;\n}\n\nexport function formatString(knownFormat) {\n  // these all have the offsets removed because we don't have access to them\n  // without all the intl stuff this is backfilling\n  const filtered = pick(knownFormat, [\n      \"weekday\",\n      \"era\",\n      \"year\",\n      \"month\",\n      \"day\",\n      \"hour\",\n      \"minute\",\n      \"second\",\n      \"timeZoneName\",\n      \"hourCycle\",\n    ]),\n    key = stringify(filtered),\n    dateTimeHuge = \"EEEE, LLLL d, yyyy, h:mm a\";\n  switch (key) {\n    case stringify(Formats.DATE_SHORT):\n      return \"M/d/yyyy\";\n    case stringify(Formats.DATE_MED):\n      return \"LLL d, yyyy\";\n    case stringify(Formats.DATE_MED_WITH_WEEKDAY):\n      return \"EEE, LLL d, yyyy\";\n    case stringify(Formats.DATE_FULL):\n      return \"LLLL d, yyyy\";\n    case stringify(Formats.DATE_HUGE):\n      return \"EEEE, LLLL d, yyyy\";\n    case stringify(Formats.TIME_SIMPLE):\n      return \"h:mm a\";\n    case stringify(Formats.TIME_WITH_SECONDS):\n      return \"h:mm:ss a\";\n    case stringify(Formats.TIME_WITH_SHORT_OFFSET):\n      return \"h:mm a\";\n    case stringify(Formats.TIME_WITH_LONG_OFFSET):\n      return \"h:mm a\";\n    case stringify(Formats.TIME_24_SIMPLE):\n      return \"HH:mm\";\n    case stringify(Formats.TIME_24_WITH_SECONDS):\n      return \"HH:mm:ss\";\n    case stringify(Formats.TIME_24_WITH_SHORT_OFFSET):\n      return \"HH:mm\";\n    case stringify(Formats.TIME_24_WITH_LONG_OFFSET):\n      return \"HH:mm\";\n    case stringify(Formats.DATETIME_SHORT):\n      return \"M/d/yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_MED):\n      return \"LLL d, yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_FULL):\n      return \"LLLL d, yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_HUGE):\n      return dateTimeHuge;\n    case stringify(Formats.DATETIME_SHORT_WITH_SECONDS):\n      return \"M/d/yyyy, h:mm:ss a\";\n    case stringify(Formats.DATETIME_MED_WITH_SECONDS):\n      return \"LLL d, yyyy, h:mm:ss a\";\n    case stringify(Formats.DATETIME_MED_WITH_WEEKDAY):\n      return \"EEE, d LLL yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_FULL_WITH_SECONDS):\n      return \"LLLL d, yyyy, h:mm:ss a\";\n    case stringify(Formats.DATETIME_HUGE_WITH_SECONDS):\n      return \"EEEE, LLLL d, yyyy, h:mm:ss a\";\n    default:\n      return dateTimeHuge;\n  }\n}\n", "import * as English from \"./english.js\";\nimport * as Formats from \"./formats.js\";\nimport { padStart } from \"./util.js\";\n\nfunction stringifyTokens(splits, tokenToString) {\n  let s = \"\";\n  for (const token of splits) {\n    if (token.literal) {\n      s += token.val;\n    } else {\n      s += tokenToString(token.val);\n    }\n  }\n  return s;\n}\n\nconst macroTokenToFormatOpts = {\n  D: Formats.DATE_SHORT,\n  DD: Formats.DATE_MED,\n  DDD: Formats.DATE_FULL,\n  DDDD: Formats.DATE_HUGE,\n  t: Formats.TIME_SIMPLE,\n  tt: Formats.TIME_WITH_SECONDS,\n  ttt: Formats.TIME_WITH_SHORT_OFFSET,\n  tttt: Formats.TIME_WITH_LONG_OFFSET,\n  T: Formats.TIME_24_SIMPLE,\n  TT: Formats.TIME_24_WITH_SECONDS,\n  TTT: Formats.TIME_24_WITH_SHORT_OFFSET,\n  TTTT: Formats.TIME_24_WITH_LONG_OFFSET,\n  f: Formats.DATETIME_SHORT,\n  ff: Formats.DATETIME_MED,\n  fff: Formats.DATETIME_FULL,\n  ffff: Formats.DATETIME_HUGE,\n  F: Formats.DATETIME_SHORT_WITH_SECONDS,\n  FF: Formats.DATETIME_MED_WITH_SECONDS,\n  FFF: Formats.DATETIME_FULL_WITH_SECONDS,\n  FFFF: Formats.DATETIME_HUGE_WITH_SECONDS,\n};\n\n/**\n * @private\n */\n\nexport default class Formatter {\n  static create(locale, opts = {}) {\n    return new Formatter(locale, opts);\n  }\n\n  static parseFormat(fmt) {\n    // white-space is always considered a literal in user-provided formats\n    // the \" \" token has a special meaning (see unitForToken)\n\n    let current = null,\n      currentFull = \"\",\n      bracketed = false;\n    const splits = [];\n    for (let i = 0; i < fmt.length; i++) {\n      const c = fmt.charAt(i);\n      if (c === \"'\") {\n        if (currentFull.length > 0) {\n          splits.push({ literal: bracketed || /^\\s+$/.test(currentFull), val: currentFull });\n        }\n        current = null;\n        currentFull = \"\";\n        bracketed = !bracketed;\n      } else if (bracketed) {\n        currentFull += c;\n      } else if (c === current) {\n        currentFull += c;\n      } else {\n        if (currentFull.length > 0) {\n          splits.push({ literal: /^\\s+$/.test(currentFull), val: currentFull });\n        }\n        currentFull = c;\n        current = c;\n      }\n    }\n\n    if (currentFull.length > 0) {\n      splits.push({ literal: bracketed || /^\\s+$/.test(currentFull), val: currentFull });\n    }\n\n    return splits;\n  }\n\n  static macroTokenToFormatOpts(token) {\n    return macroTokenToFormatOpts[token];\n  }\n\n  constructor(locale, formatOpts) {\n    this.opts = formatOpts;\n    this.loc = locale;\n    this.systemLoc = null;\n  }\n\n  formatWithSystemDefault(dt, opts) {\n    if (this.systemLoc === null) {\n      this.systemLoc = this.loc.redefaultToSystem();\n    }\n    const df = this.systemLoc.dtFormatter(dt, { ...this.opts, ...opts });\n    return df.format();\n  }\n\n  dtFormatter(dt, opts = {}) {\n    return this.loc.dtFormatter(dt, { ...this.opts, ...opts });\n  }\n\n  formatDateTime(dt, opts) {\n    return this.dtFormatter(dt, opts).format();\n  }\n\n  formatDateTimeParts(dt, opts) {\n    return this.dtFormatter(dt, opts).formatToParts();\n  }\n\n  formatInterval(interval, opts) {\n    const df = this.dtFormatter(interval.start, opts);\n    return df.dtf.formatRange(interval.start.toJSDate(), interval.end.toJSDate());\n  }\n\n  resolvedOptions(dt, opts) {\n    return this.dtFormatter(dt, opts).resolvedOptions();\n  }\n\n  num(n, p = 0) {\n    // we get some perf out of doing this here, annoyingly\n    if (this.opts.forceSimple) {\n      return padStart(n, p);\n    }\n\n    const opts = { ...this.opts };\n\n    if (p > 0) {\n      opts.padTo = p;\n    }\n\n    return this.loc.numberFormatter(opts).format(n);\n  }\n\n  formatDateTimeFromString(dt, fmt) {\n    const knownEnglish = this.loc.listingMode() === \"en\",\n      useDateTimeFormatter = this.loc.outputCalendar && this.loc.outputCalendar !== \"gregory\",\n      string = (opts, extract) => this.loc.extract(dt, opts, extract),\n      formatOffset = (opts) => {\n        if (dt.isOffsetFixed && dt.offset === 0 && opts.allowZ) {\n          return \"Z\";\n        }\n\n        return dt.isValid ? dt.zone.formatOffset(dt.ts, opts.format) : \"\";\n      },\n      meridiem = () =>\n        knownEnglish\n          ? English.meridiemForDateTime(dt)\n          : string({ hour: \"numeric\", hourCycle: \"h12\" }, \"dayperiod\"),\n      month = (length, standalone) =>\n        knownEnglish\n          ? English.monthForDateTime(dt, length)\n          : string(standalone ? { month: length } : { month: length, day: \"numeric\" }, \"month\"),\n      weekday = (length, standalone) =>\n        knownEnglish\n          ? English.weekdayForDateTime(dt, length)\n          : string(\n              standalone ? { weekday: length } : { weekday: length, month: \"long\", day: \"numeric\" },\n              \"weekday\"\n            ),\n      maybeMacro = (token) => {\n        const formatOpts = Formatter.macroTokenToFormatOpts(token);\n        if (formatOpts) {\n          return this.formatWithSystemDefault(dt, formatOpts);\n        } else {\n          return token;\n        }\n      },\n      era = (length) =>\n        knownEnglish ? English.eraForDateTime(dt, length) : string({ era: length }, \"era\"),\n      tokenToString = (token) => {\n        // Where possible: https://cldr.unicode.org/translation/date-time/date-time-symbols\n        switch (token) {\n          // ms\n          case \"S\":\n            return this.num(dt.millisecond);\n          case \"u\":\n          // falls through\n          case \"SSS\":\n            return this.num(dt.millisecond, 3);\n          // seconds\n          case \"s\":\n            return this.num(dt.second);\n          case \"ss\":\n            return this.num(dt.second, 2);\n          // fractional seconds\n          case \"uu\":\n            return this.num(Math.floor(dt.millisecond / 10), 2);\n          case \"uuu\":\n            return this.num(Math.floor(dt.millisecond / 100));\n          // minutes\n          case \"m\":\n            return this.num(dt.minute);\n          case \"mm\":\n            return this.num(dt.minute, 2);\n          // hours\n          case \"h\":\n            return this.num(dt.hour % 12 === 0 ? 12 : dt.hour % 12);\n          case \"hh\":\n            return this.num(dt.hour % 12 === 0 ? 12 : dt.hour % 12, 2);\n          case \"H\":\n            return this.num(dt.hour);\n          case \"HH\":\n            return this.num(dt.hour, 2);\n          // offset\n          case \"Z\":\n            // like +6\n            return formatOffset({ format: \"narrow\", allowZ: this.opts.allowZ });\n          case \"ZZ\":\n            // like +06:00\n            return formatOffset({ format: \"short\", allowZ: this.opts.allowZ });\n          case \"ZZZ\":\n            // like +0600\n            return formatOffset({ format: \"techie\", allowZ: this.opts.allowZ });\n          case \"ZZZZ\":\n            // like EST\n            return dt.zone.offsetName(dt.ts, { format: \"short\", locale: this.loc.locale });\n          case \"ZZZZZ\":\n            // like Eastern Standard Time\n            return dt.zone.offsetName(dt.ts, { format: \"long\", locale: this.loc.locale });\n          // zone\n          case \"z\":\n            // like America/New_York\n            return dt.zoneName;\n          // meridiems\n          case \"a\":\n            return meridiem();\n          // dates\n          case \"d\":\n            return useDateTimeFormatter ? string({ day: \"numeric\" }, \"day\") : this.num(dt.day);\n          case \"dd\":\n            return useDateTimeFormatter ? string({ day: \"2-digit\" }, \"day\") : this.num(dt.day, 2);\n          // weekdays - standalone\n          case \"c\":\n            // like 1\n            return this.num(dt.weekday);\n          case \"ccc\":\n            // like 'Tues'\n            return weekday(\"short\", true);\n          case \"cccc\":\n            // like 'Tuesday'\n            return weekday(\"long\", true);\n          case \"ccccc\":\n            // like 'T'\n            return weekday(\"narrow\", true);\n          // weekdays - format\n          case \"E\":\n            // like 1\n            return this.num(dt.weekday);\n          case \"EEE\":\n            // like 'Tues'\n            return weekday(\"short\", false);\n          case \"EEEE\":\n            // like 'Tuesday'\n            return weekday(\"long\", false);\n          case \"EEEEE\":\n            // like 'T'\n            return weekday(\"narrow\", false);\n          // months - standalone\n          case \"L\":\n            // like 1\n            return useDateTimeFormatter\n              ? string({ month: \"numeric\", day: \"numeric\" }, \"month\")\n              : this.num(dt.month);\n          case \"LL\":\n            // like 01, doesn't seem to work\n            return useDateTimeFormatter\n              ? string({ month: \"2-digit\", day: \"numeric\" }, \"month\")\n              : this.num(dt.month, 2);\n          case \"LLL\":\n            // like Jan\n            return month(\"short\", true);\n          case \"LLLL\":\n            // like January\n            return month(\"long\", true);\n          case \"LLLLL\":\n            // like J\n            return month(\"narrow\", true);\n          // months - format\n          case \"M\":\n            // like 1\n            return useDateTimeFormatter\n              ? string({ month: \"numeric\" }, \"month\")\n              : this.num(dt.month);\n          case \"MM\":\n            // like 01\n            return useDateTimeFormatter\n              ? string({ month: \"2-digit\" }, \"month\")\n              : this.num(dt.month, 2);\n          case \"MMM\":\n            // like Jan\n            return month(\"short\", false);\n          case \"MMMM\":\n            // like January\n            return month(\"long\", false);\n          case \"MMMMM\":\n            // like J\n            return month(\"narrow\", false);\n          // years\n          case \"y\":\n            // like 2014\n            return useDateTimeFormatter ? string({ year: \"numeric\" }, \"year\") : this.num(dt.year);\n          case \"yy\":\n            // like 14\n            return useDateTimeFormatter\n              ? string({ year: \"2-digit\" }, \"year\")\n              : this.num(dt.year.toString().slice(-2), 2);\n          case \"yyyy\":\n            // like 0012\n            return useDateTimeFormatter\n              ? string({ year: \"numeric\" }, \"year\")\n              : this.num(dt.year, 4);\n          case \"yyyyyy\":\n            // like 000012\n            return useDateTimeFormatter\n              ? string({ year: \"numeric\" }, \"year\")\n              : this.num(dt.year, 6);\n          // eras\n          case \"G\":\n            // like AD\n            return era(\"short\");\n          case \"GG\":\n            // like Anno Domini\n            return era(\"long\");\n          case \"GGGGG\":\n            return era(\"narrow\");\n          case \"kk\":\n            return this.num(dt.weekYear.toString().slice(-2), 2);\n          case \"kkkk\":\n            return this.num(dt.weekYear, 4);\n          case \"W\":\n            return this.num(dt.weekNumber);\n          case \"WW\":\n            return this.num(dt.weekNumber, 2);\n          case \"n\":\n            return this.num(dt.localWeekNumber);\n          case \"nn\":\n            return this.num(dt.localWeekNumber, 2);\n          case \"ii\":\n            return this.num(dt.localWeekYear.toString().slice(-2), 2);\n          case \"iiii\":\n            return this.num(dt.localWeekYear, 4);\n          case \"o\":\n            return this.num(dt.ordinal);\n          case \"ooo\":\n            return this.num(dt.ordinal, 3);\n          case \"q\":\n            // like 1\n            return this.num(dt.quarter);\n          case \"qq\":\n            // like 01\n            return this.num(dt.quarter, 2);\n          case \"X\":\n            return this.num(Math.floor(dt.ts / 1000));\n          case \"x\":\n            return this.num(dt.ts);\n          default:\n            return maybeMacro(token);\n        }\n      };\n\n    return stringifyTokens(Formatter.parseFormat(fmt), tokenToString);\n  }\n\n  formatDurationFromString(dur, fmt) {\n    const tokenToField = (token) => {\n        switch (token[0]) {\n          case \"S\":\n            return \"millisecond\";\n          case \"s\":\n            return \"second\";\n          case \"m\":\n            return \"minute\";\n          case \"h\":\n            return \"hour\";\n          case \"d\":\n            return \"day\";\n          case \"w\":\n            return \"week\";\n          case \"M\":\n            return \"month\";\n          case \"y\":\n            return \"year\";\n          default:\n            return null;\n        }\n      },\n      tokenToString = (lildur) => (token) => {\n        const mapped = tokenToField(token);\n        if (mapped) {\n          return this.num(lildur.get(mapped), token.length);\n        } else {\n          return token;\n        }\n      },\n      tokens = Formatter.parseFormat(fmt),\n      realTokens = tokens.reduce(\n        (found, { literal, val }) => (literal ? found : found.concat(val)),\n        []\n      ),\n      collapsed = dur.shiftTo(...realTokens.map(tokenToField).filter((t) => t));\n    return stringifyTokens(tokens, tokenToString(collapsed));\n  }\n}\n", "import {\n  untruncate<PERSON>ear,\n  signed<PERSON>ffset,\n  parseInteger,\n  parse<PERSON>illis,\n  isUndefined,\n  parseFloating,\n} from \"./util.js\";\nimport * as English from \"./english.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\n\n/*\n * This file handles parsing for well-specified formats. Here's how it works:\n * Two things go into parsing: a regex to match with and an extractor to take apart the groups in the match.\n * An extractor is just a function that takes a regex match array and returns a { year: ..., month: ... } object\n * parse() does the work of executing the regex and applying the extractor. It takes multiple regex/extractor pairs to try in sequence.\n * Extractors can take a \"cursor\" representing the offset in the match to look at. This makes it easy to combine extractors.\n * combineExtractors() does the work of combining them, keeping track of the cursor through multiple extractions.\n * Some extractions are super dumb and simpleParse and fromStrings help DRY them.\n */\n\nconst ianaRegex = /[A-Za-z_+-]{1,256}(?::?\\/[A-Za-z0-9_+-]{1,256}(?:\\/[A-Za-z0-9_+-]{1,256})?)?/;\n\nfunction combineRegexes(...regexes) {\n  const full = regexes.reduce((f, r) => f + r.source, \"\");\n  return RegExp(`^${full}$`);\n}\n\nfunction combineExtractors(...extractors) {\n  return (m) =>\n    extractors\n      .reduce(\n        ([mergedVals, mergedZone, cursor], ex) => {\n          const [val, zone, next] = ex(m, cursor);\n          return [{ ...mergedVals, ...val }, zone || mergedZone, next];\n        },\n        [{}, null, 1]\n      )\n      .slice(0, 2);\n}\n\nfunction parse(s, ...patterns) {\n  if (s == null) {\n    return [null, null];\n  }\n\n  for (const [regex, extractor] of patterns) {\n    const m = regex.exec(s);\n    if (m) {\n      return extractor(m);\n    }\n  }\n  return [null, null];\n}\n\nfunction simpleParse(...keys) {\n  return (match, cursor) => {\n    const ret = {};\n    let i;\n\n    for (i = 0; i < keys.length; i++) {\n      ret[keys[i]] = parseInteger(match[cursor + i]);\n    }\n    return [ret, null, cursor + i];\n  };\n}\n\n// ISO and SQL parsing\nconst offsetRegex = /(?:(Z)|([+-]\\d\\d)(?::?(\\d\\d))?)/;\nconst isoExtendedZone = `(?:${offsetRegex.source}?(?:\\\\[(${ianaRegex.source})\\\\])?)?`;\nconst isoTimeBaseRegex = /(\\d\\d)(?::?(\\d\\d)(?::?(\\d\\d)(?:[.,](\\d{1,30}))?)?)?/;\nconst isoTimeRegex = RegExp(`${isoTimeBaseRegex.source}${isoExtendedZone}`);\nconst isoTimeExtensionRegex = RegExp(`(?:T${isoTimeRegex.source})?`);\nconst isoYmdRegex = /([+-]\\d{6}|\\d{4})(?:-?(\\d\\d)(?:-?(\\d\\d))?)?/;\nconst isoWeekRegex = /(\\d{4})-?W(\\d\\d)(?:-?(\\d))?/;\nconst isoOrdinalRegex = /(\\d{4})-?(\\d{3})/;\nconst extractISOWeekData = simpleParse(\"weekYear\", \"weekNumber\", \"weekDay\");\nconst extractISOOrdinalData = simpleParse(\"year\", \"ordinal\");\nconst sqlYmdRegex = /(\\d{4})-(\\d\\d)-(\\d\\d)/; // dumbed-down version of the ISO one\nconst sqlTimeRegex = RegExp(\n  `${isoTimeBaseRegex.source} ?(?:${offsetRegex.source}|(${ianaRegex.source}))?`\n);\nconst sqlTimeExtensionRegex = RegExp(`(?: ${sqlTimeRegex.source})?`);\n\nfunction int(match, pos, fallback) {\n  const m = match[pos];\n  return isUndefined(m) ? fallback : parseInteger(m);\n}\n\nfunction extractISOYmd(match, cursor) {\n  const item = {\n    year: int(match, cursor),\n    month: int(match, cursor + 1, 1),\n    day: int(match, cursor + 2, 1),\n  };\n\n  return [item, null, cursor + 3];\n}\n\nfunction extractISOTime(match, cursor) {\n  const item = {\n    hours: int(match, cursor, 0),\n    minutes: int(match, cursor + 1, 0),\n    seconds: int(match, cursor + 2, 0),\n    milliseconds: parseMillis(match[cursor + 3]),\n  };\n\n  return [item, null, cursor + 4];\n}\n\nfunction extractISOOffset(match, cursor) {\n  const local = !match[cursor] && !match[cursor + 1],\n    fullOffset = signedOffset(match[cursor + 1], match[cursor + 2]),\n    zone = local ? null : FixedOffsetZone.instance(fullOffset);\n  return [{}, zone, cursor + 3];\n}\n\nfunction extractIANAZone(match, cursor) {\n  const zone = match[cursor] ? IANAZone.create(match[cursor]) : null;\n  return [{}, zone, cursor + 1];\n}\n\n// ISO time parsing\n\nconst isoTimeOnly = RegExp(`^T?${isoTimeBaseRegex.source}$`);\n\n// ISO duration parsing\n\nconst isoDuration =\n  /^-?P(?:(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)Y)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)M)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)W)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)D)?(?:T(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)H)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)M)?(?:(-?\\d{1,20})(?:[.,](-?\\d{1,20}))?S)?)?)$/;\n\nfunction extractISODuration(match) {\n  const [s, yearStr, monthStr, weekStr, dayStr, hourStr, minuteStr, secondStr, millisecondsStr] =\n    match;\n\n  const hasNegativePrefix = s[0] === \"-\";\n  const negativeSeconds = secondStr && secondStr[0] === \"-\";\n\n  const maybeNegate = (num, force = false) =>\n    num !== undefined && (force || (num && hasNegativePrefix)) ? -num : num;\n\n  return [\n    {\n      years: maybeNegate(parseFloating(yearStr)),\n      months: maybeNegate(parseFloating(monthStr)),\n      weeks: maybeNegate(parseFloating(weekStr)),\n      days: maybeNegate(parseFloating(dayStr)),\n      hours: maybeNegate(parseFloating(hourStr)),\n      minutes: maybeNegate(parseFloating(minuteStr)),\n      seconds: maybeNegate(parseFloating(secondStr), secondStr === \"-0\"),\n      milliseconds: maybeNegate(parseMillis(millisecondsStr), negativeSeconds),\n    },\n  ];\n}\n\n// These are a little braindead. EDT *should* tell us that we're in, say, America/New_York\n// and not just that we're in -240 *right now*. But since I don't think these are used that often\n// I'm just going to ignore that\nconst obsOffsets = {\n  GMT: 0,\n  EDT: -4 * 60,\n  EST: -5 * 60,\n  CDT: -5 * 60,\n  CST: -6 * 60,\n  MDT: -6 * 60,\n  MST: -7 * 60,\n  PDT: -7 * 60,\n  PST: -8 * 60,\n};\n\nfunction fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr) {\n  const result = {\n    year: yearStr.length === 2 ? untruncateYear(parseInteger(yearStr)) : parseInteger(yearStr),\n    month: English.monthsShort.indexOf(monthStr) + 1,\n    day: parseInteger(dayStr),\n    hour: parseInteger(hourStr),\n    minute: parseInteger(minuteStr),\n  };\n\n  if (secondStr) result.second = parseInteger(secondStr);\n  if (weekdayStr) {\n    result.weekday =\n      weekdayStr.length > 3\n        ? English.weekdaysLong.indexOf(weekdayStr) + 1\n        : English.weekdaysShort.indexOf(weekdayStr) + 1;\n  }\n\n  return result;\n}\n\n// RFC 2822/5322\nconst rfc2822 =\n  /^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\\s)?(\\d{1,2})\\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\\s(\\d{2,4})\\s(\\d\\d):(\\d\\d)(?::(\\d\\d))?\\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\\d\\d)(\\d\\d)))$/;\n\nfunction extractRFC2822(match) {\n  const [\n      ,\n      weekdayStr,\n      dayStr,\n      monthStr,\n      yearStr,\n      hourStr,\n      minuteStr,\n      secondStr,\n      obsOffset,\n      milOffset,\n      offHourStr,\n      offMinuteStr,\n    ] = match,\n    result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);\n\n  let offset;\n  if (obsOffset) {\n    offset = obsOffsets[obsOffset];\n  } else if (milOffset) {\n    offset = 0;\n  } else {\n    offset = signedOffset(offHourStr, offMinuteStr);\n  }\n\n  return [result, new FixedOffsetZone(offset)];\n}\n\nfunction preprocessRFC2822(s) {\n  // Remove comments and folding whitespace and replace multiple-spaces with a single space\n  return s\n    .replace(/\\([^()]*\\)|[\\n\\t]/g, \" \")\n    .replace(/(\\s\\s+)/g, \" \")\n    .trim();\n}\n\n// http date\n\nconst rfc1123 =\n    /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\\d\\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\\d{4}) (\\d\\d):(\\d\\d):(\\d\\d) GMT$/,\n  rfc850 =\n    /^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\\d\\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\\d\\d) (\\d\\d):(\\d\\d):(\\d\\d) GMT$/,\n  ascii =\n    /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \\d|\\d\\d) (\\d\\d):(\\d\\d):(\\d\\d) (\\d{4})$/;\n\nfunction extractRFC1123Or850(match) {\n  const [, weekdayStr, dayStr, monthStr, yearStr, hourStr, minuteStr, secondStr] = match,\n    result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);\n  return [result, FixedOffsetZone.utcInstance];\n}\n\nfunction extractASCII(match) {\n  const [, weekdayStr, monthStr, dayStr, hourStr, minuteStr, secondStr, yearStr] = match,\n    result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);\n  return [result, FixedOffsetZone.utcInstance];\n}\n\nconst isoYmdWithTimeExtensionRegex = combineRegexes(isoYmdRegex, isoTimeExtensionRegex);\nconst isoWeekWithTimeExtensionRegex = combineRegexes(isoWeekRegex, isoTimeExtensionRegex);\nconst isoOrdinalWithTimeExtensionRegex = combineRegexes(isoOrdinalRegex, isoTimeExtensionRegex);\nconst isoTimeCombinedRegex = combineRegexes(isoTimeRegex);\n\nconst extractISOYmdTimeAndOffset = combineExtractors(\n  extractISOYmd,\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\nconst extractISOWeekTimeAndOffset = combineExtractors(\n  extractISOWeekData,\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\nconst extractISOOrdinalDateAndTime = combineExtractors(\n  extractISOOrdinalData,\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\nconst extractISOTimeAndOffset = combineExtractors(\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\n\n/*\n * @private\n */\n\nexport function parseISODate(s) {\n  return parse(\n    s,\n    [isoYmdWithTimeExtensionRegex, extractISOYmdTimeAndOffset],\n    [isoWeekWithTimeExtensionRegex, extractISOWeekTimeAndOffset],\n    [isoOrdinalWithTimeExtensionRegex, extractISOOrdinalDateAndTime],\n    [isoTimeCombinedRegex, extractISOTimeAndOffset]\n  );\n}\n\nexport function parseRFC2822Date(s) {\n  return parse(preprocessRFC2822(s), [rfc2822, extractRFC2822]);\n}\n\nexport function parseHTTPDate(s) {\n  return parse(\n    s,\n    [rfc1123, extractRFC1123Or850],\n    [rfc850, extractRFC1123Or850],\n    [ascii, extractASCII]\n  );\n}\n\nexport function parseISODuration(s) {\n  return parse(s, [isoDuration, extractISODuration]);\n}\n\nconst extractISOTimeOnly = combineExtractors(extractISOTime);\n\nexport function parseISOTimeOnly(s) {\n  return parse(s, [isoTimeOnly, extractISOTimeOnly]);\n}\n\nconst sqlYmdWithTimeExtensionRegex = combineRegexes(sqlYmdRegex, sqlTimeExtensionRegex);\nconst sqlTimeCombinedRegex = combineRegexes(sqlTimeRegex);\n\nconst extractISOTimeOffsetAndIANAZone = combineExtractors(\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\n\nexport function parseSQL(s) {\n  return parse(\n    s,\n    [sqlYmdWithTimeExtensionRegex, extractISOYmdTimeAndOffset],\n    [sqlTimeCombinedRegex, extractISOTimeOffsetAndIANAZone]\n  );\n}\n", "import { InvalidArgumentError, InvalidDurationError, InvalidUnitError } from \"./errors.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport Invalid from \"./impl/invalid.js\";\nimport Locale from \"./impl/locale.js\";\nimport { parseISODuration, parseISOTimeOnly } from \"./impl/regexParser.js\";\nimport {\n  asNumber,\n  hasOwnProperty,\n  isNumber,\n  isUndefined,\n  normalizeObject,\n  roundTo,\n} from \"./impl/util.js\";\nimport Settings from \"./settings.js\";\nimport DateTime from \"./datetime.js\";\n\nconst INVALID = \"Invalid Duration\";\n\n// unit conversion constants\nexport const lowOrderMatrix = {\n    weeks: {\n      days: 7,\n      hours: 7 * 24,\n      minutes: 7 * 24 * 60,\n      seconds: 7 * 24 * 60 * 60,\n      milliseconds: 7 * 24 * 60 * 60 * 1000,\n    },\n    days: {\n      hours: 24,\n      minutes: 24 * 60,\n      seconds: 24 * 60 * 60,\n      milliseconds: 24 * 60 * 60 * 1000,\n    },\n    hours: { minutes: 60, seconds: 60 * 60, milliseconds: 60 * 60 * 1000 },\n    minutes: { seconds: 60, milliseconds: 60 * 1000 },\n    seconds: { milliseconds: 1000 },\n  },\n  casualMatrix = {\n    years: {\n      quarters: 4,\n      months: 12,\n      weeks: 52,\n      days: 365,\n      hours: 365 * 24,\n      minutes: 365 * 24 * 60,\n      seconds: 365 * 24 * 60 * 60,\n      milliseconds: 365 * 24 * 60 * 60 * 1000,\n    },\n    quarters: {\n      months: 3,\n      weeks: 13,\n      days: 91,\n      hours: 91 * 24,\n      minutes: 91 * 24 * 60,\n      seconds: 91 * 24 * 60 * 60,\n      milliseconds: 91 * 24 * 60 * 60 * 1000,\n    },\n    months: {\n      weeks: 4,\n      days: 30,\n      hours: 30 * 24,\n      minutes: 30 * 24 * 60,\n      seconds: 30 * 24 * 60 * 60,\n      milliseconds: 30 * 24 * 60 * 60 * 1000,\n    },\n\n    ...lowOrderMatrix,\n  },\n  daysInYearAccurate = 146097.0 / 400,\n  daysInMonthAccurate = 146097.0 / 4800,\n  accurateMatrix = {\n    years: {\n      quarters: 4,\n      months: 12,\n      weeks: daysInYearAccurate / 7,\n      days: daysInYearAccurate,\n      hours: daysInYearAccurate * 24,\n      minutes: daysInYearAccurate * 24 * 60,\n      seconds: daysInYearAccurate * 24 * 60 * 60,\n      milliseconds: daysInYearAccurate * 24 * 60 * 60 * 1000,\n    },\n    quarters: {\n      months: 3,\n      weeks: daysInYearAccurate / 28,\n      days: daysInYearAccurate / 4,\n      hours: (daysInYearAccurate * 24) / 4,\n      minutes: (daysInYearAccurate * 24 * 60) / 4,\n      seconds: (daysInYearAccurate * 24 * 60 * 60) / 4,\n      milliseconds: (daysInYearAccurate * 24 * 60 * 60 * 1000) / 4,\n    },\n    months: {\n      weeks: daysInMonthAccurate / 7,\n      days: daysInMonthAccurate,\n      hours: daysInMonthAccurate * 24,\n      minutes: daysInMonthAccurate * 24 * 60,\n      seconds: daysInMonthAccurate * 24 * 60 * 60,\n      milliseconds: daysInMonthAccurate * 24 * 60 * 60 * 1000,\n    },\n    ...lowOrderMatrix,\n  };\n\n// units ordered by size\nconst orderedUnits = [\n  \"years\",\n  \"quarters\",\n  \"months\",\n  \"weeks\",\n  \"days\",\n  \"hours\",\n  \"minutes\",\n  \"seconds\",\n  \"milliseconds\",\n];\n\nconst reverseUnits = orderedUnits.slice(0).reverse();\n\n// clone really means \"create another instance just like this one, but with these changes\"\nfunction clone(dur, alts, clear = false) {\n  // deep merge for vals\n  const conf = {\n    values: clear ? alts.values : { ...dur.values, ...(alts.values || {}) },\n    loc: dur.loc.clone(alts.loc),\n    conversionAccuracy: alts.conversionAccuracy || dur.conversionAccuracy,\n    matrix: alts.matrix || dur.matrix,\n  };\n  return new Duration(conf);\n}\n\nfunction durationToMillis(matrix, vals) {\n  let sum = vals.milliseconds ?? 0;\n  for (const unit of reverseUnits.slice(1)) {\n    if (vals[unit]) {\n      sum += vals[unit] * matrix[unit][\"milliseconds\"];\n    }\n  }\n  return sum;\n}\n\n// NB: mutates parameters\nfunction normalizeValues(matrix, vals) {\n  // the logic below assumes the overall value of the duration is positive\n  // if this is not the case, factor is used to make it so\n  const factor = durationToMillis(matrix, vals) < 0 ? -1 : 1;\n\n  orderedUnits.reduceRight((previous, current) => {\n    if (!isUndefined(vals[current])) {\n      if (previous) {\n        const previousVal = vals[previous] * factor;\n        const conv = matrix[current][previous];\n\n        // if (previousVal < 0):\n        // lower order unit is negative (e.g. { years: 2, days: -2 })\n        // normalize this by reducing the higher order unit by the appropriate amount\n        // and increasing the lower order unit\n        // this can never make the higher order unit negative, because this function only operates\n        // on positive durations, so the amount of time represented by the lower order unit cannot\n        // be larger than the higher order unit\n        // else:\n        // lower order unit is positive (e.g. { years: 2, days: 450 } or { years: -2, days: 450 })\n        // in this case we attempt to convert as much as possible from the lower order unit into\n        // the higher order one\n        //\n        // Math.floor takes care of both of these cases, rounding away from 0\n        // if previousVal < 0 it makes the absolute value larger\n        // if previousVal >= it makes the absolute value smaller\n        const rollUp = Math.floor(previousVal / conv);\n        vals[current] += rollUp * factor;\n        vals[previous] -= rollUp * conv * factor;\n      }\n      return current;\n    } else {\n      return previous;\n    }\n  }, null);\n\n  // try to convert any decimals into smaller units if possible\n  // for example for { years: 2.5, days: 0, seconds: 0 } we want to get { years: 2, days: 182, hours: 12 }\n  orderedUnits.reduce((previous, current) => {\n    if (!isUndefined(vals[current])) {\n      if (previous) {\n        const fraction = vals[previous] % 1;\n        vals[previous] -= fraction;\n        vals[current] += fraction * matrix[previous][current];\n      }\n      return current;\n    } else {\n      return previous;\n    }\n  }, null);\n}\n\n// Remove all properties with a value of 0 from an object\nfunction removeZeroes(vals) {\n  const newVals = {};\n  for (const [key, value] of Object.entries(vals)) {\n    if (value !== 0) {\n      newVals[key] = value;\n    }\n  }\n  return newVals;\n}\n\n/**\n * A Duration object represents a period of time, like \"2 months\" or \"1 day, 1 hour\". Conceptually, it's just a map of units to their quantities, accompanied by some additional configuration and methods for creating, parsing, interrogating, transforming, and formatting them. They can be used on their own or in conjunction with other Luxon types; for example, you can use {@link DateTime#plus} to add a Duration object to a DateTime, producing another DateTime.\n *\n * Here is a brief overview of commonly used methods and getters in Duration:\n *\n * * **Creation** To create a Duration, use {@link Duration.fromMillis}, {@link Duration.fromObject}, or {@link Duration.fromISO}.\n * * **Unit values** See the {@link Duration#years}, {@link Duration#months}, {@link Duration#weeks}, {@link Duration#days}, {@link Duration#hours}, {@link Duration#minutes}, {@link Duration#seconds}, {@link Duration#milliseconds} accessors.\n * * **Configuration** See  {@link Duration#locale} and {@link Duration#numberingSystem} accessors.\n * * **Transformation** To create new Durations out of old ones use {@link Duration#plus}, {@link Duration#minus}, {@link Duration#normalize}, {@link Duration#set}, {@link Duration#reconfigure}, {@link Duration#shiftTo}, and {@link Duration#negate}.\n * * **Output** To convert the Duration into other representations, see {@link Duration#as}, {@link Duration#toISO}, {@link Duration#toFormat}, and {@link Duration#toJSON}\n *\n * There's are more methods documented below. In addition, for more information on subtler topics like internationalization and validity, see the external documentation.\n */\nexport default class Duration {\n  /**\n   * @private\n   */\n  constructor(config) {\n    const accurate = config.conversionAccuracy === \"longterm\" || false;\n    let matrix = accurate ? accurateMatrix : casualMatrix;\n\n    if (config.matrix) {\n      matrix = config.matrix;\n    }\n\n    /**\n     * @access private\n     */\n    this.values = config.values;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.conversionAccuracy = accurate ? \"longterm\" : \"casual\";\n    /**\n     * @access private\n     */\n    this.invalid = config.invalid || null;\n    /**\n     * @access private\n     */\n    this.matrix = matrix;\n    /**\n     * @access private\n     */\n    this.isLuxonDuration = true;\n  }\n\n  /**\n   * Create Duration from a number of milliseconds.\n   * @param {number} count of milliseconds\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  static fromMillis(count, opts) {\n    return Duration.fromObject({ milliseconds: count }, opts);\n  }\n\n  /**\n   * Create a Duration from a JavaScript object with keys like 'years' and 'hours'.\n   * If this object is empty then a zero milliseconds duration is returned.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.years\n   * @param {number} obj.quarters\n   * @param {number} obj.months\n   * @param {number} obj.weeks\n   * @param {number} obj.days\n   * @param {number} obj.hours\n   * @param {number} obj.minutes\n   * @param {number} obj.seconds\n   * @param {number} obj.milliseconds\n   * @param {Object} [opts=[]] - options for creating this Duration\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the custom conversion system to use\n   * @return {Duration}\n   */\n  static fromObject(obj, opts = {}) {\n    if (obj == null || typeof obj !== \"object\") {\n      throw new InvalidArgumentError(\n        `Duration.fromObject: argument expected to be an object, got ${\n          obj === null ? \"null\" : typeof obj\n        }`\n      );\n    }\n\n    return new Duration({\n      values: normalizeObject(obj, Duration.normalizeUnit),\n      loc: Locale.fromObject(opts),\n      conversionAccuracy: opts.conversionAccuracy,\n      matrix: opts.matrix,\n    });\n  }\n\n  /**\n   * Create a Duration from DurationLike.\n   *\n   * @param {Object | number | Duration} durationLike\n   * One of:\n   * - object with keys like 'years' and 'hours'.\n   * - number representing milliseconds\n   * - Duration instance\n   * @return {Duration}\n   */\n  static fromDurationLike(durationLike) {\n    if (isNumber(durationLike)) {\n      return Duration.fromMillis(durationLike);\n    } else if (Duration.isDuration(durationLike)) {\n      return durationLike;\n    } else if (typeof durationLike === \"object\") {\n      return Duration.fromObject(durationLike);\n    } else {\n      throw new InvalidArgumentError(\n        `Unknown duration argument ${durationLike} of type ${typeof durationLike}`\n      );\n    }\n  }\n\n  /**\n   * Create a Duration from an ISO 8601 duration string.\n   * @param {string} text - text to parse\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the preset conversion system to use\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations\n   * @example Duration.fromISO('P3Y6M1W4DT12H30M5S').toObject() //=> { years: 3, months: 6, weeks: 1, days: 4, hours: 12, minutes: 30, seconds: 5 }\n   * @example Duration.fromISO('PT23H').toObject() //=> { hours: 23 }\n   * @example Duration.fromISO('P5Y3M').toObject() //=> { years: 5, months: 3 }\n   * @return {Duration}\n   */\n  static fromISO(text, opts) {\n    const [parsed] = parseISODuration(text);\n    if (parsed) {\n      return Duration.fromObject(parsed, opts);\n    } else {\n      return Duration.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n    }\n  }\n\n  /**\n   * Create a Duration from an ISO 8601 time string.\n   * @param {string} text - text to parse\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the conversion system to use\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Times\n   * @example Duration.fromISOTime('11:22:33.444').toObject() //=> { hours: 11, minutes: 22, seconds: 33, milliseconds: 444 }\n   * @example Duration.fromISOTime('11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('T11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('T1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @return {Duration}\n   */\n  static fromISOTime(text, opts) {\n    const [parsed] = parseISOTimeOnly(text);\n    if (parsed) {\n      return Duration.fromObject(parsed, opts);\n    } else {\n      return Duration.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n    }\n  }\n\n  /**\n   * Create an invalid Duration.\n   * @param {string} reason - simple string of why this datetime is invalid. Should not contain parameters or anything else data-dependent\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {Duration}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the Duration is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDurationError(invalid);\n    } else {\n      return new Duration({ invalid });\n    }\n  }\n\n  /**\n   * @private\n   */\n  static normalizeUnit(unit) {\n    const normalized = {\n      year: \"years\",\n      years: \"years\",\n      quarter: \"quarters\",\n      quarters: \"quarters\",\n      month: \"months\",\n      months: \"months\",\n      week: \"weeks\",\n      weeks: \"weeks\",\n      day: \"days\",\n      days: \"days\",\n      hour: \"hours\",\n      hours: \"hours\",\n      minute: \"minutes\",\n      minutes: \"minutes\",\n      second: \"seconds\",\n      seconds: \"seconds\",\n      millisecond: \"milliseconds\",\n      milliseconds: \"milliseconds\",\n    }[unit ? unit.toLowerCase() : unit];\n\n    if (!normalized) throw new InvalidUnitError(unit);\n\n    return normalized;\n  }\n\n  /**\n   * Check if an object is a Duration. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDuration(o) {\n    return (o && o.isLuxonDuration) || false;\n  }\n\n  /**\n   * Get  the locale of a Duration, such 'en-GB'\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a Duration, such 'beng'. The numbering system is used when formatting the Duration\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Returns a string representation of this Duration formatted according to the specified format string. You may use these tokens:\n   * * `S` for milliseconds\n   * * `s` for seconds\n   * * `m` for minutes\n   * * `h` for hours\n   * * `d` for days\n   * * `w` for weeks\n   * * `M` for months\n   * * `y` for years\n   * Notes:\n   * * Add padding by repeating the token, e.g. \"yy\" pads the years to two digits, \"hhhh\" pads the hours out to four digits\n   * * Tokens can be escaped by wrapping with single quotes.\n   * * The duration will be converted to the set of units in the format string using {@link Duration#shiftTo} and the Durations's conversion accuracy setting.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - options\n   * @param {boolean} [opts.floor=true] - floor numerical values\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"y d s\") //=> \"1 6 2\"\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"yy dd sss\") //=> \"01 06 002\"\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"M S\") //=> \"12 518402000\"\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    // reverse-compat since 1.2; we always round down now, never up, and we do it by default\n    const fmtOpts = {\n      ...opts,\n      floor: opts.round !== false && opts.floor !== false,\n    };\n    return this.isValid\n      ? Formatter.create(this.loc, fmtOpts).formatDurationFromString(this, fmt)\n      : INVALID;\n  }\n\n  /**\n   * Returns a string representation of a Duration with all units included.\n   * To modify its behavior, use `listStyle` and any Intl.NumberFormat option, though `unitDisplay` is especially relevant.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat#options\n   * @param {Object} opts - Formatting options. Accepts the same keys as the options parameter of the native `Intl.NumberFormat` constructor, as well as `listStyle`.\n   * @param {string} [opts.listStyle='narrow'] - How to format the merged list. Corresponds to the `style` property of the options parameter of the native `Intl.ListFormat` constructor.\n   * @example\n   * ```js\n   * var dur = Duration.fromObject({ days: 1, hours: 5, minutes: 6 })\n   * dur.toHuman() //=> '1 day, 5 hours, 6 minutes'\n   * dur.toHuman({ listStyle: \"long\" }) //=> '1 day, 5 hours, and 6 minutes'\n   * dur.toHuman({ unitDisplay: \"short\" }) //=> '1 day, 5 hr, 6 min'\n   * ```\n   */\n  toHuman(opts = {}) {\n    if (!this.isValid) return INVALID;\n\n    const l = orderedUnits\n      .map((unit) => {\n        const val = this.values[unit];\n        if (isUndefined(val)) {\n          return null;\n        }\n        return this.loc\n          .numberFormatter({ style: \"unit\", unitDisplay: \"long\", ...opts, unit: unit.slice(0, -1) })\n          .format(val);\n      })\n      .filter((n) => n);\n\n    return this.loc\n      .listFormatter({ type: \"conjunction\", style: opts.listStyle || \"narrow\", ...opts })\n      .format(l);\n  }\n\n  /**\n   * Returns a JavaScript object with this Duration's values.\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toObject() //=> { years: 1, days: 6, seconds: 2 }\n   * @return {Object}\n   */\n  toObject() {\n    if (!this.isValid) return {};\n    return { ...this.values };\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Duration.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations\n   * @example Duration.fromObject({ years: 3, seconds: 45 }).toISO() //=> 'P3YT45S'\n   * @example Duration.fromObject({ months: 4, seconds: 45 }).toISO() //=> 'P4MT45S'\n   * @example Duration.fromObject({ months: 5 }).toISO() //=> 'P5M'\n   * @example Duration.fromObject({ minutes: 5 }).toISO() //=> 'PT5M'\n   * @example Duration.fromObject({ milliseconds: 6 }).toISO() //=> 'PT0.006S'\n   * @return {string}\n   */\n  toISO() {\n    // we could use the formatter, but this is an easier way to get the minimum string\n    if (!this.isValid) return null;\n\n    let s = \"P\";\n    if (this.years !== 0) s += this.years + \"Y\";\n    if (this.months !== 0 || this.quarters !== 0) s += this.months + this.quarters * 3 + \"M\";\n    if (this.weeks !== 0) s += this.weeks + \"W\";\n    if (this.days !== 0) s += this.days + \"D\";\n    if (this.hours !== 0 || this.minutes !== 0 || this.seconds !== 0 || this.milliseconds !== 0)\n      s += \"T\";\n    if (this.hours !== 0) s += this.hours + \"H\";\n    if (this.minutes !== 0) s += this.minutes + \"M\";\n    if (this.seconds !== 0 || this.milliseconds !== 0)\n      // this will handle \"floating point madness\" by removing extra decimal places\n      // https://stackoverflow.com/questions/588004/is-floating-point-math-broken\n      s += roundTo(this.seconds + this.milliseconds / 1000, 3) + \"S\";\n    if (s === \"P\") s += \"T0S\";\n    return s;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Duration, formatted as a time of day.\n   * Note that this will return null if the duration is invalid, negative, or equal to or greater than 24 hours.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Times\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example Duration.fromObject({ hours: 11 }).toISOTime() //=> '11:00:00.000'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressMilliseconds: true }) //=> '11:00:00'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressSeconds: true }) //=> '11:00'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ includePrefix: true }) //=> 'T11:00:00.000'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ format: 'basic' }) //=> '110000.000'\n   * @return {string}\n   */\n  toISOTime(opts = {}) {\n    if (!this.isValid) return null;\n\n    const millis = this.toMillis();\n    if (millis < 0 || millis >= 86400000) return null;\n\n    opts = {\n      suppressMilliseconds: false,\n      suppressSeconds: false,\n      includePrefix: false,\n      format: \"extended\",\n      ...opts,\n      includeOffset: false,\n    };\n\n    const dateTime = DateTime.fromMillis(millis, { zone: \"UTC\" });\n    return dateTime.toISOTime(opts);\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this Duration appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this Duration appropriate for use in debugging.\n   * @return {string}\n   */\n  toString() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a string representation of this Duration appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `Duration { values: ${JSON.stringify(this.values)} }`;\n    } else {\n      return `Duration { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns an milliseconds value of this Duration.\n   * @return {number}\n   */\n  toMillis() {\n    if (!this.isValid) return NaN;\n\n    return durationToMillis(this.matrix, this.values);\n  }\n\n  /**\n   * Returns an milliseconds value of this Duration. Alias of {@link toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Make this Duration longer by the specified amount. Return a newly-constructed Duration.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @return {Duration}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n\n    const dur = Duration.fromDurationLike(duration),\n      result = {};\n\n    for (const k of orderedUnits) {\n      if (hasOwnProperty(dur.values, k) || hasOwnProperty(this.values, k)) {\n        result[k] = dur.get(k) + this.get(k);\n      }\n    }\n\n    return clone(this, { values: result }, true);\n  }\n\n  /**\n   * Make this Duration shorter by the specified amount. Return a newly-constructed Duration.\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @return {Duration}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n\n    const dur = Duration.fromDurationLike(duration);\n    return this.plus(dur.negate());\n  }\n\n  /**\n   * Scale this Duration by the specified amount. Return a newly-constructed Duration.\n   * @param {function} fn - The function to apply to each unit. Arity is 1 or 2: the value of the unit and, optionally, the unit name. Must return a number.\n   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits(x => x * 2) //=> { hours: 2, minutes: 60 }\n   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits((x, u) => u === \"hours\" ? x * 2 : x) //=> { hours: 2, minutes: 30 }\n   * @return {Duration}\n   */\n  mapUnits(fn) {\n    if (!this.isValid) return this;\n    const result = {};\n    for (const k of Object.keys(this.values)) {\n      result[k] = asNumber(fn(this.values[k], k));\n    }\n    return clone(this, { values: result }, true);\n  }\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example Duration.fromObject({years: 2, days: 3}).get('years') //=> 2\n   * @example Duration.fromObject({years: 2, days: 3}).get('months') //=> 0\n   * @example Duration.fromObject({years: 2, days: 3}).get('days') //=> 3\n   * @return {number}\n   */\n  get(unit) {\n    return this[Duration.normalizeUnit(unit)];\n  }\n\n  /**\n   * \"Set\" the values of specified units. Return a newly-constructed Duration.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dur.set({ years: 2017 })\n   * @example dur.set({ hours: 8, minutes: 30 })\n   * @return {Duration}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n\n    const mixed = { ...this.values, ...normalizeObject(values, Duration.normalizeUnit) };\n    return clone(this, { values: mixed });\n  }\n\n  /**\n   * \"Set\" the locale and/or numberingSystem.  Returns a newly-constructed Duration.\n   * @example dur.reconfigure({ locale: 'en-GB' })\n   * @return {Duration}\n   */\n  reconfigure({ locale, numberingSystem, conversionAccuracy, matrix } = {}) {\n    const loc = this.loc.clone({ locale, numberingSystem });\n    const opts = { loc, matrix, conversionAccuracy };\n    return clone(this, opts);\n  }\n\n  /**\n   * Return the length of the duration in the specified unit.\n   * @param {string} unit - a unit such as 'minutes' or 'days'\n   * @example Duration.fromObject({years: 1}).as('days') //=> 365\n   * @example Duration.fromObject({years: 1}).as('months') //=> 12\n   * @example Duration.fromObject({hours: 60}).as('days') //=> 2.5\n   * @return {number}\n   */\n  as(unit) {\n    return this.isValid ? this.shiftTo(unit).get(unit) : NaN;\n  }\n\n  /**\n   * Reduce this Duration to its canonical representation in its current units.\n   * Assuming the overall value of the Duration is positive, this means:\n   * - excessive values for lower-order units are converted to higher-order units (if possible, see first and second example)\n   * - negative lower-order units are converted to higher order units (there must be such a higher order unit, otherwise\n   *   the overall value would be negative, see third example)\n   * - fractional values for higher-order units are converted to lower-order units (if possible, see fourth example)\n   *\n   * If the overall value is negative, the result of this method is equivalent to `this.negate().normalize().negate()`.\n   * @example Duration.fromObject({ years: 2, days: 5000 }).normalize().toObject() //=> { years: 15, days: 255 }\n   * @example Duration.fromObject({ days: 5000 }).normalize().toObject() //=> { days: 5000 }\n   * @example Duration.fromObject({ hours: 12, minutes: -45 }).normalize().toObject() //=> { hours: 11, minutes: 15 }\n   * @example Duration.fromObject({ years: 2.5, days: 0, hours: 0 }).normalize().toObject() //=> { years: 2, days: 182, hours: 12 }\n   * @return {Duration}\n   */\n  normalize() {\n    if (!this.isValid) return this;\n    const vals = this.toObject();\n    normalizeValues(this.matrix, vals);\n    return clone(this, { values: vals }, true);\n  }\n\n  /**\n   * Rescale units to its largest representation\n   * @example Duration.fromObject({ milliseconds: 90000 }).rescale().toObject() //=> { minutes: 1, seconds: 30 }\n   * @return {Duration}\n   */\n  rescale() {\n    if (!this.isValid) return this;\n    const vals = removeZeroes(this.normalize().shiftToAll().toObject());\n    return clone(this, { values: vals }, true);\n  }\n\n  /**\n   * Convert this Duration into its representation in a different set of units.\n   * @example Duration.fromObject({ hours: 1, seconds: 30 }).shiftTo('minutes', 'milliseconds').toObject() //=> { minutes: 60, milliseconds: 30000 }\n   * @return {Duration}\n   */\n  shiftTo(...units) {\n    if (!this.isValid) return this;\n\n    if (units.length === 0) {\n      return this;\n    }\n\n    units = units.map((u) => Duration.normalizeUnit(u));\n\n    const built = {},\n      accumulated = {},\n      vals = this.toObject();\n    let lastUnit;\n\n    for (const k of orderedUnits) {\n      if (units.indexOf(k) >= 0) {\n        lastUnit = k;\n\n        let own = 0;\n\n        // anything we haven't boiled down yet should get boiled to this unit\n        for (const ak in accumulated) {\n          own += this.matrix[ak][k] * accumulated[ak];\n          accumulated[ak] = 0;\n        }\n\n        // plus anything that's already in this unit\n        if (isNumber(vals[k])) {\n          own += vals[k];\n        }\n\n        // only keep the integer part for now in the hopes of putting any decimal part\n        // into a smaller unit later\n        const i = Math.trunc(own);\n        built[k] = i;\n        accumulated[k] = (own * 1000 - i * 1000) / 1000;\n\n        // otherwise, keep it in the wings to boil it later\n      } else if (isNumber(vals[k])) {\n        accumulated[k] = vals[k];\n      }\n    }\n\n    // anything leftover becomes the decimal for the last unit\n    // lastUnit must be defined since units is not empty\n    for (const key in accumulated) {\n      if (accumulated[key] !== 0) {\n        built[lastUnit] +=\n          key === lastUnit ? accumulated[key] : accumulated[key] / this.matrix[lastUnit][key];\n      }\n    }\n\n    normalizeValues(this.matrix, built);\n    return clone(this, { values: built }, true);\n  }\n\n  /**\n   * Shift this Duration to all available units.\n   * Same as shiftTo(\"years\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", \"seconds\", \"milliseconds\")\n   * @return {Duration}\n   */\n  shiftToAll() {\n    if (!this.isValid) return this;\n    return this.shiftTo(\n      \"years\",\n      \"months\",\n      \"weeks\",\n      \"days\",\n      \"hours\",\n      \"minutes\",\n      \"seconds\",\n      \"milliseconds\"\n    );\n  }\n\n  /**\n   * Return the negative of this Duration.\n   * @example Duration.fromObject({ hours: 1, seconds: 30 }).negate().toObject() //=> { hours: -1, seconds: -30 }\n   * @return {Duration}\n   */\n  negate() {\n    if (!this.isValid) return this;\n    const negated = {};\n    for (const k of Object.keys(this.values)) {\n      negated[k] = this.values[k] === 0 ? 0 : -this.values[k];\n    }\n    return clone(this, { values: negated }, true);\n  }\n\n  /**\n   * Get the years.\n   * @type {number}\n   */\n  get years() {\n    return this.isValid ? this.values.years || 0 : NaN;\n  }\n\n  /**\n   * Get the quarters.\n   * @type {number}\n   */\n  get quarters() {\n    return this.isValid ? this.values.quarters || 0 : NaN;\n  }\n\n  /**\n   * Get the months.\n   * @type {number}\n   */\n  get months() {\n    return this.isValid ? this.values.months || 0 : NaN;\n  }\n\n  /**\n   * Get the weeks\n   * @type {number}\n   */\n  get weeks() {\n    return this.isValid ? this.values.weeks || 0 : NaN;\n  }\n\n  /**\n   * Get the days.\n   * @type {number}\n   */\n  get days() {\n    return this.isValid ? this.values.days || 0 : NaN;\n  }\n\n  /**\n   * Get the hours.\n   * @type {number}\n   */\n  get hours() {\n    return this.isValid ? this.values.hours || 0 : NaN;\n  }\n\n  /**\n   * Get the minutes.\n   * @type {number}\n   */\n  get minutes() {\n    return this.isValid ? this.values.minutes || 0 : NaN;\n  }\n\n  /**\n   * Get the seconds.\n   * @return {number}\n   */\n  get seconds() {\n    return this.isValid ? this.values.seconds || 0 : NaN;\n  }\n\n  /**\n   * Get the milliseconds.\n   * @return {number}\n   */\n  get milliseconds() {\n    return this.isValid ? this.values.milliseconds || 0 : NaN;\n  }\n\n  /**\n   * Returns whether the Duration is invalid. Invalid durations are returned by diff operations\n   * on invalid DateTimes or Intervals.\n   * @return {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this Duration became invalid, or null if the Duration is valid\n   * @return {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this Duration became invalid, or null if the Duration is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Equality check\n   * Two Durations are equal iff they have the same units and the same values for each unit.\n   * @param {Duration} other\n   * @return {boolean}\n   */\n  equals(other) {\n    if (!this.isValid || !other.isValid) {\n      return false;\n    }\n\n    if (!this.loc.equals(other.loc)) {\n      return false;\n    }\n\n    function eq(v1, v2) {\n      // Consider 0 and undefined as equal\n      if (v1 === undefined || v1 === 0) return v2 === undefined || v2 === 0;\n      return v1 === v2;\n    }\n\n    for (const u of orderedUnits) {\n      if (!eq(this.values[u], other.values[u])) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n", "import DateTime, { friendlyDateTime } from \"./datetime.js\";\nimport Duration from \"./duration.js\";\nimport Settings from \"./settings.js\";\nimport { InvalidArgumentError, InvalidIntervalError } from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport * as Formats from \"./impl/formats.js\";\n\nconst INVALID = \"Invalid Interval\";\n\n// checks if the start is equal to or before the end\nfunction validateStartEnd(start, end) {\n  if (!start || !start.isValid) {\n    return Interval.invalid(\"missing or invalid start\");\n  } else if (!end || !end.isValid) {\n    return Interval.invalid(\"missing or invalid end\");\n  } else if (end < start) {\n    return Interval.invalid(\n      \"end before start\",\n      `The end of an interval must be after its start, but you had start=${start.toISO()} and end=${end.toISO()}`\n    );\n  } else {\n    return null;\n  }\n}\n\n/**\n * An Interval object represents a half-open interval of time, where each endpoint is a {@link DateTime}. Conceptually, it's a container for those two endpoints, accompanied by methods for creating, parsing, interrogating, comparing, transforming, and formatting them.\n *\n * Here is a brief overview of the most commonly used methods and getters in Interval:\n *\n * * **Creation** To create an Interval, use {@link Interval.fromDateTimes}, {@link Interval.after}, {@link Interval.before}, or {@link Interval.fromISO}.\n * * **Accessors** Use {@link Interval#start} and {@link Interval#end} to get the start and end.\n * * **Interrogation** To analyze the Interval, use {@link Interval#count}, {@link Interval#length}, {@link Interval#hasSame}, {@link Interval#contains}, {@link Interval#isAfter}, or {@link Interval#isBefore}.\n * * **Transformation** To create other Intervals out of this one, use {@link Interval#set}, {@link Interval#splitAt}, {@link Interval#splitBy}, {@link Interval#divideEqually}, {@link Interval.merge}, {@link Interval.xor}, {@link Interval#union}, {@link Interval#intersection}, or {@link Interval#difference}.\n * * **Comparison** To compare this Interval to another one, use {@link Interval#equals}, {@link Interval#overlaps}, {@link Interval#abutsStart}, {@link Interval#abutsEnd}, {@link Interval#engulfs}\n * * **Output** To convert the Interval into other representations, see {@link Interval#toString}, {@link Interval#toLocaleString}, {@link Interval#toISO}, {@link Interval#toISODate}, {@link Interval#toISOTime}, {@link Interval#toFormat}, and {@link Interval#toDuration}.\n */\nexport default class Interval {\n  /**\n   * @private\n   */\n  constructor(config) {\n    /**\n     * @access private\n     */\n    this.s = config.start;\n    /**\n     * @access private\n     */\n    this.e = config.end;\n    /**\n     * @access private\n     */\n    this.invalid = config.invalid || null;\n    /**\n     * @access private\n     */\n    this.isLuxonInterval = true;\n  }\n\n  /**\n   * Create an invalid Interval.\n   * @param {string} reason - simple string of why this Interval is invalid. Should not contain parameters or anything else data-dependent\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {Interval}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the Interval is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidIntervalError(invalid);\n    } else {\n      return new Interval({ invalid });\n    }\n  }\n\n  /**\n   * Create an Interval from a start DateTime and an end DateTime. Inclusive of the start but not the end.\n   * @param {DateTime|Date|Object} start\n   * @param {DateTime|Date|Object} end\n   * @return {Interval}\n   */\n  static fromDateTimes(start, end) {\n    const builtStart = friendlyDateTime(start),\n      builtEnd = friendlyDateTime(end);\n\n    const validateError = validateStartEnd(builtStart, builtEnd);\n\n    if (validateError == null) {\n      return new Interval({\n        start: builtStart,\n        end: builtEnd,\n      });\n    } else {\n      return validateError;\n    }\n  }\n\n  /**\n   * Create an Interval from a start DateTime and a Duration to extend to.\n   * @param {DateTime|Date|Object} start\n   * @param {Duration|Object|number} duration - the length of the Interval.\n   * @return {Interval}\n   */\n  static after(start, duration) {\n    const dur = Duration.fromDurationLike(duration),\n      dt = friendlyDateTime(start);\n    return Interval.fromDateTimes(dt, dt.plus(dur));\n  }\n\n  /**\n   * Create an Interval from an end DateTime and a Duration to extend backwards to.\n   * @param {DateTime|Date|Object} end\n   * @param {Duration|Object|number} duration - the length of the Interval.\n   * @return {Interval}\n   */\n  static before(end, duration) {\n    const dur = Duration.fromDurationLike(duration),\n      dt = friendlyDateTime(end);\n    return Interval.fromDateTimes(dt.minus(dur), dt);\n  }\n\n  /**\n   * Create an Interval from an ISO 8601 string.\n   * Accepts `<start>/<end>`, `<start>/<duration>`, and `<duration>/<end>` formats.\n   * @param {string} text - the ISO string to parse\n   * @param {Object} [opts] - options to pass {@link DateTime#fromISO} and optionally {@link Duration#fromISO}\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @return {Interval}\n   */\n  static fromISO(text, opts) {\n    const [s, e] = (text || \"\").split(\"/\", 2);\n    if (s && e) {\n      let start, startIsValid;\n      try {\n        start = DateTime.fromISO(s, opts);\n        startIsValid = start.isValid;\n      } catch (e) {\n        startIsValid = false;\n      }\n\n      let end, endIsValid;\n      try {\n        end = DateTime.fromISO(e, opts);\n        endIsValid = end.isValid;\n      } catch (e) {\n        endIsValid = false;\n      }\n\n      if (startIsValid && endIsValid) {\n        return Interval.fromDateTimes(start, end);\n      }\n\n      if (startIsValid) {\n        const dur = Duration.fromISO(e, opts);\n        if (dur.isValid) {\n          return Interval.after(start, dur);\n        }\n      } else if (endIsValid) {\n        const dur = Duration.fromISO(s, opts);\n        if (dur.isValid) {\n          return Interval.before(end, dur);\n        }\n      }\n    }\n    return Interval.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n  }\n\n  /**\n   * Check if an object is an Interval. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isInterval(o) {\n    return (o && o.isLuxonInterval) || false;\n  }\n\n  /**\n   * Returns the start of the Interval\n   * @type {DateTime}\n   */\n  get start() {\n    return this.isValid ? this.s : null;\n  }\n\n  /**\n   * Returns the end of the Interval\n   * @type {DateTime}\n   */\n  get end() {\n    return this.isValid ? this.e : null;\n  }\n\n  /**\n   * Returns whether this Interval's end is at least its start, meaning that the Interval isn't 'backwards'.\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalidReason === null;\n  }\n\n  /**\n   * Returns an error code if this Interval is invalid, or null if the Interval is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this Interval became invalid, or null if the Interval is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Returns the length of the Interval in the specified unit.\n   * @param {string} unit - the unit (such as 'hours' or 'days') to return the length in.\n   * @return {number}\n   */\n  length(unit = \"milliseconds\") {\n    return this.isValid ? this.toDuration(...[unit]).get(unit) : NaN;\n  }\n\n  /**\n   * Returns the count of minutes, hours, days, months, or years included in the Interval, even in part.\n   * Unlike {@link Interval#length} this counts sections of the calendar, not periods of time, e.g. specifying 'day'\n   * asks 'what dates are included in this interval?', not 'how many days long is this interval?'\n   * @param {string} [unit='milliseconds'] - the unit of time to count.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; this operation will always use the locale of the start DateTime\n   * @return {number}\n   */\n  count(unit = \"milliseconds\", opts) {\n    if (!this.isValid) return NaN;\n    const start = this.start.startOf(unit, opts);\n    let end;\n    if (opts?.useLocaleWeeks) {\n      end = this.end.reconfigure({ locale: start.locale });\n    } else {\n      end = this.end;\n    }\n    end = end.startOf(unit, opts);\n    return Math.floor(end.diff(start, unit).get(unit)) + (end.valueOf() !== this.end.valueOf());\n  }\n\n  /**\n   * Returns whether this Interval's start and end are both in the same unit of time\n   * @param {string} unit - the unit of time to check sameness on\n   * @return {boolean}\n   */\n  hasSame(unit) {\n    return this.isValid ? this.isEmpty() || this.e.minus(1).hasSame(this.s, unit) : false;\n  }\n\n  /**\n   * Return whether this Interval has the same start and end DateTimes.\n   * @return {boolean}\n   */\n  isEmpty() {\n    return this.s.valueOf() === this.e.valueOf();\n  }\n\n  /**\n   * Return whether this Interval's start is after the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  isAfter(dateTime) {\n    if (!this.isValid) return false;\n    return this.s > dateTime;\n  }\n\n  /**\n   * Return whether this Interval's end is before the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  isBefore(dateTime) {\n    if (!this.isValid) return false;\n    return this.e <= dateTime;\n  }\n\n  /**\n   * Return whether this Interval contains the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  contains(dateTime) {\n    if (!this.isValid) return false;\n    return this.s <= dateTime && this.e > dateTime;\n  }\n\n  /**\n   * \"Sets\" the start and/or end dates. Returns a newly-constructed Interval.\n   * @param {Object} values - the values to set\n   * @param {DateTime} values.start - the starting DateTime\n   * @param {DateTime} values.end - the ending DateTime\n   * @return {Interval}\n   */\n  set({ start, end } = {}) {\n    if (!this.isValid) return this;\n    return Interval.fromDateTimes(start || this.s, end || this.e);\n  }\n\n  /**\n   * Split this Interval at each of the specified DateTimes\n   * @param {...DateTime} dateTimes - the unit of time to count.\n   * @return {Array}\n   */\n  splitAt(...dateTimes) {\n    if (!this.isValid) return [];\n    const sorted = dateTimes\n        .map(friendlyDateTime)\n        .filter((d) => this.contains(d))\n        .sort((a, b) => a.toMillis() - b.toMillis()),\n      results = [];\n    let { s } = this,\n      i = 0;\n\n    while (s < this.e) {\n      const added = sorted[i] || this.e,\n        next = +added > +this.e ? this.e : added;\n      results.push(Interval.fromDateTimes(s, next));\n      s = next;\n      i += 1;\n    }\n\n    return results;\n  }\n\n  /**\n   * Split this Interval into smaller Intervals, each of the specified length.\n   * Left over time is grouped into a smaller interval\n   * @param {Duration|Object|number} duration - The length of each resulting interval.\n   * @return {Array}\n   */\n  splitBy(duration) {\n    const dur = Duration.fromDurationLike(duration);\n\n    if (!this.isValid || !dur.isValid || dur.as(\"milliseconds\") === 0) {\n      return [];\n    }\n\n    let { s } = this,\n      idx = 1,\n      next;\n\n    const results = [];\n    while (s < this.e) {\n      const added = this.start.plus(dur.mapUnits((x) => x * idx));\n      next = +added > +this.e ? this.e : added;\n      results.push(Interval.fromDateTimes(s, next));\n      s = next;\n      idx += 1;\n    }\n\n    return results;\n  }\n\n  /**\n   * Split this Interval into the specified number of smaller intervals.\n   * @param {number} numberOfParts - The number of Intervals to divide the Interval into.\n   * @return {Array}\n   */\n  divideEqually(numberOfParts) {\n    if (!this.isValid) return [];\n    return this.splitBy(this.length() / numberOfParts).slice(0, numberOfParts);\n  }\n\n  /**\n   * Return whether this Interval overlaps with the specified Interval\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  overlaps(other) {\n    return this.e > other.s && this.s < other.e;\n  }\n\n  /**\n   * Return whether this Interval's end is adjacent to the specified Interval's start.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  abutsStart(other) {\n    if (!this.isValid) return false;\n    return +this.e === +other.s;\n  }\n\n  /**\n   * Return whether this Interval's start is adjacent to the specified Interval's end.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  abutsEnd(other) {\n    if (!this.isValid) return false;\n    return +other.e === +this.s;\n  }\n\n  /**\n   * Returns true if this Interval fully contains the specified Interval, specifically if the intersect (of this Interval and the other Interval) is equal to the other Interval; false otherwise.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  engulfs(other) {\n    if (!this.isValid) return false;\n    return this.s <= other.s && this.e >= other.e;\n  }\n\n  /**\n   * Return whether this Interval has the same start and end as the specified Interval.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  equals(other) {\n    if (!this.isValid || !other.isValid) {\n      return false;\n    }\n\n    return this.s.equals(other.s) && this.e.equals(other.e);\n  }\n\n  /**\n   * Return an Interval representing the intersection of this Interval and the specified Interval.\n   * Specifically, the resulting Interval has the maximum start time and the minimum end time of the two Intervals.\n   * Returns null if the intersection is empty, meaning, the intervals don't intersect.\n   * @param {Interval} other\n   * @return {Interval}\n   */\n  intersection(other) {\n    if (!this.isValid) return this;\n    const s = this.s > other.s ? this.s : other.s,\n      e = this.e < other.e ? this.e : other.e;\n\n    if (s >= e) {\n      return null;\n    } else {\n      return Interval.fromDateTimes(s, e);\n    }\n  }\n\n  /**\n   * Return an Interval representing the union of this Interval and the specified Interval.\n   * Specifically, the resulting Interval has the minimum start time and the maximum end time of the two Intervals.\n   * @param {Interval} other\n   * @return {Interval}\n   */\n  union(other) {\n    if (!this.isValid) return this;\n    const s = this.s < other.s ? this.s : other.s,\n      e = this.e > other.e ? this.e : other.e;\n    return Interval.fromDateTimes(s, e);\n  }\n\n  /**\n   * Merge an array of Intervals into a equivalent minimal set of Intervals.\n   * Combines overlapping and adjacent Intervals.\n   * @param {Array} intervals\n   * @return {Array}\n   */\n  static merge(intervals) {\n    const [found, final] = intervals\n      .sort((a, b) => a.s - b.s)\n      .reduce(\n        ([sofar, current], item) => {\n          if (!current) {\n            return [sofar, item];\n          } else if (current.overlaps(item) || current.abutsStart(item)) {\n            return [sofar, current.union(item)];\n          } else {\n            return [sofar.concat([current]), item];\n          }\n        },\n        [[], null]\n      );\n    if (final) {\n      found.push(final);\n    }\n    return found;\n  }\n\n  /**\n   * Return an array of Intervals representing the spans of time that only appear in one of the specified Intervals.\n   * @param {Array} intervals\n   * @return {Array}\n   */\n  static xor(intervals) {\n    let start = null,\n      currentCount = 0;\n    const results = [],\n      ends = intervals.map((i) => [\n        { time: i.s, type: \"s\" },\n        { time: i.e, type: \"e\" },\n      ]),\n      flattened = Array.prototype.concat(...ends),\n      arr = flattened.sort((a, b) => a.time - b.time);\n\n    for (const i of arr) {\n      currentCount += i.type === \"s\" ? 1 : -1;\n\n      if (currentCount === 1) {\n        start = i.time;\n      } else {\n        if (start && +start !== +i.time) {\n          results.push(Interval.fromDateTimes(start, i.time));\n        }\n\n        start = null;\n      }\n    }\n\n    return Interval.merge(results);\n  }\n\n  /**\n   * Return an Interval representing the span of time in this Interval that doesn't overlap with any of the specified Intervals.\n   * @param {...Interval} intervals\n   * @return {Array}\n   */\n  difference(...intervals) {\n    return Interval.xor([this].concat(intervals))\n      .map((i) => this.intersection(i))\n      .filter((i) => i && !i.isEmpty());\n  }\n\n  /**\n   * Returns a string representation of this Interval appropriate for debugging.\n   * @return {string}\n   */\n  toString() {\n    if (!this.isValid) return INVALID;\n    return `[${this.s.toISO()} – ${this.e.toISO()})`;\n  }\n\n  /**\n   * Returns a string representation of this Interval appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`;\n    } else {\n      return `Interval { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns a localized string representing this Interval. Accepts the same options as the\n   * Intl.DateTimeFormat constructor and any presets defined by Luxon, such as\n   * {@link DateTime.DATE_FULL} or {@link DateTime.TIME_SIMPLE}. The exact behavior of this method\n   * is browser-specific, but in general it will return an appropriate representation of the\n   * Interval in the assigned locale. Defaults to the system's locale if no locale has been\n   * specified.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {Object} [formatOpts=DateTime.DATE_SHORT] - Either a DateTime preset or\n   * Intl.DateTimeFormat constructor options.\n   * @param {Object} opts - Options to override the configuration of the start DateTime.\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(); //=> 11/7/2022 – 11/8/2022\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL); //=> November 7 – 8, 2022\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL, { locale: 'fr-FR' }); //=> 7–8 novembre 2022\n   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString(DateTime.TIME_SIMPLE); //=> 6:00 – 8:00 PM\n   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> Mon, Nov 07, 6:00 – 8:00 p\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.s.loc.clone(opts), formatOpts).formatInterval(this)\n      : INVALID;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Interval.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @param {Object} opts - The same options as {@link DateTime#toISO}\n   * @return {string}\n   */\n  toISO(opts) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISO(opts)}/${this.e.toISO(opts)}`;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of date of this Interval.\n   * The time components are ignored.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @return {string}\n   */\n  toISODate() {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISODate()}/${this.e.toISODate()}`;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of time of this Interval.\n   * The date components are ignored.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @param {Object} opts - The same options as {@link DateTime#toISO}\n   * @return {string}\n   */\n  toISOTime(opts) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISOTime(opts)}/${this.e.toISOTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this Interval formatted according to the specified format\n   * string. **You may not want this.** See {@link Interval#toLocaleString} for a more flexible\n   * formatting tool.\n   * @param {string} dateFormat - The format string. This string formats the start and end time.\n   * See {@link DateTime#toFormat} for details.\n   * @param {Object} opts - Options.\n   * @param {string} [opts.separator =  ' – '] - A separator to place between the start and end\n   * representations.\n   * @return {string}\n   */\n  toFormat(dateFormat, { separator = \" – \" } = {}) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toFormat(dateFormat)}${separator}${this.e.toFormat(dateFormat)}`;\n  }\n\n  /**\n   * Return a Duration representing the time spanned by this interval.\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration().toObject() //=> { milliseconds: 88489257 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration('days').toObject() //=> { days: 1.0241812152777778 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes']).toObject() //=> { hours: 24, minutes: 34.82095 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes', 'seconds']).toObject() //=> { hours: 24, minutes: 34, seconds: 49.257 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration('seconds').toObject() //=> { seconds: 88489.257 }\n   * @return {Duration}\n   */\n  toDuration(unit, opts) {\n    if (!this.isValid) {\n      return Duration.invalid(this.invalidReason);\n    }\n    return this.e.diff(this.s, unit, opts);\n  }\n\n  /**\n   * Run mapFn on the interval start and end, returning a new Interval from the resulting DateTimes\n   * @param {function} mapFn\n   * @return {Interval}\n   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.toUTC())\n   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.plus({ hours: 2 }))\n   */\n  mapEndpoints(mapFn) {\n    return Interval.fromDateTimes(mapFn(this.s), mapFn(this.e));\n  }\n}\n", "import DateTime from \"./datetime.js\";\nimport Settings from \"./settings.js\";\nimport Locale from \"./impl/locale.js\";\nimport IANAZone from \"./zones/IANAZone.js\";\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\n\nimport { hasLocaleWeekInfo, hasRelative } from \"./impl/util.js\";\n\n/**\n * The Info class contains static methods for retrieving general time and date related data. For example, it has methods for finding out if a time zone has a DST, for listing the months in any supported locale, and for discovering which of Luxon features are available in the current environment.\n */\nexport default class Info {\n  /**\n   * Return whether the specified zone contains a DST.\n   * @param {string|Zone} [zone='local'] - Zone to check. Defaults to the environment's local zone.\n   * @return {boolean}\n   */\n  static hasDST(zone = Settings.defaultZone) {\n    const proto = DateTime.now().setZone(zone).set({ month: 12 });\n\n    return !zone.isUniversal && proto.offset !== proto.set({ month: 6 }).offset;\n  }\n\n  /**\n   * Return whether the specified zone is a valid IANA specifier.\n   * @param {string} zone - Zone to check\n   * @return {boolean}\n   */\n  static isValidIANAZone(zone) {\n    return IANAZone.isValidZone(zone);\n  }\n\n  /**\n   * Converts the input into a {@link Zone} instance.\n   *\n   * * If `input` is already a Zone instance, it is returned unchanged.\n   * * If `input` is a string containing a valid time zone name, a Zone instance\n   *   with that name is returned.\n   * * If `input` is a string that doesn't refer to a known time zone, a Zone\n   *   instance with {@link Zone#isValid} == false is returned.\n   * * If `input is a number, a Zone instance with the specified fixed offset\n   *   in minutes is returned.\n   * * If `input` is `null` or `undefined`, the default zone is returned.\n   * @param {string|Zone|number} [input] - the value to be converted\n   * @return {Zone}\n   */\n  static normalizeZone(input) {\n    return normalizeZone(input, Settings.defaultZone);\n  }\n\n  /**\n   * Get the weekday on which the week starts according to the given locale.\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @returns {number} the start of the week, 1 for Monday through 7 for Sunday\n   */\n  static getStartOfWeek({ locale = null, locObj = null } = {}) {\n    return (locObj || Locale.create(locale)).getStartOfWeek();\n  }\n\n  /**\n   * Get the minimum number of days necessary in a week before it is considered part of the next year according\n   * to the given locale.\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @returns {number}\n   */\n  static getMinimumDaysInFirstWeek({ locale = null, locObj = null } = {}) {\n    return (locObj || Locale.create(locale)).getMinDaysInFirstWeek();\n  }\n\n  /**\n   * Get the weekdays, which are considered the weekend according to the given locale\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @returns {number[]} an array of weekdays, 1 for Monday through 7 for Sunday\n   */\n  static getWeekendWeekdays({ locale = null, locObj = null } = {}) {\n    // copy the array, because we cache it internally\n    return (locObj || Locale.create(locale)).getWeekendDays().slice();\n  }\n\n  /**\n   * Return an array of standalone month names.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {string} [length='long'] - the length of the month representation, such as \"numeric\", \"2-digit\", \"narrow\", \"short\", \"long\"\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @param {string} [opts.outputCalendar='gregory'] - the calendar\n   * @example Info.months()[0] //=> 'January'\n   * @example Info.months('short')[0] //=> 'Jan'\n   * @example Info.months('numeric')[0] //=> '1'\n   * @example Info.months('short', { locale: 'fr-CA' } )[0] //=> 'janv.'\n   * @example Info.months('numeric', { locale: 'ar' })[0] //=> '١'\n   * @example Info.months('long', { outputCalendar: 'islamic' })[0] //=> 'Rabiʻ I'\n   * @return {Array}\n   */\n  static months(\n    length = \"long\",\n    { locale = null, numberingSystem = null, locObj = null, outputCalendar = \"gregory\" } = {}\n  ) {\n    return (locObj || Locale.create(locale, numberingSystem, outputCalendar)).months(length);\n  }\n\n  /**\n   * Return an array of format month names.\n   * Format months differ from standalone months in that they're meant to appear next to the day of the month. In some languages, that\n   * changes the string.\n   * See {@link Info#months}\n   * @param {string} [length='long'] - the length of the month representation, such as \"numeric\", \"2-digit\", \"narrow\", \"short\", \"long\"\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @param {string} [opts.outputCalendar='gregory'] - the calendar\n   * @return {Array}\n   */\n  static monthsFormat(\n    length = \"long\",\n    { locale = null, numberingSystem = null, locObj = null, outputCalendar = \"gregory\" } = {}\n  ) {\n    return (locObj || Locale.create(locale, numberingSystem, outputCalendar)).months(length, true);\n  }\n\n  /**\n   * Return an array of standalone week names.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {string} [length='long'] - the length of the weekday representation, such as \"narrow\", \"short\", \"long\".\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @example Info.weekdays()[0] //=> 'Monday'\n   * @example Info.weekdays('short')[0] //=> 'Mon'\n   * @example Info.weekdays('short', { locale: 'fr-CA' })[0] //=> 'lun.'\n   * @example Info.weekdays('short', { locale: 'ar' })[0] //=> 'الاثنين'\n   * @return {Array}\n   */\n  static weekdays(length = \"long\", { locale = null, numberingSystem = null, locObj = null } = {}) {\n    return (locObj || Locale.create(locale, numberingSystem, null)).weekdays(length);\n  }\n\n  /**\n   * Return an array of format week names.\n   * Format weekdays differ from standalone weekdays in that they're meant to appear next to more date information. In some languages, that\n   * changes the string.\n   * See {@link Info#weekdays}\n   * @param {string} [length='long'] - the length of the month representation, such as \"narrow\", \"short\", \"long\".\n   * @param {Object} opts - options\n   * @param {string} [opts.locale=null] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @return {Array}\n   */\n  static weekdaysFormat(\n    length = \"long\",\n    { locale = null, numberingSystem = null, locObj = null } = {}\n  ) {\n    return (locObj || Locale.create(locale, numberingSystem, null)).weekdays(length, true);\n  }\n\n  /**\n   * Return an array of meridiems.\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @example Info.meridiems() //=> [ 'AM', 'PM' ]\n   * @example Info.meridiems({ locale: 'my' }) //=> [ 'နံနက်', 'ညနေ' ]\n   * @return {Array}\n   */\n  static meridiems({ locale = null } = {}) {\n    return Locale.create(locale).meridiems();\n  }\n\n  /**\n   * Return an array of eras, such as ['BC', 'AD']. The locale can be specified, but the calendar system is always Gregorian.\n   * @param {string} [length='short'] - the length of the era representation, such as \"short\" or \"long\".\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @example Info.eras() //=> [ 'BC', 'AD' ]\n   * @example Info.eras('long') //=> [ 'Before Christ', 'Anno Domini' ]\n   * @example Info.eras('long', { locale: 'fr' }) //=> [ 'avant Jésus-Christ', 'après Jésus-Christ' ]\n   * @return {Array}\n   */\n  static eras(length = \"short\", { locale = null } = {}) {\n    return Locale.create(locale, null, \"gregory\").eras(length);\n  }\n\n  /**\n   * Return the set of available features in this environment.\n   * Some features of Luxon are not available in all environments. For example, on older browsers, relative time formatting support is not available. Use this function to figure out if that's the case.\n   * Keys:\n   * * `relative`: whether this environment supports relative time formatting\n   * * `localeWeek`: whether this environment supports different weekdays for the start of the week based on the locale\n   * @example Info.features() //=> { relative: false, localeWeek: true }\n   * @return {Object}\n   */\n  static features() {\n    return { relative: hasRelative(), localeWeek: hasLocaleWeekInfo() };\n  }\n}\n", "import Duration from \"../duration.js\";\n\nfunction dayDiff(earlier, later) {\n  const utcDayStart = (dt) => dt.toUTC(0, { keepLocalTime: true }).startOf(\"day\").valueOf(),\n    ms = utcDayStart(later) - utcDayStart(earlier);\n  return Math.floor(Duration.fromMillis(ms).as(\"days\"));\n}\n\nfunction highOrderDiffs(cursor, later, units) {\n  const differs = [\n    [\"years\", (a, b) => b.year - a.year],\n    [\"quarters\", (a, b) => b.quarter - a.quarter + (b.year - a.year) * 4],\n    [\"months\", (a, b) => b.month - a.month + (b.year - a.year) * 12],\n    [\n      \"weeks\",\n      (a, b) => {\n        const days = dayDiff(a, b);\n        return (days - (days % 7)) / 7;\n      },\n    ],\n    [\"days\", dayDiff],\n  ];\n\n  const results = {};\n  const earlier = cursor;\n  let lowestOrder, highWater;\n\n  /* This loop tries to diff using larger units first.\n     If we overshoot, we backtrack and try the next smaller unit.\n     \"cursor\" starts out at the earlier timestamp and moves closer and closer to \"later\"\n     as we use smaller and smaller units.\n     highWater keeps track of where we would be if we added one more of the smallest unit,\n     this is used later to potentially convert any difference smaller than the smallest higher order unit\n     into a fraction of that smallest higher order unit\n  */\n  for (const [unit, differ] of differs) {\n    if (units.indexOf(unit) >= 0) {\n      lowestOrder = unit;\n\n      results[unit] = differ(cursor, later);\n      highWater = earlier.plus(results);\n\n      if (highWater > later) {\n        // we overshot the end point, backtrack cursor by 1\n        results[unit]--;\n        cursor = earlier.plus(results);\n\n        // if we are still overshooting now, we need to backtrack again\n        // this happens in certain situations when diffing times in different zones,\n        // because this calculation ignores time zones\n        if (cursor > later) {\n          // keep the \"overshot by 1\" around as highWater\n          highWater = cursor;\n          // backtrack cursor by 1\n          results[unit]--;\n          cursor = earlier.plus(results);\n        }\n      } else {\n        cursor = highWater;\n      }\n    }\n  }\n\n  return [cursor, results, highWater, lowestOrder];\n}\n\nexport default function (earlier, later, units, opts) {\n  let [cursor, results, highWater, lowestOrder] = highOrderDiffs(earlier, later, units);\n\n  const remainingMillis = later - cursor;\n\n  const lowerOrderUnits = units.filter(\n    (u) => [\"hours\", \"minutes\", \"seconds\", \"milliseconds\"].indexOf(u) >= 0\n  );\n\n  if (lowerOrderUnits.length === 0) {\n    if (highWater < later) {\n      highWater = cursor.plus({ [lowestOrder]: 1 });\n    }\n\n    if (highWater !== cursor) {\n      results[lowestOrder] = (results[lowestOrder] || 0) + remainingMillis / (highWater - cursor);\n    }\n  }\n\n  const duration = Duration.fromObject(results, opts);\n\n  if (lowerOrderUnits.length > 0) {\n    return Duration.fromMillis(remainingMillis, opts)\n      .shiftTo(...lowerOrderUnits)\n      .plus(duration);\n  } else {\n    return duration;\n  }\n}\n", "import { parseM<PERSON>is, isUndefined, untruncate<PERSON>ear, signedOffset, hasOwnProperty } from \"./util.js\";\nimport Formatter from \"./formatter.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\nimport DateTime from \"../datetime.js\";\nimport { digitRegex, parseDigits } from \"./digits.js\";\nimport { ConflictingSpecificationError } from \"../errors.js\";\n\nconst MISSING_FTP = \"missing Intl.DateTimeFormat.formatToParts support\";\n\nfunction intUnit(regex, post = (i) => i) {\n  return { regex, deser: ([s]) => post(parseDigits(s)) };\n}\n\nconst NBSP = String.fromCharCode(160);\nconst spaceOrNBSP = `[ ${NBSP}]`;\nconst spaceOrNBSPRegExp = new RegExp(spaceOrNBSP, \"g\");\n\nfunction fixListRegex(s) {\n  // make dots optional and also make them literal\n  // make space and non breakable space characters interchangeable\n  return s.replace(/\\./g, \"\\\\.?\").replace(spaceOrNBSPRegExp, spaceOrNBSP);\n}\n\nfunction stripInsensitivities(s) {\n  return s\n    .replace(/\\./g, \"\") // ignore dots that were made optional\n    .replace(spaceOrNBSPRegExp, \" \") // interchange space and nbsp\n    .toLowerCase();\n}\n\nfunction oneOf(strings, startIndex) {\n  if (strings === null) {\n    return null;\n  } else {\n    return {\n      regex: RegExp(strings.map(fixListRegex).join(\"|\")),\n      deser: ([s]) =>\n        strings.findIndex((i) => stripInsensitivities(s) === stripInsensitivities(i)) + startIndex,\n    };\n  }\n}\n\nfunction offset(regex, groups) {\n  return { regex, deser: ([, h, m]) => signedOffset(h, m), groups };\n}\n\nfunction simple(regex) {\n  return { regex, deser: ([s]) => s };\n}\n\nfunction escapeToken(value) {\n  return value.replace(/[\\-\\[\\]{}()*+?.,\\\\\\^$|#\\s]/g, \"\\\\$&\");\n}\n\n/**\n * @param token\n * @param {Locale} loc\n */\nfunction unitForToken(token, loc) {\n  const one = digitRegex(loc),\n    two = digitRegex(loc, \"{2}\"),\n    three = digitRegex(loc, \"{3}\"),\n    four = digitRegex(loc, \"{4}\"),\n    six = digitRegex(loc, \"{6}\"),\n    oneOrTwo = digitRegex(loc, \"{1,2}\"),\n    oneToThree = digitRegex(loc, \"{1,3}\"),\n    oneToSix = digitRegex(loc, \"{1,6}\"),\n    oneToNine = digitRegex(loc, \"{1,9}\"),\n    twoToFour = digitRegex(loc, \"{2,4}\"),\n    fourToSix = digitRegex(loc, \"{4,6}\"),\n    literal = (t) => ({ regex: RegExp(escapeToken(t.val)), deser: ([s]) => s, literal: true }),\n    unitate = (t) => {\n      if (token.literal) {\n        return literal(t);\n      }\n      switch (t.val) {\n        // era\n        case \"G\":\n          return oneOf(loc.eras(\"short\"), 0);\n        case \"GG\":\n          return oneOf(loc.eras(\"long\"), 0);\n        // years\n        case \"y\":\n          return intUnit(oneToSix);\n        case \"yy\":\n          return intUnit(twoToFour, untruncateYear);\n        case \"yyyy\":\n          return intUnit(four);\n        case \"yyyyy\":\n          return intUnit(fourToSix);\n        case \"yyyyyy\":\n          return intUnit(six);\n        // months\n        case \"M\":\n          return intUnit(oneOrTwo);\n        case \"MM\":\n          return intUnit(two);\n        case \"MMM\":\n          return oneOf(loc.months(\"short\", true), 1);\n        case \"MMMM\":\n          return oneOf(loc.months(\"long\", true), 1);\n        case \"L\":\n          return intUnit(oneOrTwo);\n        case \"LL\":\n          return intUnit(two);\n        case \"LLL\":\n          return oneOf(loc.months(\"short\", false), 1);\n        case \"LLLL\":\n          return oneOf(loc.months(\"long\", false), 1);\n        // dates\n        case \"d\":\n          return intUnit(oneOrTwo);\n        case \"dd\":\n          return intUnit(two);\n        // ordinals\n        case \"o\":\n          return intUnit(oneToThree);\n        case \"ooo\":\n          return intUnit(three);\n        // time\n        case \"HH\":\n          return intUnit(two);\n        case \"H\":\n          return intUnit(oneOrTwo);\n        case \"hh\":\n          return intUnit(two);\n        case \"h\":\n          return intUnit(oneOrTwo);\n        case \"mm\":\n          return intUnit(two);\n        case \"m\":\n          return intUnit(oneOrTwo);\n        case \"q\":\n          return intUnit(oneOrTwo);\n        case \"qq\":\n          return intUnit(two);\n        case \"s\":\n          return intUnit(oneOrTwo);\n        case \"ss\":\n          return intUnit(two);\n        case \"S\":\n          return intUnit(oneToThree);\n        case \"SSS\":\n          return intUnit(three);\n        case \"u\":\n          return simple(oneToNine);\n        case \"uu\":\n          return simple(oneOrTwo);\n        case \"uuu\":\n          return intUnit(one);\n        // meridiem\n        case \"a\":\n          return oneOf(loc.meridiems(), 0);\n        // weekYear (k)\n        case \"kkkk\":\n          return intUnit(four);\n        case \"kk\":\n          return intUnit(twoToFour, untruncateYear);\n        // weekNumber (W)\n        case \"W\":\n          return intUnit(oneOrTwo);\n        case \"WW\":\n          return intUnit(two);\n        // weekdays\n        case \"E\":\n        case \"c\":\n          return intUnit(one);\n        case \"EEE\":\n          return oneOf(loc.weekdays(\"short\", false), 1);\n        case \"EEEE\":\n          return oneOf(loc.weekdays(\"long\", false), 1);\n        case \"ccc\":\n          return oneOf(loc.weekdays(\"short\", true), 1);\n        case \"cccc\":\n          return oneOf(loc.weekdays(\"long\", true), 1);\n        // offset/zone\n        case \"Z\":\n        case \"ZZ\":\n          return offset(new RegExp(`([+-]${oneOrTwo.source})(?::(${two.source}))?`), 2);\n        case \"ZZZ\":\n          return offset(new RegExp(`([+-]${oneOrTwo.source})(${two.source})?`), 2);\n        // we don't support ZZZZ (PST) or ZZZZZ (Pacific Standard Time) in parsing\n        // because we don't have any way to figure out what they are\n        case \"z\":\n          return simple(/[a-z_+-/]{1,256}?/i);\n        // this special-case \"token\" represents a place where a macro-token expanded into a white-space literal\n        // in this case we accept any non-newline white-space\n        case \" \":\n          return simple(/[^\\S\\n\\r]/);\n        default:\n          return literal(t);\n      }\n    };\n\n  const unit = unitate(token) || {\n    invalidReason: MISSING_FTP,\n  };\n\n  unit.token = token;\n\n  return unit;\n}\n\nconst partTypeStyleToTokenVal = {\n  year: {\n    \"2-digit\": \"yy\",\n    numeric: \"yyyyy\",\n  },\n  month: {\n    numeric: \"M\",\n    \"2-digit\": \"MM\",\n    short: \"MMM\",\n    long: \"MMMM\",\n  },\n  day: {\n    numeric: \"d\",\n    \"2-digit\": \"dd\",\n  },\n  weekday: {\n    short: \"EEE\",\n    long: \"EEEE\",\n  },\n  dayperiod: \"a\",\n  dayPeriod: \"a\",\n  hour12: {\n    numeric: \"h\",\n    \"2-digit\": \"hh\",\n  },\n  hour24: {\n    numeric: \"H\",\n    \"2-digit\": \"HH\",\n  },\n  minute: {\n    numeric: \"m\",\n    \"2-digit\": \"mm\",\n  },\n  second: {\n    numeric: \"s\",\n    \"2-digit\": \"ss\",\n  },\n  timeZoneName: {\n    long: \"ZZZZZ\",\n    short: \"ZZZ\",\n  },\n};\n\nfunction tokenForPart(part, formatOpts, resolvedOpts) {\n  const { type, value } = part;\n\n  if (type === \"literal\") {\n    const isSpace = /^\\s+$/.test(value);\n    return {\n      literal: !isSpace,\n      val: isSpace ? \" \" : value,\n    };\n  }\n\n  const style = formatOpts[type];\n\n  // The user might have explicitly specified hour12 or hourCycle\n  // if so, respect their decision\n  // if not, refer back to the resolvedOpts, which are based on the locale\n  let actualType = type;\n  if (type === \"hour\") {\n    if (formatOpts.hour12 != null) {\n      actualType = formatOpts.hour12 ? \"hour12\" : \"hour24\";\n    } else if (formatOpts.hourCycle != null) {\n      if (formatOpts.hourCycle === \"h11\" || formatOpts.hourCycle === \"h12\") {\n        actualType = \"hour12\";\n      } else {\n        actualType = \"hour24\";\n      }\n    } else {\n      // tokens only differentiate between 24 hours or not,\n      // so we do not need to check hourCycle here, which is less supported anyways\n      actualType = resolvedOpts.hour12 ? \"hour12\" : \"hour24\";\n    }\n  }\n  let val = partTypeStyleToTokenVal[actualType];\n  if (typeof val === \"object\") {\n    val = val[style];\n  }\n\n  if (val) {\n    return {\n      literal: false,\n      val,\n    };\n  }\n\n  return undefined;\n}\n\nfunction buildRegex(units) {\n  const re = units.map((u) => u.regex).reduce((f, r) => `${f}(${r.source})`, \"\");\n  return [`^${re}$`, units];\n}\n\nfunction match(input, regex, handlers) {\n  const matches = input.match(regex);\n\n  if (matches) {\n    const all = {};\n    let matchIndex = 1;\n    for (const i in handlers) {\n      if (hasOwnProperty(handlers, i)) {\n        const h = handlers[i],\n          groups = h.groups ? h.groups + 1 : 1;\n        if (!h.literal && h.token) {\n          all[h.token.val[0]] = h.deser(matches.slice(matchIndex, matchIndex + groups));\n        }\n        matchIndex += groups;\n      }\n    }\n    return [matches, all];\n  } else {\n    return [matches, {}];\n  }\n}\n\nfunction dateTimeFromMatches(matches) {\n  const toField = (token) => {\n    switch (token) {\n      case \"S\":\n        return \"millisecond\";\n      case \"s\":\n        return \"second\";\n      case \"m\":\n        return \"minute\";\n      case \"h\":\n      case \"H\":\n        return \"hour\";\n      case \"d\":\n        return \"day\";\n      case \"o\":\n        return \"ordinal\";\n      case \"L\":\n      case \"M\":\n        return \"month\";\n      case \"y\":\n        return \"year\";\n      case \"E\":\n      case \"c\":\n        return \"weekday\";\n      case \"W\":\n        return \"weekNumber\";\n      case \"k\":\n        return \"weekYear\";\n      case \"q\":\n        return \"quarter\";\n      default:\n        return null;\n    }\n  };\n\n  let zone = null;\n  let specificOffset;\n  if (!isUndefined(matches.z)) {\n    zone = IANAZone.create(matches.z);\n  }\n\n  if (!isUndefined(matches.Z)) {\n    if (!zone) {\n      zone = new FixedOffsetZone(matches.Z);\n    }\n    specificOffset = matches.Z;\n  }\n\n  if (!isUndefined(matches.q)) {\n    matches.M = (matches.q - 1) * 3 + 1;\n  }\n\n  if (!isUndefined(matches.h)) {\n    if (matches.h < 12 && matches.a === 1) {\n      matches.h += 12;\n    } else if (matches.h === 12 && matches.a === 0) {\n      matches.h = 0;\n    }\n  }\n\n  if (matches.G === 0 && matches.y) {\n    matches.y = -matches.y;\n  }\n\n  if (!isUndefined(matches.u)) {\n    matches.S = parseMillis(matches.u);\n  }\n\n  const vals = Object.keys(matches).reduce((r, k) => {\n    const f = toField(k);\n    if (f) {\n      r[f] = matches[k];\n    }\n\n    return r;\n  }, {});\n\n  return [vals, zone, specificOffset];\n}\n\nlet dummyDateTimeCache = null;\n\nfunction getDummyDateTime() {\n  if (!dummyDateTimeCache) {\n    dummyDateTimeCache = DateTime.fromMillis(1555555555555);\n  }\n\n  return dummyDateTimeCache;\n}\n\nfunction maybeExpandMacroToken(token, locale) {\n  if (token.literal) {\n    return token;\n  }\n\n  const formatOpts = Formatter.macroTokenToFormatOpts(token.val);\n  const tokens = formatOptsToTokens(formatOpts, locale);\n\n  if (tokens == null || tokens.includes(undefined)) {\n    return token;\n  }\n\n  return tokens;\n}\n\nexport function expandMacroTokens(tokens, locale) {\n  return Array.prototype.concat(...tokens.map((t) => maybeExpandMacroToken(t, locale)));\n}\n\n/**\n * @private\n */\n\nexport class TokenParser {\n  constructor(locale, format) {\n    this.locale = locale;\n    this.format = format;\n    this.tokens = expandMacroTokens(Formatter.parseFormat(format), locale);\n    this.units = this.tokens.map((t) => unitForToken(t, locale));\n    this.disqualifyingUnit = this.units.find((t) => t.invalidReason);\n\n    if (!this.disqualifyingUnit) {\n      const [regexString, handlers] = buildRegex(this.units);\n      this.regex = RegExp(regexString, \"i\");\n      this.handlers = handlers;\n    }\n  }\n\n  explainFromTokens(input) {\n    if (!this.isValid) {\n      return { input, tokens: this.tokens, invalidReason: this.invalidReason };\n    } else {\n      const [rawMatches, matches] = match(input, this.regex, this.handlers),\n        [result, zone, specificOffset] = matches\n          ? dateTimeFromMatches(matches)\n          : [null, null, undefined];\n      if (hasOwnProperty(matches, \"a\") && hasOwnProperty(matches, \"H\")) {\n        throw new ConflictingSpecificationError(\n          \"Can't include meridiem when specifying 24-hour format\"\n        );\n      }\n      return {\n        input,\n        tokens: this.tokens,\n        regex: this.regex,\n        rawMatches,\n        matches,\n        result,\n        zone,\n        specificOffset,\n      };\n    }\n  }\n\n  get isValid() {\n    return !this.disqualifyingUnit;\n  }\n\n  get invalidReason() {\n    return this.disqualifyingUnit ? this.disqualifyingUnit.invalidReason : null;\n  }\n}\n\nexport function explainFromTokens(locale, input, format) {\n  const parser = new TokenParser(locale, format);\n  return parser.explainFromTokens(input);\n}\n\nexport function parseFromTokens(locale, input, format) {\n  const { result, zone, specificOffset, invalidReason } = explainFromTokens(locale, input, format);\n  return [result, zone, specificOffset, invalidReason];\n}\n\nexport function formatOptsToTokens(formatOpts, locale) {\n  if (!formatOpts) {\n    return null;\n  }\n\n  const formatter = Formatter.create(locale, formatOpts);\n  const df = formatter.dtFormatter(getDummyDateTime());\n  const parts = df.formatToParts();\n  const resolvedOpts = df.resolvedOptions();\n  return parts.map((p) => tokenForPart(p, formatOpts, resolvedOpts));\n}\n", "import Duration from \"./duration.js\";\nimport Interval from \"./interval.js\";\nimport Settings from \"./settings.js\";\nimport Info from \"./info.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport FixedOffsetZone from \"./zones/fixedOffsetZone.js\";\nimport Locale from \"./impl/locale.js\";\nimport {\n  isUndefined,\n  maybeArray,\n  isDate,\n  isNumber,\n  bestBy,\n  daysInMonth,\n  daysInYear,\n  isLeapYear,\n  weeksInWeekYear,\n  normalizeObject,\n  roundTo,\n  objToLocalTS,\n  padStart,\n} from \"./impl/util.js\";\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\nimport diff from \"./impl/diff.js\";\nimport { parseRFC2822Date, parseISODate, parseHTTPDate, parseSQL } from \"./impl/regexParser.js\";\nimport {\n  parseFromTokens,\n  explainFromTokens,\n  formatOptsToTokens,\n  expandMacroTokens,\n  TokenParser,\n} from \"./impl/tokenParser.js\";\nimport {\n  gregorianToWeek,\n  weekToGregorian,\n  gregorianToOrdinal,\n  ordinalToGregorian,\n  hasInvalidGregorianData,\n  hasInvalidWeekData,\n  hasInvalidOrdinalData,\n  hasInvalidTimeData,\n  usesLocalWeekValues,\n  isoWeekdayToLocal,\n} from \"./impl/conversions.js\";\nimport * as Formats from \"./impl/formats.js\";\nimport {\n  InvalidArgumentError,\n  ConflictingSpecificationError,\n  InvalidUnitError,\n  InvalidDateTimeError,\n} from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\n\nconst INVALID = \"Invalid DateTime\";\nconst MAX_DATE = 8.64e15;\n\nfunction unsupportedZone(zone) {\n  return new Invalid(\"unsupported zone\", `the zone \"${zone.name}\" is not supported`);\n}\n\n// we cache week data on the DT object and this intermediates the cache\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedWeekData(dt) {\n  if (dt.weekData === null) {\n    dt.weekData = gregorianToWeek(dt.c);\n  }\n  return dt.weekData;\n}\n\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedLocalWeekData(dt) {\n  if (dt.localWeekData === null) {\n    dt.localWeekData = gregorianToWeek(\n      dt.c,\n      dt.loc.getMinDaysInFirstWeek(),\n      dt.loc.getStartOfWeek()\n    );\n  }\n  return dt.localWeekData;\n}\n\n// clone really means, \"make a new object with these modifications\". all \"setters\" really use this\n// to create a new object while only changing some of the properties\nfunction clone(inst, alts) {\n  const current = {\n    ts: inst.ts,\n    zone: inst.zone,\n    c: inst.c,\n    o: inst.o,\n    loc: inst.loc,\n    invalid: inst.invalid,\n  };\n  return new DateTime({ ...current, ...alts, old: current });\n}\n\n// find the right offset a given local time. The o input is our guess, which determines which\n// offset we'll pick in ambiguous cases (e.g. there are two 3 AMs b/c Fallback DST)\nfunction fixOffset(localTS, o, tz) {\n  // Our UTC time is just a guess because our offset is just a guess\n  let utcGuess = localTS - o * 60 * 1000;\n\n  // Test whether the zone matches the offset for this ts\n  const o2 = tz.offset(utcGuess);\n\n  // If so, offset didn't change and we're done\n  if (o === o2) {\n    return [utcGuess, o];\n  }\n\n  // If not, change the ts by the difference in the offset\n  utcGuess -= (o2 - o) * 60 * 1000;\n\n  // If that gives us the local time we want, we're done\n  const o3 = tz.offset(utcGuess);\n  if (o2 === o3) {\n    return [utcGuess, o2];\n  }\n\n  // If it's different, we're in a hole time. The offset has changed, but the we don't adjust the time\n  return [localTS - Math.min(o2, o3) * 60 * 1000, Math.max(o2, o3)];\n}\n\n// convert an epoch timestamp into a calendar object with the given offset\nfunction tsToObj(ts, offset) {\n  ts += offset * 60 * 1000;\n\n  const d = new Date(ts);\n\n  return {\n    year: d.getUTCFullYear(),\n    month: d.getUTCMonth() + 1,\n    day: d.getUTCDate(),\n    hour: d.getUTCHours(),\n    minute: d.getUTCMinutes(),\n    second: d.getUTCSeconds(),\n    millisecond: d.getUTCMilliseconds(),\n  };\n}\n\n// convert a calendar object to a epoch timestamp\nfunction objToTS(obj, offset, zone) {\n  return fixOffset(objToLocalTS(obj), offset, zone);\n}\n\n// create a new DT instance by adding a duration, adjusting for DSTs\nfunction adjustTime(inst, dur) {\n  const oPre = inst.o,\n    year = inst.c.year + Math.trunc(dur.years),\n    month = inst.c.month + Math.trunc(dur.months) + Math.trunc(dur.quarters) * 3,\n    c = {\n      ...inst.c,\n      year,\n      month,\n      day:\n        Math.min(inst.c.day, daysInMonth(year, month)) +\n        Math.trunc(dur.days) +\n        Math.trunc(dur.weeks) * 7,\n    },\n    millisToAdd = Duration.fromObject({\n      years: dur.years - Math.trunc(dur.years),\n      quarters: dur.quarters - Math.trunc(dur.quarters),\n      months: dur.months - Math.trunc(dur.months),\n      weeks: dur.weeks - Math.trunc(dur.weeks),\n      days: dur.days - Math.trunc(dur.days),\n      hours: dur.hours,\n      minutes: dur.minutes,\n      seconds: dur.seconds,\n      milliseconds: dur.milliseconds,\n    }).as(\"milliseconds\"),\n    localTS = objToLocalTS(c);\n\n  let [ts, o] = fixOffset(localTS, oPre, inst.zone);\n\n  if (millisToAdd !== 0) {\n    ts += millisToAdd;\n    // that could have changed the offset by going over a DST, but we want to keep the ts the same\n    o = inst.zone.offset(ts);\n  }\n\n  return { ts, o };\n}\n\n// helper useful in turning the results of parsing into real dates\n// by handling the zone options\nfunction parseDataToDateTime(parsed, parsedZone, opts, format, text, specificOffset) {\n  const { setZone, zone } = opts;\n  if ((parsed && Object.keys(parsed).length !== 0) || parsedZone) {\n    const interpretationZone = parsedZone || zone,\n      inst = DateTime.fromObject(parsed, {\n        ...opts,\n        zone: interpretationZone,\n        specificOffset,\n      });\n    return setZone ? inst : inst.setZone(zone);\n  } else {\n    return DateTime.invalid(\n      new Invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ${format}`)\n    );\n  }\n}\n\n// if you want to output a technical format (e.g. RFC 2822), this helper\n// helps handle the details\nfunction toTechFormat(dt, format, allowZ = true) {\n  return dt.isValid\n    ? Formatter.create(Locale.create(\"en-US\"), {\n        allowZ,\n        forceSimple: true,\n      }).formatDateTimeFromString(dt, format)\n    : null;\n}\n\nfunction toISODate(o, extended) {\n  const longFormat = o.c.year > 9999 || o.c.year < 0;\n  let c = \"\";\n  if (longFormat && o.c.year >= 0) c += \"+\";\n  c += padStart(o.c.year, longFormat ? 6 : 4);\n\n  if (extended) {\n    c += \"-\";\n    c += padStart(o.c.month);\n    c += \"-\";\n    c += padStart(o.c.day);\n  } else {\n    c += padStart(o.c.month);\n    c += padStart(o.c.day);\n  }\n  return c;\n}\n\nfunction toISOTime(\n  o,\n  extended,\n  suppressSeconds,\n  suppressMilliseconds,\n  includeOffset,\n  extendedZone\n) {\n  let c = padStart(o.c.hour);\n  if (extended) {\n    c += \":\";\n    c += padStart(o.c.minute);\n    if (o.c.millisecond !== 0 || o.c.second !== 0 || !suppressSeconds) {\n      c += \":\";\n    }\n  } else {\n    c += padStart(o.c.minute);\n  }\n\n  if (o.c.millisecond !== 0 || o.c.second !== 0 || !suppressSeconds) {\n    c += padStart(o.c.second);\n\n    if (o.c.millisecond !== 0 || !suppressMilliseconds) {\n      c += \".\";\n      c += padStart(o.c.millisecond, 3);\n    }\n  }\n\n  if (includeOffset) {\n    if (o.isOffsetFixed && o.offset === 0 && !extendedZone) {\n      c += \"Z\";\n    } else if (o.o < 0) {\n      c += \"-\";\n      c += padStart(Math.trunc(-o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(-o.o % 60));\n    } else {\n      c += \"+\";\n      c += padStart(Math.trunc(o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(o.o % 60));\n    }\n  }\n\n  if (extendedZone) {\n    c += \"[\" + o.zone.ianaName + \"]\";\n  }\n  return c;\n}\n\n// defaults for unspecified units in the supported calendars\nconst defaultUnitValues = {\n    month: 1,\n    day: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  },\n  defaultWeekUnitValues = {\n    weekNumber: 1,\n    weekday: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  },\n  defaultOrdinalUnitValues = {\n    ordinal: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  };\n\n// Units in the supported calendars, sorted by bigness\nconst orderedUnits = [\"year\", \"month\", \"day\", \"hour\", \"minute\", \"second\", \"millisecond\"],\n  orderedWeekUnits = [\n    \"weekYear\",\n    \"weekNumber\",\n    \"weekday\",\n    \"hour\",\n    \"minute\",\n    \"second\",\n    \"millisecond\",\n  ],\n  orderedOrdinalUnits = [\"year\", \"ordinal\", \"hour\", \"minute\", \"second\", \"millisecond\"];\n\n// standardize case and plurality in units\nfunction normalizeUnit(unit) {\n  const normalized = {\n    year: \"year\",\n    years: \"year\",\n    month: \"month\",\n    months: \"month\",\n    day: \"day\",\n    days: \"day\",\n    hour: \"hour\",\n    hours: \"hour\",\n    minute: \"minute\",\n    minutes: \"minute\",\n    quarter: \"quarter\",\n    quarters: \"quarter\",\n    second: \"second\",\n    seconds: \"second\",\n    millisecond: \"millisecond\",\n    milliseconds: \"millisecond\",\n    weekday: \"weekday\",\n    weekdays: \"weekday\",\n    weeknumber: \"weekNumber\",\n    weeksnumber: \"weekNumber\",\n    weeknumbers: \"weekNumber\",\n    weekyear: \"weekYear\",\n    weekyears: \"weekYear\",\n    ordinal: \"ordinal\",\n  }[unit.toLowerCase()];\n\n  if (!normalized) throw new InvalidUnitError(unit);\n\n  return normalized;\n}\n\nfunction normalizeUnitWithLocalWeeks(unit) {\n  switch (unit.toLowerCase()) {\n    case \"localweekday\":\n    case \"localweekdays\":\n      return \"localWeekday\";\n    case \"localweeknumber\":\n    case \"localweeknumbers\":\n      return \"localWeekNumber\";\n    case \"localweekyear\":\n    case \"localweekyears\":\n      return \"localWeekYear\";\n    default:\n      return normalizeUnit(unit);\n  }\n}\n\n// cache offsets for zones based on the current timestamp when this function is\n// first called. When we are handling a datetime from components like (year,\n// month, day, hour) in a time zone, we need a guess about what the timezone\n// offset is so that we can convert into a UTC timestamp. One way is to find the\n// offset of now in the zone. The actual date may have a different offset (for\n// example, if we handle a date in June while we're in December in a zone that\n// observes DST), but we can check and adjust that.\n//\n// When handling many dates, calculating the offset for now every time is\n// expensive. It's just a guess, so we can cache the offset to use even if we\n// are right on a time change boundary (we'll just correct in the other\n// direction). Using a timestamp from first read is a slight optimization for\n// handling dates close to the current date, since those dates will usually be\n// in the same offset (we could set the timestamp statically, instead). We use a\n// single timestamp for all zones to make things a bit more predictable.\n//\n// This is safe for quickDT (used by local() and utc()) because we don't fill in\n// higher-order units from tsNow (as we do in fromObject, this requires that\n// offset is calculated from tsNow).\nfunction guessOffsetForZone(zone) {\n  if (!zoneOffsetGuessCache[zone]) {\n    if (zoneOffsetTs === undefined) {\n      zoneOffsetTs = Settings.now();\n    }\n\n    zoneOffsetGuessCache[zone] = zone.offset(zoneOffsetTs);\n  }\n  return zoneOffsetGuessCache[zone];\n}\n\n// this is a dumbed down version of fromObject() that runs about 60% faster\n// but doesn't do any validation, makes a bunch of assumptions about what units\n// are present, and so on.\nfunction quickDT(obj, opts) {\n  const zone = normalizeZone(opts.zone, Settings.defaultZone);\n  if (!zone.isValid) {\n    return DateTime.invalid(unsupportedZone(zone));\n  }\n\n  const loc = Locale.fromObject(opts);\n\n  let ts, o;\n\n  // assume we have the higher-order units\n  if (!isUndefined(obj.year)) {\n    for (const u of orderedUnits) {\n      if (isUndefined(obj[u])) {\n        obj[u] = defaultUnitValues[u];\n      }\n    }\n\n    const invalid = hasInvalidGregorianData(obj) || hasInvalidTimeData(obj);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    const offsetProvis = guessOffsetForZone(zone);\n    [ts, o] = objToTS(obj, offsetProvis, zone);\n  } else {\n    ts = Settings.now();\n  }\n\n  return new DateTime({ ts, zone, loc, o });\n}\n\nfunction diffRelative(start, end, opts) {\n  const round = isUndefined(opts.round) ? true : opts.round,\n    format = (c, unit) => {\n      c = roundTo(c, round || opts.calendary ? 0 : 2, true);\n      const formatter = end.loc.clone(opts).relFormatter(opts);\n      return formatter.format(c, unit);\n    },\n    differ = (unit) => {\n      if (opts.calendary) {\n        if (!end.hasSame(start, unit)) {\n          return end.startOf(unit).diff(start.startOf(unit), unit).get(unit);\n        } else return 0;\n      } else {\n        return end.diff(start, unit).get(unit);\n      }\n    };\n\n  if (opts.unit) {\n    return format(differ(opts.unit), opts.unit);\n  }\n\n  for (const unit of opts.units) {\n    const count = differ(unit);\n    if (Math.abs(count) >= 1) {\n      return format(count, unit);\n    }\n  }\n  return format(start > end ? -0 : 0, opts.units[opts.units.length - 1]);\n}\n\nfunction lastOpts(argList) {\n  let opts = {},\n    args;\n  if (argList.length > 0 && typeof argList[argList.length - 1] === \"object\") {\n    opts = argList[argList.length - 1];\n    args = Array.from(argList).slice(0, argList.length - 1);\n  } else {\n    args = Array.from(argList);\n  }\n  return [opts, args];\n}\n\n/**\n * Timestamp to use for cached zone offset guesses (exposed for test)\n */\nlet zoneOffsetTs;\n/**\n * Cache for zone offset guesses (exposed for test).\n *\n * This optimizes quickDT via guessOffsetForZone to avoid repeated calls of\n * zone.offset().\n */\nlet zoneOffsetGuessCache = {};\n\n/**\n * A DateTime is an immutable data structure representing a specific date and time and accompanying methods. It contains class and instance methods for creating, parsing, interrogating, transforming, and formatting them.\n *\n * A DateTime comprises of:\n * * A timestamp. Each DateTime instance refers to a specific millisecond of the Unix epoch.\n * * A time zone. Each instance is considered in the context of a specific zone (by default the local system's zone).\n * * Configuration properties that effect how output strings are formatted, such as `locale`, `numberingSystem`, and `outputCalendar`.\n *\n * Here is a brief overview of the most commonly used functionality it provides:\n *\n * * **Creation**: To create a DateTime from its components, use one of its factory class methods: {@link DateTime.local}, {@link DateTime.utc}, and (most flexibly) {@link DateTime.fromObject}. To create one from a standard string format, use {@link DateTime.fromISO}, {@link DateTime.fromHTTP}, and {@link DateTime.fromRFC2822}. To create one from a custom string format, use {@link DateTime.fromFormat}. To create one from a native JS date, use {@link DateTime.fromJSDate}.\n * * **Gregorian calendar and time**: To examine the Gregorian properties of a DateTime individually (i.e as opposed to collectively through {@link DateTime#toObject}), use the {@link DateTime#year}, {@link DateTime#month},\n * {@link DateTime#day}, {@link DateTime#hour}, {@link DateTime#minute}, {@link DateTime#second}, {@link DateTime#millisecond} accessors.\n * * **Week calendar**: For ISO week calendar attributes, see the {@link DateTime#weekYear}, {@link DateTime#weekNumber}, and {@link DateTime#weekday} accessors.\n * * **Configuration** See the {@link DateTime#locale} and {@link DateTime#numberingSystem} accessors.\n * * **Transformation**: To transform the DateTime into other DateTimes, use {@link DateTime#set}, {@link DateTime#reconfigure}, {@link DateTime#setZone}, {@link DateTime#setLocale}, {@link DateTime.plus}, {@link DateTime#minus}, {@link DateTime#endOf}, {@link DateTime#startOf}, {@link DateTime#toUTC}, and {@link DateTime#toLocal}.\n * * **Output**: To convert the DateTime to other representations, use the {@link DateTime#toRelative}, {@link DateTime#toRelativeCalendar}, {@link DateTime#toJSON}, {@link DateTime#toISO}, {@link DateTime#toHTTP}, {@link DateTime#toObject}, {@link DateTime#toRFC2822}, {@link DateTime#toString}, {@link DateTime#toLocaleString}, {@link DateTime#toFormat}, {@link DateTime#toMillis} and {@link DateTime#toJSDate}.\n *\n * There's plenty others documented below. In addition, for more information on subtler topics like internationalization, time zones, alternative calendars, validity, and so on, see the external documentation.\n */\nexport default class DateTime {\n  /**\n   * @access private\n   */\n  constructor(config) {\n    const zone = config.zone || Settings.defaultZone;\n\n    let invalid =\n      config.invalid ||\n      (Number.isNaN(config.ts) ? new Invalid(\"invalid input\") : null) ||\n      (!zone.isValid ? unsupportedZone(zone) : null);\n    /**\n     * @access private\n     */\n    this.ts = isUndefined(config.ts) ? Settings.now() : config.ts;\n\n    let c = null,\n      o = null;\n    if (!invalid) {\n      const unchanged = config.old && config.old.ts === this.ts && config.old.zone.equals(zone);\n\n      if (unchanged) {\n        [c, o] = [config.old.c, config.old.o];\n      } else {\n        // If an offset has been passed and we have not been called from\n        // clone(), we can trust it and avoid the offset calculation.\n        const ot = isNumber(config.o) && !config.old ? config.o : zone.offset(this.ts);\n        c = tsToObj(this.ts, ot);\n        invalid = Number.isNaN(c.year) ? new Invalid(\"invalid input\") : null;\n        c = invalid ? null : c;\n        o = invalid ? null : ot;\n      }\n    }\n\n    /**\n     * @access private\n     */\n    this._zone = zone;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.invalid = invalid;\n    /**\n     * @access private\n     */\n    this.weekData = null;\n    /**\n     * @access private\n     */\n    this.localWeekData = null;\n    /**\n     * @access private\n     */\n    this.c = c;\n    /**\n     * @access private\n     */\n    this.o = o;\n    /**\n     * @access private\n     */\n    this.isLuxonDateTime = true;\n  }\n\n  // CONSTRUCT\n\n  /**\n   * Create a DateTime for the current instant, in the system's time zone.\n   *\n   * Use Settings to override these default values if needed.\n   * @example DateTime.now().toISO() //~> now in the ISO format\n   * @return {DateTime}\n   */\n  static now() {\n    return new DateTime({});\n  }\n\n  /**\n   * Create a local DateTime\n   * @param {number} [year] - The calendar year. If omitted (as in, call `local()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month, 1-indexed\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @example DateTime.local()                                  //~> now\n   * @example DateTime.local({ zone: \"America/New_York\" })      //~> now, in US east coast time\n   * @example DateTime.local(2017)                              //~> 2017-01-01T00:00:00\n   * @example DateTime.local(2017, 3)                           //~> 2017-03-01T00:00:00\n   * @example DateTime.local(2017, 3, 12, { locale: \"fr\" })     //~> 2017-03-12T00:00:00, with a French locale\n   * @example DateTime.local(2017, 3, 12, 5)                    //~> 2017-03-12T05:00:00\n   * @example DateTime.local(2017, 3, 12, 5, { zone: \"utc\" })   //~> 2017-03-12T05:00:00, in UTC\n   * @example DateTime.local(2017, 3, 12, 5, 45)                //~> 2017-03-12T05:45:00\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10)            //~> 2017-03-12T05:45:10\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10, 765)       //~> 2017-03-12T05:45:10.765\n   * @return {DateTime}\n   */\n  static local() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);\n  }\n\n  /**\n   * Create a DateTime in UTC\n   * @param {number} [year] - The calendar year. If omitted (as in, call `utc()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} [options.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [options.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [options.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.utc()                                              //~> now\n   * @example DateTime.utc(2017)                                          //~> 2017-01-01T00:00:00Z\n   * @example DateTime.utc(2017, 3)                                       //~> 2017-03-01T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12)                                   //~> 2017-03-12T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5)                                //~> 2017-03-12T05:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45)                            //~> 2017-03-12T05:45:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, { locale: \"fr\" })          //~> 2017-03-12T05:45:00Z with a French locale\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10)                        //~> 2017-03-12T05:45:10Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10, 765, { locale: \"fr\" }) //~> 2017-03-12T05:45:10.765Z with a French locale\n   * @return {DateTime}\n   */\n  static utc() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n\n    opts.zone = FixedOffsetZone.utcInstance;\n    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);\n  }\n\n  /**\n   * Create a DateTime from a JavaScript Date object. Uses the default zone.\n   * @param {Date} date - a JavaScript Date object\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @return {DateTime}\n   */\n  static fromJSDate(date, options = {}) {\n    const ts = isDate(date) ? date.valueOf() : NaN;\n    if (Number.isNaN(ts)) {\n      return DateTime.invalid(\"invalid input\");\n    }\n\n    const zoneToUse = normalizeZone(options.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n\n    return new DateTime({\n      ts: ts,\n      zone: zoneToUse,\n      loc: Locale.fromObject(options),\n    });\n  }\n\n  /**\n   * Create a DateTime from a number of milliseconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} milliseconds - a number of milliseconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromMillis(milliseconds, options = {}) {\n    if (!isNumber(milliseconds)) {\n      throw new InvalidArgumentError(\n        `fromMillis requires a numerical input, but received a ${typeof milliseconds} with value ${milliseconds}`\n      );\n    } else if (milliseconds < -MAX_DATE || milliseconds > MAX_DATE) {\n      // this isn't perfect because we can still end up out of range because of additional shifting, but it's a start\n      return DateTime.invalid(\"Timestamp out of range\");\n    } else {\n      return new DateTime({\n        ts: milliseconds,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options),\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a number of seconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} seconds - a number of seconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromSeconds(seconds, options = {}) {\n    if (!isNumber(seconds)) {\n      throw new InvalidArgumentError(\"fromSeconds requires a numerical input\");\n    } else {\n      return new DateTime({\n        ts: seconds * 1000,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options),\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a JavaScript object with keys like 'year' and 'hour' with reasonable defaults.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.year - a year, such as 1987\n   * @param {number} obj.month - a month, 1-12\n   * @param {number} obj.day - a day of the month, 1-31, depending on the month\n   * @param {number} obj.ordinal - day of the year, 1-365 or 366\n   * @param {number} obj.weekYear - an ISO week year\n   * @param {number} obj.weekNumber - an ISO week number, between 1 and 52 or 53, depending on the year\n   * @param {number} obj.weekday - an ISO weekday, 1-7, where 1 is Monday and 7 is Sunday\n   * @param {number} obj.localWeekYear - a week year, according to the locale\n   * @param {number} obj.localWeekNumber - a week number, between 1 and 52 or 53, depending on the year, according to the locale\n   * @param {number} obj.localWeekday - a weekday, 1-7, where 1 is the first and 7 is the last day of the week, according to the locale\n   * @param {number} obj.hour - hour of the day, 0-23\n   * @param {number} obj.minute - minute of the hour, 0-59\n   * @param {number} obj.second - second of the minute, 0-59\n   * @param {number} obj.millisecond - millisecond of the second, 0-999\n   * @param {Object} opts - options for creating this DateTime\n   * @param {string|Zone} [opts.zone='local'] - interpret the numbers in the context of a particular zone. Can take any value taken as the first argument to setZone()\n   * @param {string} [opts.locale='system\\'s locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromObject({ year: 1982, month: 5, day: 25}).toISODate() //=> '1982-05-25'\n   * @example DateTime.fromObject({ year: 1982 }).toISODate() //=> '1982-01-01'\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }) //~> today at 10:26:06\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'utc' }),\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'local' })\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'America/New_York' })\n   * @example DateTime.fromObject({ weekYear: 2016, weekNumber: 2, weekday: 3 }).toISODate() //=> '2016-01-13'\n   * @example DateTime.fromObject({ localWeekYear: 2022, localWeekNumber: 1, localWeekday: 1 }, { locale: \"en-US\" }).toISODate() //=> '2021-12-26'\n   * @return {DateTime}\n   */\n  static fromObject(obj, opts = {}) {\n    obj = obj || {};\n    const zoneToUse = normalizeZone(opts.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n\n    const loc = Locale.fromObject(opts);\n    const normalized = normalizeObject(obj, normalizeUnitWithLocalWeeks);\n    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, loc);\n\n    const tsNow = Settings.now(),\n      offsetProvis = !isUndefined(opts.specificOffset)\n        ? opts.specificOffset\n        : zoneToUse.offset(tsNow),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    // cases:\n    // just a weekday -> this week's instance of that weekday, no worries\n    // (gregorian data or ordinal) + (weekYear or weekNumber) -> error\n    // (gregorian month or day) + ordinal -> error\n    // otherwise just use weeks or ordinals or gregorian, depending on what's specified\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\n        \"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"\n      );\n    }\n\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n\n    const useWeekData = definiteWeekDef || (normalized.weekday && !containsGregor);\n\n    // configure ourselves to deal with gregorian dates or week stuff\n    let units,\n      defaultValues,\n      objNow = tsToObj(tsNow, offsetProvis);\n    if (useWeekData) {\n      units = orderedWeekUnits;\n      defaultValues = defaultWeekUnitValues;\n      objNow = gregorianToWeek(objNow, minDaysInFirstWeek, startOfWeek);\n    } else if (containsOrdinal) {\n      units = orderedOrdinalUnits;\n      defaultValues = defaultOrdinalUnitValues;\n      objNow = gregorianToOrdinal(objNow);\n    } else {\n      units = orderedUnits;\n      defaultValues = defaultUnitValues;\n    }\n\n    // set default values for missing stuff\n    let foundFirst = false;\n    for (const u of units) {\n      const v = normalized[u];\n      if (!isUndefined(v)) {\n        foundFirst = true;\n      } else if (foundFirst) {\n        normalized[u] = defaultValues[u];\n      } else {\n        normalized[u] = objNow[u];\n      }\n    }\n\n    // make sure the values we have are in range\n    const higherOrderInvalid = useWeekData\n        ? hasInvalidWeekData(normalized, minDaysInFirstWeek, startOfWeek)\n        : containsOrdinal\n        ? hasInvalidOrdinalData(normalized)\n        : hasInvalidGregorianData(normalized),\n      invalid = higherOrderInvalid || hasInvalidTimeData(normalized);\n\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    // compute the actual time\n    const gregorian = useWeekData\n        ? weekToGregorian(normalized, minDaysInFirstWeek, startOfWeek)\n        : containsOrdinal\n        ? ordinalToGregorian(normalized)\n        : normalized,\n      [tsFinal, offsetFinal] = objToTS(gregorian, offsetProvis, zoneToUse),\n      inst = new DateTime({\n        ts: tsFinal,\n        zone: zoneToUse,\n        o: offsetFinal,\n        loc,\n      });\n\n    // gregorian data + weekday serves only to validate\n    if (normalized.weekday && containsGregor && obj.weekday !== inst.weekday) {\n      return DateTime.invalid(\n        \"mismatched weekday\",\n        `you can't specify both a weekday of ${normalized.weekday} and a date of ${inst.toISO()}`\n      );\n    }\n\n    if (!inst.isValid) {\n      return DateTime.invalid(inst.invalid);\n    }\n\n    return inst;\n  }\n\n  /**\n   * Create a DateTime from an ISO 8601 string\n   * @param {string} text - the ISO string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the time to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} [opts.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [opts.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [opts.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00', {setZone: true})\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123', {zone: 'utc'})\n   * @example DateTime.fromISO('2016-W05-4')\n   * @return {DateTime}\n   */\n  static fromISO(text, opts = {}) {\n    const [vals, parsedZone] = parseISODate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"ISO 8601\", text);\n  }\n\n  /**\n   * Create a DateTime from an RFC 2822 string\n   * @param {string} text - the RFC 2822 string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since the offset is always specified in the string itself, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23:12 GMT')\n   * @example DateTime.fromRFC2822('Fri, 25 Nov 2016 13:23:12 +0600')\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23 Z')\n   * @return {DateTime}\n   */\n  static fromRFC2822(text, opts = {}) {\n    const [vals, parsedZone] = parseRFC2822Date(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"RFC 2822\", text);\n  }\n\n  /**\n   * Create a DateTime from an HTTP header date\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @param {string} text - the HTTP header date\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since HTTP dates are always in UTC, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with the fixed-offset zone specified in the string. For HTTP dates, this is always UTC, so this option is equivalent to setting the `zone` option to 'utc', but this option is included for consistency with similar methods.\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromHTTP('Sun, 06 Nov 1994 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sunday, 06-Nov-94 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sun Nov  6 08:49:37 1994')\n   * @return {DateTime}\n   */\n  static fromHTTP(text, opts = {}) {\n    const [vals, parsedZone] = parseHTTPDate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"HTTP\", opts);\n  }\n\n  /**\n   * Create a DateTime from an input string and format string.\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/parsing?id=table-of-tokens).\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see the link below for the formats)\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromFormat(text, fmt, opts = {}) {\n    if (isUndefined(text) || isUndefined(fmt)) {\n      throw new InvalidArgumentError(\"fromFormat requires an input string and a format\");\n    }\n\n    const { locale = null, numberingSystem = null } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      }),\n      [vals, parsedZone, specificOffset, invalid] = parseFromTokens(localeToUse, text, fmt);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    } else {\n      return parseDataToDateTime(vals, parsedZone, opts, `format ${fmt}`, text, specificOffset);\n    }\n  }\n\n  /**\n   * @deprecated use fromFormat instead\n   */\n  static fromString(text, fmt, opts = {}) {\n    return DateTime.fromFormat(text, fmt, opts);\n  }\n\n  /**\n   * Create a DateTime from a SQL date, time, or datetime\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale\n   * @param {string} text - the string to parse\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @example DateTime.fromSQL('2017-05-15')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342+06:00')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles', { setZone: true })\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342', { zone: 'America/Los_Angeles' })\n   * @example DateTime.fromSQL('09:12:34.342')\n   * @return {DateTime}\n   */\n  static fromSQL(text, opts = {}) {\n    const [vals, parsedZone] = parseSQL(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"SQL\", text);\n  }\n\n  /**\n   * Create an invalid DateTime.\n   * @param {string} reason - simple string of why this DateTime is invalid. Should not contain parameters or anything else data-dependent.\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {DateTime}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the DateTime is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDateTimeError(invalid);\n    } else {\n      return new DateTime({ invalid });\n    }\n  }\n\n  /**\n   * Check if an object is an instance of DateTime. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDateTime(o) {\n    return (o && o.isLuxonDateTime) || false;\n  }\n\n  /**\n   * Produce the format string for a set of options\n   * @param formatOpts\n   * @param localeOpts\n   * @returns {string}\n   */\n  static parseFormatForOpts(formatOpts, localeOpts = {}) {\n    const tokenList = formatOptsToTokens(formatOpts, Locale.fromObject(localeOpts));\n    return !tokenList ? null : tokenList.map((t) => (t ? t.val : null)).join(\"\");\n  }\n\n  /**\n   * Produce the the fully expanded format token for the locale\n   * Does NOT quote characters, so quoted tokens will not round trip correctly\n   * @param fmt\n   * @param localeOpts\n   * @returns {string}\n   */\n  static expandFormat(fmt, localeOpts = {}) {\n    const expanded = expandMacroTokens(Formatter.parseFormat(fmt), Locale.fromObject(localeOpts));\n    return expanded.map((t) => t.val).join(\"\");\n  }\n\n  static resetCache() {\n    zoneOffsetTs = undefined;\n    zoneOffsetGuessCache = {};\n  }\n\n  // INFO\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example DateTime.local(2017, 7, 4).get('month'); //=> 7\n   * @example DateTime.local(2017, 7, 4).get('day'); //=> 4\n   * @return {number}\n   */\n  get(unit) {\n    return this[unit];\n  }\n\n  /**\n   * Returns whether the DateTime is valid. Invalid DateTimes occur when:\n   * * The DateTime was created from invalid calendar information, such as the 13th month or February 30\n   * * The DateTime was created by an operation on another invalid date\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this DateTime is invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this DateTime became invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Get the locale of a DateTime, such 'en-GB'. The locale is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a DateTime, such 'beng'. The numbering system is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Get the output calendar of a DateTime, such 'islamic'. The output calendar is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get outputCalendar() {\n    return this.isValid ? this.loc.outputCalendar : null;\n  }\n\n  /**\n   * Get the time zone associated with this DateTime.\n   * @type {Zone}\n   */\n  get zone() {\n    return this._zone;\n  }\n\n  /**\n   * Get the name of the time zone.\n   * @type {string}\n   */\n  get zoneName() {\n    return this.isValid ? this.zone.name : null;\n  }\n\n  /**\n   * Get the year\n   * @example DateTime.local(2017, 5, 25).year //=> 2017\n   * @type {number}\n   */\n  get year() {\n    return this.isValid ? this.c.year : NaN;\n  }\n\n  /**\n   * Get the quarter\n   * @example DateTime.local(2017, 5, 25).quarter //=> 2\n   * @type {number}\n   */\n  get quarter() {\n    return this.isValid ? Math.ceil(this.c.month / 3) : NaN;\n  }\n\n  /**\n   * Get the month (1-12).\n   * @example DateTime.local(2017, 5, 25).month //=> 5\n   * @type {number}\n   */\n  get month() {\n    return this.isValid ? this.c.month : NaN;\n  }\n\n  /**\n   * Get the day of the month (1-30ish).\n   * @example DateTime.local(2017, 5, 25).day //=> 25\n   * @type {number}\n   */\n  get day() {\n    return this.isValid ? this.c.day : NaN;\n  }\n\n  /**\n   * Get the hour of the day (0-23).\n   * @example DateTime.local(2017, 5, 25, 9).hour //=> 9\n   * @type {number}\n   */\n  get hour() {\n    return this.isValid ? this.c.hour : NaN;\n  }\n\n  /**\n   * Get the minute of the hour (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30).minute //=> 30\n   * @type {number}\n   */\n  get minute() {\n    return this.isValid ? this.c.minute : NaN;\n  }\n\n  /**\n   * Get the second of the minute (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52).second //=> 52\n   * @type {number}\n   */\n  get second() {\n    return this.isValid ? this.c.second : NaN;\n  }\n\n  /**\n   * Get the millisecond of the second (0-999).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52, 654).millisecond //=> 654\n   * @type {number}\n   */\n  get millisecond() {\n    return this.isValid ? this.c.millisecond : NaN;\n  }\n\n  /**\n   * Get the week year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 12, 31).weekYear //=> 2015\n   * @type {number}\n   */\n  get weekYear() {\n    return this.isValid ? possiblyCachedWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the week number of the week year (1-52ish).\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2017, 5, 25).weekNumber //=> 21\n   * @type {number}\n   */\n  get weekNumber() {\n    return this.isValid ? possiblyCachedWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the day of the week.\n   * 1 is Monday and 7 is Sunday\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 11, 31).weekday //=> 4\n   * @type {number}\n   */\n  get weekday() {\n    return this.isValid ? possiblyCachedWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Returns true if this date is on a weekend according to the locale, false otherwise\n   * @returns {boolean}\n   */\n  get isWeekend() {\n    return this.isValid && this.loc.getWeekendDays().includes(this.weekday);\n  }\n\n  /**\n   * Get the day of the week according to the locale.\n   * 1 is the first day of the week and 7 is the last day of the week.\n   * If the locale assigns Sunday as the first day of the week, then a date which is a Sunday will return 1,\n   * @returns {number}\n   */\n  get localWeekday() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Get the week number of the week year according to the locale. Different locales assign week numbers differently,\n   * because the week can start on different days of the week (see localWeekday) and because a different number of days\n   * is required for a week to count as the first week of a year.\n   * @returns {number}\n   */\n  get localWeekNumber() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the week year according to the locale. Different locales assign week numbers (and therefor week years)\n   * differently, see localWeekNumber.\n   * @returns {number}\n   */\n  get localWeekYear() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the ordinal (meaning the day of the year)\n   * @example DateTime.local(2017, 5, 25).ordinal //=> 145\n   * @type {number|DateTime}\n   */\n  get ordinal() {\n    return this.isValid ? gregorianToOrdinal(this.c).ordinal : NaN;\n  }\n\n  /**\n   * Get the human readable short month name, such as 'Oct'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthShort //=> Oct\n   * @type {string}\n   */\n  get monthShort() {\n    return this.isValid ? Info.months(\"short\", { locObj: this.loc })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable long month name, such as 'October'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthLong //=> October\n   * @type {string}\n   */\n  get monthLong() {\n    return this.isValid ? Info.months(\"long\", { locObj: this.loc })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable short weekday, such as 'Mon'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayShort //=> Mon\n   * @type {string}\n   */\n  get weekdayShort() {\n    return this.isValid ? Info.weekdays(\"short\", { locObj: this.loc })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the human readable long weekday, such as 'Monday'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayLong //=> Monday\n   * @type {string}\n   */\n  get weekdayLong() {\n    return this.isValid ? Info.weekdays(\"long\", { locObj: this.loc })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the UTC offset of this DateTime in minutes\n   * @example DateTime.now().offset //=> -240\n   * @example DateTime.utc().offset //=> 0\n   * @type {number}\n   */\n  get offset() {\n    return this.isValid ? +this.o : NaN;\n  }\n\n  /**\n   * Get the short human name for the zone's current offset, for example \"EST\" or \"EDT\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameShort() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"short\",\n        locale: this.locale,\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get the long human name for the zone's current offset, for example \"Eastern Standard Time\" or \"Eastern Daylight Time\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameLong() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"long\",\n        locale: this.locale,\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get whether this zone's offset ever changes, as in a DST.\n   * @type {boolean}\n   */\n  get isOffsetFixed() {\n    return this.isValid ? this.zone.isUniversal : null;\n  }\n\n  /**\n   * Get whether the DateTime is in a DST.\n   * @type {boolean}\n   */\n  get isInDST() {\n    if (this.isOffsetFixed) {\n      return false;\n    } else {\n      return (\n        this.offset > this.set({ month: 1, day: 1 }).offset ||\n        this.offset > this.set({ month: 5 }).offset\n      );\n    }\n  }\n\n  /**\n   * Get those DateTimes which have the same local time as this DateTime, but a different offset from UTC\n   * in this DateTime's zone. During DST changes local time can be ambiguous, for example\n   * `2023-10-29T02:30:00` in `Europe/Berlin` can have offset `+01:00` or `+02:00`.\n   * This method will return both possible DateTimes if this DateTime's local time is ambiguous.\n   * @returns {DateTime[]}\n   */\n  getPossibleOffsets() {\n    if (!this.isValid || this.isOffsetFixed) {\n      return [this];\n    }\n    const dayMs = 86400000;\n    const minuteMs = 60000;\n    const localTS = objToLocalTS(this.c);\n    const oEarlier = this.zone.offset(localTS - dayMs);\n    const oLater = this.zone.offset(localTS + dayMs);\n\n    const o1 = this.zone.offset(localTS - oEarlier * minuteMs);\n    const o2 = this.zone.offset(localTS - oLater * minuteMs);\n    if (o1 === o2) {\n      return [this];\n    }\n    const ts1 = localTS - o1 * minuteMs;\n    const ts2 = localTS - o2 * minuteMs;\n    const c1 = tsToObj(ts1, o1);\n    const c2 = tsToObj(ts2, o2);\n    if (\n      c1.hour === c2.hour &&\n      c1.minute === c2.minute &&\n      c1.second === c2.second &&\n      c1.millisecond === c2.millisecond\n    ) {\n      return [clone(this, { ts: ts1 }), clone(this, { ts: ts2 })];\n    }\n    return [this];\n  }\n\n  /**\n   * Returns true if this DateTime is in a leap year, false otherwise\n   * @example DateTime.local(2016).isInLeapYear //=> true\n   * @example DateTime.local(2013).isInLeapYear //=> false\n   * @type {boolean}\n   */\n  get isInLeapYear() {\n    return isLeapYear(this.year);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's month\n   * @example DateTime.local(2016, 2).daysInMonth //=> 29\n   * @example DateTime.local(2016, 3).daysInMonth //=> 31\n   * @type {number}\n   */\n  get daysInMonth() {\n    return daysInMonth(this.year, this.month);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's year\n   * @example DateTime.local(2016).daysInYear //=> 366\n   * @example DateTime.local(2013).daysInYear //=> 365\n   * @type {number}\n   */\n  get daysInYear() {\n    return this.isValid ? daysInYear(this.year) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2004).weeksInWeekYear //=> 53\n   * @example DateTime.local(2013).weeksInWeekYear //=> 52\n   * @type {number}\n   */\n  get weeksInWeekYear() {\n    return this.isValid ? weeksInWeekYear(this.weekYear) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's local week year\n   * @example DateTime.local(2020, 6, {locale: 'en-US'}).weeksInLocalWeekYear //=> 52\n   * @example DateTime.local(2020, 6, {locale: 'de-DE'}).weeksInLocalWeekYear //=> 53\n   * @type {number}\n   */\n  get weeksInLocalWeekYear() {\n    return this.isValid\n      ? weeksInWeekYear(\n          this.localWeekYear,\n          this.loc.getMinDaysInFirstWeek(),\n          this.loc.getStartOfWeek()\n        )\n      : NaN;\n  }\n\n  /**\n   * Returns the resolved Intl options for this DateTime.\n   * This is useful in understanding the behavior of formatting methods\n   * @param {Object} opts - the same options as toLocaleString\n   * @return {Object}\n   */\n  resolvedLocaleOptions(opts = {}) {\n    const { locale, numberingSystem, calendar } = Formatter.create(\n      this.loc.clone(opts),\n      opts\n    ).resolvedOptions(this);\n    return { locale, numberingSystem, outputCalendar: calendar };\n  }\n\n  // TRANSFORM\n\n  /**\n   * \"Set\" the DateTime's zone to UTC. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to {@link DateTime#setZone}('utc')\n   * @param {number} [offset=0] - optionally, an offset from UTC in minutes\n   * @param {Object} [opts={}] - options to pass to `setZone()`\n   * @return {DateTime}\n   */\n  toUTC(offset = 0, opts = {}) {\n    return this.setZone(FixedOffsetZone.instance(offset), opts);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to the host's local zone. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to `setZone('local')`\n   * @return {DateTime}\n   */\n  toLocal() {\n    return this.setZone(Settings.defaultZone);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to specified zone. Returns a newly-constructed DateTime.\n   *\n   * By default, the setter keeps the underlying time the same (as in, the same timestamp), but the new instance will report different local times and consider DSTs when making computations, as with {@link DateTime#plus}. You may wish to use {@link DateTime#toLocal} and {@link DateTime#toUTC} which provide simple convenience wrappers for commonly used zones.\n   * @param {string|Zone} [zone='local'] - a zone identifier. As a string, that can be any IANA zone supported by the host environment, or a fixed-offset name of the form 'UTC+3', or the strings 'local' or 'utc'. You may also supply an instance of a {@link DateTime#Zone} class.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.keepLocalTime=false] - If true, adjust the underlying time so that the local time stays the same, but in the target zone. You should rarely need this.\n   * @return {DateTime}\n   */\n  setZone(zone, { keepLocalTime = false, keepCalendarTime = false } = {}) {\n    zone = normalizeZone(zone, Settings.defaultZone);\n    if (zone.equals(this.zone)) {\n      return this;\n    } else if (!zone.isValid) {\n      return DateTime.invalid(unsupportedZone(zone));\n    } else {\n      let newTS = this.ts;\n      if (keepLocalTime || keepCalendarTime) {\n        const offsetGuess = zone.offset(this.ts);\n        const asObj = this.toObject();\n        [newTS] = objToTS(asObj, offsetGuess, zone);\n      }\n      return clone(this, { ts: newTS, zone });\n    }\n  }\n\n  /**\n   * \"Set\" the locale, numberingSystem, or outputCalendar. Returns a newly-constructed DateTime.\n   * @param {Object} properties - the properties to set\n   * @example DateTime.local(2017, 5, 25).reconfigure({ locale: 'en-GB' })\n   * @return {DateTime}\n   */\n  reconfigure({ locale, numberingSystem, outputCalendar } = {}) {\n    const loc = this.loc.clone({ locale, numberingSystem, outputCalendar });\n    return clone(this, { loc });\n  }\n\n  /**\n   * \"Set\" the locale. Returns a newly-constructed DateTime.\n   * Just a convenient alias for reconfigure({ locale })\n   * @example DateTime.local(2017, 5, 25).setLocale('en-GB')\n   * @return {DateTime}\n   */\n  setLocale(locale) {\n    return this.reconfigure({ locale });\n  }\n\n  /**\n   * \"Set\" the values of specified units. Returns a newly-constructed DateTime.\n   * You can only set units with this method; for \"setting\" metadata, see {@link DateTime#reconfigure} and {@link DateTime#setZone}.\n   *\n   * This method also supports setting locale-based week units, i.e. `localWeekday`, `localWeekNumber` and `localWeekYear`.\n   * They cannot be mixed with ISO-week units like `weekday`.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dt.set({ year: 2017 })\n   * @example dt.set({ hour: 8, minute: 30 })\n   * @example dt.set({ weekday: 5 })\n   * @example dt.set({ year: 2005, ordinal: 234 })\n   * @return {DateTime}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n\n    const normalized = normalizeObject(values, normalizeUnitWithLocalWeeks);\n    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, this.loc);\n\n    const settingWeekStuff =\n        !isUndefined(normalized.weekYear) ||\n        !isUndefined(normalized.weekNumber) ||\n        !isUndefined(normalized.weekday),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\n        \"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"\n      );\n    }\n\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n\n    let mixed;\n    if (settingWeekStuff) {\n      mixed = weekToGregorian(\n        { ...gregorianToWeek(this.c, minDaysInFirstWeek, startOfWeek), ...normalized },\n        minDaysInFirstWeek,\n        startOfWeek\n      );\n    } else if (!isUndefined(normalized.ordinal)) {\n      mixed = ordinalToGregorian({ ...gregorianToOrdinal(this.c), ...normalized });\n    } else {\n      mixed = { ...this.toObject(), ...normalized };\n\n      // if we didn't set the day but we ended up on an overflow date,\n      // use the last day of the right month\n      if (isUndefined(normalized.day)) {\n        mixed.day = Math.min(daysInMonth(mixed.year, mixed.month), mixed.day);\n      }\n    }\n\n    const [ts, o] = objToTS(mixed, this.o, this.zone);\n    return clone(this, { ts, o });\n  }\n\n  /**\n   * Add a period of time to this DateTime and return the resulting DateTime\n   *\n   * Adding hours, minutes, seconds, or milliseconds increases the timestamp by the right number of milliseconds. Adding days, months, or years shifts the calendar, accounting for DSTs and leap years along the way. Thus, `dt.plus({ hours: 24 })` may result in a different time than `dt.plus({ days: 1 })` if there's a DST shift in between.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @example DateTime.now().plus(123) //~> in 123 milliseconds\n   * @example DateTime.now().plus({ minutes: 15 }) //~> in 15 minutes\n   * @example DateTime.now().plus({ days: 1 }) //~> this time tomorrow\n   * @example DateTime.now().plus({ days: -1 }) //~> this time yesterday\n   * @example DateTime.now().plus({ hours: 3, minutes: 13 }) //~> in 3 hr, 13 min\n   * @example DateTime.now().plus(Duration.fromObject({ hours: 3, minutes: 13 })) //~> in 3 hr, 13 min\n   * @return {DateTime}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration);\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * Subtract a period of time to this DateTime and return the resulting DateTime\n   * See {@link DateTime#plus}\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   @return {DateTime}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration).negate();\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * \"Set\" this DateTime to the beginning of a unit of time.\n   * @param {string} unit - The unit to go to the beginning of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).startOf('month').toISODate(); //=> '2014-03-01'\n   * @example DateTime.local(2014, 3, 3).startOf('year').toISODate(); //=> '2014-01-01'\n   * @example DateTime.local(2014, 3, 3).startOf('week').toISODate(); //=> '2014-03-03', weeks always start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('day').toISOTime(); //=> '00:00.000-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('hour').toISOTime(); //=> '05:00:00.000-05:00'\n   * @return {DateTime}\n   */\n  startOf(unit, { useLocaleWeeks = false } = {}) {\n    if (!this.isValid) return this;\n\n    const o = {},\n      normalizedUnit = Duration.normalizeUnit(unit);\n    switch (normalizedUnit) {\n      case \"years\":\n        o.month = 1;\n      // falls through\n      case \"quarters\":\n      case \"months\":\n        o.day = 1;\n      // falls through\n      case \"weeks\":\n      case \"days\":\n        o.hour = 0;\n      // falls through\n      case \"hours\":\n        o.minute = 0;\n      // falls through\n      case \"minutes\":\n        o.second = 0;\n      // falls through\n      case \"seconds\":\n        o.millisecond = 0;\n        break;\n      case \"milliseconds\":\n        break;\n      // no default, invalid units throw in normalizeUnit()\n    }\n\n    if (normalizedUnit === \"weeks\") {\n      if (useLocaleWeeks) {\n        const startOfWeek = this.loc.getStartOfWeek();\n        const { weekday } = this;\n        if (weekday < startOfWeek) {\n          o.weekNumber = this.weekNumber - 1;\n        }\n        o.weekday = startOfWeek;\n      } else {\n        o.weekday = 1;\n      }\n    }\n\n    if (normalizedUnit === \"quarters\") {\n      const q = Math.ceil(this.month / 3);\n      o.month = (q - 1) * 3 + 1;\n    }\n\n    return this.set(o);\n  }\n\n  /**\n   * \"Set\" this DateTime to the end (meaning the last millisecond) of a unit of time\n   * @param {string} unit - The unit to go to the end of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).endOf('month').toISO(); //=> '2014-03-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('year').toISO(); //=> '2014-12-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('week').toISO(); // => '2014-03-09T23:59:59.999-05:00', weeks start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('day').toISO(); //=> '2014-03-03T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('hour').toISO(); //=> '2014-03-03T05:59:59.999-05:00'\n   * @return {DateTime}\n   */\n  endOf(unit, opts) {\n    return this.isValid\n      ? this.plus({ [unit]: 1 })\n          .startOf(unit, opts)\n          .minus(1)\n      : this;\n  }\n\n  // OUTPUT\n\n  /**\n   * Returns a string representation of this DateTime formatted according to the specified format string.\n   * **You may not want this.** See {@link DateTime#toLocaleString} for a more flexible formatting tool. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/formatting?id=table-of-tokens).\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toFormat('yyyy LLL dd') //=> '2017 Apr 22'\n   * @example DateTime.now().setLocale('fr').toFormat('yyyy LLL dd') //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat('yyyy LLL dd', { locale: \"fr\" }) //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat(\"HH 'hours and' mm 'minutes'\") //=> '20 hours and 55 minutes'\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.redefaultToEN(opts)).formatDateTimeFromString(this, fmt)\n      : INVALID;\n  }\n\n  /**\n   * Returns a localized string representing this date. Accepts the same options as the Intl.DateTimeFormat constructor and any presets defined by Luxon, such as `DateTime.DATE_FULL` or `DateTime.TIME_SIMPLE`.\n   * The exact behavior of this method is browser-specific, but in general it will return an appropriate representation\n   * of the DateTime in the assigned locale.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param formatOpts {Object} - Intl.DateTimeFormat constructor options and configuration options\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toLocaleString(); //=> 4/20/2017\n   * @example DateTime.now().setLocale('en-gb').toLocaleString(); //=> '20/04/2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL); //=> 'April 20, 2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL, { locale: 'fr' }); //=> '28 août 2022'\n   * @example DateTime.now().toLocaleString(DateTime.TIME_SIMPLE); //=> '11:32 AM'\n   * @example DateTime.now().toLocaleString(DateTime.DATETIME_SHORT); //=> '4/20/2017, 11:32 AM'\n   * @example DateTime.now().toLocaleString({ weekday: 'long', month: 'long', day: '2-digit' }); //=> 'Thursday, April 20'\n   * @example DateTime.now().toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> 'Thu, Apr 20, 11:27 AM'\n   * @example DateTime.now().toLocaleString({ hour: '2-digit', minute: '2-digit', hourCycle: 'h23' }); //=> '11:32'\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.clone(opts), formatOpts).formatDateTime(this)\n      : INVALID;\n  }\n\n  /**\n   * Returns an array of format \"parts\", meaning individual tokens along with metadata. This is allows callers to post-process individual sections of the formatted output.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat/formatToParts\n   * @param opts {Object} - Intl.DateTimeFormat constructor options, same as `toLocaleString`.\n   * @example DateTime.now().toLocaleParts(); //=> [\n   *                                   //=>   { type: 'day', value: '25' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'month', value: '05' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'year', value: '1982' }\n   *                                   //=> ]\n   */\n  toLocaleParts(opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.clone(opts), opts).formatDateTimeParts(this)\n      : [];\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=false] - add the time zone format extension\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc(1983, 5, 25).toISO() //=> '1982-05-25T00:00:00.000Z'\n   * @example DateTime.now().toISO() //=> '2017-04-22T20:47:05.335-04:00'\n   * @example DateTime.now().toISO({ includeOffset: false }) //=> '2017-04-22T20:47:05.335'\n   * @example DateTime.now().toISO({ format: 'basic' }) //=> '20170422T204705.335-0400'\n   * @return {string}\n   */\n  toISO({\n    format = \"extended\",\n    suppressSeconds = false,\n    suppressMilliseconds = false,\n    includeOffset = true,\n    extendedZone = false,\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    const ext = format === \"extended\";\n\n    let c = toISODate(this, ext);\n    c += \"T\";\n    c += toISOTime(this, ext, suppressSeconds, suppressMilliseconds, includeOffset, extendedZone);\n    return c;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's date component\n   * @param {Object} opts - options\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc(1982, 5, 25).toISODate() //=> '1982-05-25'\n   * @example DateTime.utc(1982, 5, 25).toISODate({ format: 'basic' }) //=> '19820525'\n   * @return {string}\n   */\n  toISODate({ format = \"extended\" } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    return toISODate(this, format === \"extended\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's week date\n   * @example DateTime.utc(1982, 5, 25).toISOWeekDate() //=> '1982-W21-2'\n   * @return {string}\n   */\n  toISOWeekDate() {\n    return toTechFormat(this, \"kkkk-'W'WW-c\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's time component\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=true] - add the time zone format extension\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime() //=> '07:34:19.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34, seconds: 0, milliseconds: 0 }).toISOTime({ suppressSeconds: true }) //=> '07:34Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ format: 'basic' }) //=> '073419.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ includePrefix: true }) //=> 'T07:34:19.361Z'\n   * @return {string}\n   */\n  toISOTime({\n    suppressMilliseconds = false,\n    suppressSeconds = false,\n    includeOffset = true,\n    includePrefix = false,\n    extendedZone = false,\n    format = \"extended\",\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    let c = includePrefix ? \"T\" : \"\";\n    return (\n      c +\n      toISOTime(\n        this,\n        format === \"extended\",\n        suppressSeconds,\n        suppressMilliseconds,\n        includeOffset,\n        extendedZone\n      )\n    );\n  }\n\n  /**\n   * Returns an RFC 2822-compatible string representation of this DateTime\n   * @example DateTime.utc(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 +0000'\n   * @example DateTime.local(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 -0400'\n   * @return {string}\n   */\n  toRFC2822() {\n    return toTechFormat(this, \"EEE, dd LLL yyyy HH:mm:ss ZZZ\", false);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in HTTP headers. The output is always expressed in GMT.\n   * Specifically, the string conforms to RFC 1123.\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @example DateTime.utc(2014, 7, 13).toHTTP() //=> 'Sun, 13 Jul 2014 00:00:00 GMT'\n   * @example DateTime.utc(2014, 7, 13, 19).toHTTP() //=> 'Sun, 13 Jul 2014 19:00:00 GMT'\n   * @return {string}\n   */\n  toHTTP() {\n    return toTechFormat(this.toUTC(), \"EEE, dd LLL yyyy HH:mm:ss 'GMT'\");\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Date\n   * @example DateTime.utc(2014, 7, 13).toSQLDate() //=> '2014-07-13'\n   * @return {string}\n   */\n  toSQLDate() {\n    if (!this.isValid) {\n      return null;\n    }\n    return toISODate(this, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Time\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc().toSQL() //=> '05:15:16.345'\n   * @example DateTime.now().toSQL() //=> '05:15:16.345 -04:00'\n   * @example DateTime.now().toSQL({ includeOffset: false }) //=> '05:15:16.345'\n   * @example DateTime.now().toSQL({ includeZone: false }) //=> '05:15:16.345 America/New_York'\n   * @return {string}\n   */\n  toSQLTime({ includeOffset = true, includeZone = false, includeOffsetSpace = true } = {}) {\n    let fmt = \"HH:mm:ss.SSS\";\n\n    if (includeZone || includeOffset) {\n      if (includeOffsetSpace) {\n        fmt += \" \";\n      }\n      if (includeZone) {\n        fmt += \"z\";\n      } else if (includeOffset) {\n        fmt += \"ZZ\";\n      }\n    }\n\n    return toTechFormat(this, fmt, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 Z'\n   * @example DateTime.local(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 -04:00'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeOffset: false }) //=> '2014-07-13 00:00:00.000'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeZone: true }) //=> '2014-07-13 00:00:00.000 America/New_York'\n   * @return {string}\n   */\n  toSQL(opts = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    return `${this.toSQLDate()} ${this.toSQLTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for debugging\n   * @return {string}\n   */\n  toString() {\n    return this.isValid ? this.toISO() : INVALID;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`;\n    } else {\n      return `DateTime { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime. Alias of {@link DateTime#toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime.\n   * @return {number}\n   */\n  toMillis() {\n    return this.isValid ? this.ts : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds of this DateTime.\n   * @return {number}\n   */\n  toSeconds() {\n    return this.isValid ? this.ts / 1000 : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds (as a whole number) of this DateTime.\n   * @return {number}\n   */\n  toUnixInteger() {\n    return this.isValid ? Math.floor(this.ts / 1000) : NaN;\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this DateTime appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a BSON serializable equivalent to this DateTime.\n   * @return {Date}\n   */\n  toBSON() {\n    return this.toJSDate();\n  }\n\n  /**\n   * Returns a JavaScript object with this DateTime's year, month, day, and so on.\n   * @param opts - options for generating the object\n   * @param {boolean} [opts.includeConfig=false] - include configuration attributes in the output\n   * @example DateTime.now().toObject() //=> { year: 2017, month: 4, day: 22, hour: 20, minute: 49, second: 42, millisecond: 268 }\n   * @return {Object}\n   */\n  toObject(opts = {}) {\n    if (!this.isValid) return {};\n\n    const base = { ...this.c };\n\n    if (opts.includeConfig) {\n      base.outputCalendar = this.outputCalendar;\n      base.numberingSystem = this.loc.numberingSystem;\n      base.locale = this.loc.locale;\n    }\n    return base;\n  }\n\n  /**\n   * Returns a JavaScript Date equivalent to this DateTime.\n   * @return {Date}\n   */\n  toJSDate() {\n    return new Date(this.isValid ? this.ts : NaN);\n  }\n\n  // COMPARE\n\n  /**\n   * Return the difference between two DateTimes as a Duration.\n   * @param {DateTime} otherDateTime - the DateTime to compare this one to\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or array of units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example\n   * var i1 = DateTime.fromISO('1982-05-25T09:45'),\n   *     i2 = DateTime.fromISO('1983-10-14T10:30');\n   * i2.diff(i1).toObject() //=> { milliseconds: 43807500000 }\n   * i2.diff(i1, 'hours').toObject() //=> { hours: 12168.75 }\n   * i2.diff(i1, ['months', 'days']).toObject() //=> { months: 16, days: 19.03125 }\n   * i2.diff(i1, ['months', 'days', 'hours']).toObject() //=> { months: 16, days: 19, hours: 0.75 }\n   * @return {Duration}\n   */\n  diff(otherDateTime, unit = \"milliseconds\", opts = {}) {\n    if (!this.isValid || !otherDateTime.isValid) {\n      return Duration.invalid(\"created by diffing an invalid DateTime\");\n    }\n\n    const durOpts = { locale: this.locale, numberingSystem: this.numberingSystem, ...opts };\n\n    const units = maybeArray(unit).map(Duration.normalizeUnit),\n      otherIsLater = otherDateTime.valueOf() > this.valueOf(),\n      earlier = otherIsLater ? this : otherDateTime,\n      later = otherIsLater ? otherDateTime : this,\n      diffed = diff(earlier, later, units, durOpts);\n\n    return otherIsLater ? diffed.negate() : diffed;\n  }\n\n  /**\n   * Return the difference between this DateTime and right now.\n   * See {@link DateTime#diff}\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units units (such as 'hours' or 'days') to include in the duration\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  diffNow(unit = \"milliseconds\", opts = {}) {\n    return this.diff(DateTime.now(), unit, opts);\n  }\n\n  /**\n   * Return an Interval spanning between this DateTime and another DateTime\n   * @param {DateTime} otherDateTime - the other end point of the Interval\n   * @return {Interval}\n   */\n  until(otherDateTime) {\n    return this.isValid ? Interval.fromDateTimes(this, otherDateTime) : this;\n  }\n\n  /**\n   * Return whether this DateTime is in the same unit of time as another DateTime.\n   * Higher-order units must also be identical for this function to return `true`.\n   * Note that time zones are **ignored** in this comparison, which compares the **local** calendar time. Use {@link DateTime#setZone} to convert one of the dates if needed.\n   * @param {DateTime} otherDateTime - the other DateTime\n   * @param {string} unit - the unit of time to check sameness on\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; only the locale of this DateTime is used\n   * @example DateTime.now().hasSame(otherDT, 'day'); //~> true if otherDT is in the same current calendar day\n   * @return {boolean}\n   */\n  hasSame(otherDateTime, unit, opts) {\n    if (!this.isValid) return false;\n\n    const inputMs = otherDateTime.valueOf();\n    const adjustedToZone = this.setZone(otherDateTime.zone, { keepLocalTime: true });\n    return (\n      adjustedToZone.startOf(unit, opts) <= inputMs && inputMs <= adjustedToZone.endOf(unit, opts)\n    );\n  }\n\n  /**\n   * Equality check\n   * Two DateTimes are equal if and only if they represent the same millisecond, have the same zone and location, and are both valid.\n   * To compare just the millisecond values, use `+dt1 === +dt2`.\n   * @param {DateTime} other - the other DateTime\n   * @return {boolean}\n   */\n  equals(other) {\n    return (\n      this.isValid &&\n      other.isValid &&\n      this.valueOf() === other.valueOf() &&\n      this.zone.equals(other.zone) &&\n      this.loc.equals(other.loc)\n    );\n  }\n\n  /**\n   * Returns a string representation of a this time relative to now, such as \"in two days\". Can only internationalize if your\n   * platform supports Intl.RelativeTimeFormat. Rounds down by default.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} [options.style=\"long\"] - the style of units, must be \"long\", \"short\", or \"narrow\"\n   * @param {string|string[]} options.unit - use a specific unit or array of units; if omitted, or an array, the method will pick the best unit. Use an array or one of \"years\", \"quarters\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", or \"seconds\"\n   * @param {boolean} [options.round=true] - whether to round the numbers in the output.\n   * @param {number} [options.padding=0] - padding in milliseconds. This allows you to round up the result if it fits inside the threshold. Don't use in combination with {round: false} because the decimal output will include the padding.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelative() //=> \"in 1 day\"\n   * @example DateTime.now().setLocale(\"es\").toRelative({ days: 1 }) //=> \"dentro de 1 día\"\n   * @example DateTime.now().plus({ days: 1 }).toRelative({ locale: \"fr\" }) //=> \"dans 23 heures\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative() //=> \"2 days ago\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative({ unit: \"hours\" }) //=> \"48 hours ago\"\n   * @example DateTime.now().minus({ hours: 36 }).toRelative({ round: false }) //=> \"1.5 days ago\"\n   */\n  toRelative(options = {}) {\n    if (!this.isValid) return null;\n    const base = options.base || DateTime.fromObject({}, { zone: this.zone }),\n      padding = options.padding ? (this < base ? -options.padding : options.padding) : 0;\n    let units = [\"years\", \"months\", \"days\", \"hours\", \"minutes\", \"seconds\"];\n    let unit = options.unit;\n    if (Array.isArray(options.unit)) {\n      units = options.unit;\n      unit = undefined;\n    }\n    return diffRelative(base, this.plus(padding), {\n      ...options,\n      numeric: \"always\",\n      units,\n      unit,\n    });\n  }\n\n  /**\n   * Returns a string representation of this date relative to today, such as \"yesterday\" or \"next month\".\n   * Only internationalizes on platforms that supports Intl.RelativeTimeFormat.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.unit - use a specific unit; if omitted, the method will pick the unit. Use one of \"years\", \"quarters\", \"months\", \"weeks\", or \"days\"\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar() //=> \"tomorrow\"\n   * @example DateTime.now().setLocale(\"es\").plus({ days: 1 }).toRelative() //=> \"\"mañana\"\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar({ locale: \"fr\" }) //=> \"demain\"\n   * @example DateTime.now().minus({ days: 2 }).toRelativeCalendar() //=> \"2 days ago\"\n   */\n  toRelativeCalendar(options = {}) {\n    if (!this.isValid) return null;\n\n    return diffRelative(options.base || DateTime.fromObject({}, { zone: this.zone }), this, {\n      ...options,\n      numeric: \"auto\",\n      units: [\"years\", \"months\", \"days\"],\n      calendary: true,\n    });\n  }\n\n  /**\n   * Return the min of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the minimum\n   * @return {DateTime} the min DateTime, or undefined if called with no argument\n   */\n  static min(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"min requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, (i) => i.valueOf(), Math.min);\n  }\n\n  /**\n   * Return the max of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the maximum\n   * @return {DateTime} the max DateTime, or undefined if called with no argument\n   */\n  static max(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"max requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, (i) => i.valueOf(), Math.max);\n  }\n\n  // MISC\n\n  /**\n   * Explain how a string would be parsed by fromFormat()\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see description)\n   * @param {Object} options - options taken by fromFormat()\n   * @return {Object}\n   */\n  static fromFormatExplain(text, fmt, options = {}) {\n    const { locale = null, numberingSystem = null } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n    return explainFromTokens(localeToUse, text, fmt);\n  }\n\n  /**\n   * @deprecated use fromFormatExplain instead\n   */\n  static fromStringExplain(text, fmt, options = {}) {\n    return DateTime.fromFormatExplain(text, fmt, options);\n  }\n\n  /**\n   * Build a parser for `fmt` using the given locale. This parser can be passed\n   * to {@link DateTime.fromFormatParser} to a parse a date in this format. This\n   * can be used to optimize cases where many dates need to be parsed in a\n   * specific format.\n   *\n   * @param {String} fmt - the format the string is expected to be in (see\n   * description)\n   * @param {Object} options - options used to set locale and numberingSystem\n   * for parser\n   * @returns {TokenParser} - opaque object to be used\n   */\n  static buildFormatParser(fmt, options = {}) {\n    const { locale = null, numberingSystem = null } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n    return new TokenParser(localeToUse, fmt);\n  }\n\n  /**\n   * Create a DateTime from an input string and format parser.\n   *\n   * The format parser must have been created with the same locale as this call.\n   *\n   * @param {String} text - the string to parse\n   * @param {TokenParser} formatParser - parser from {@link DateTime.buildFormatParser}\n   * @param {Object} opts - options taken by fromFormat()\n   * @returns {DateTime}\n   */\n  static fromFormatParser(text, formatParser, opts = {}) {\n    if (isUndefined(text) || isUndefined(formatParser)) {\n      throw new InvalidArgumentError(\n        \"fromFormatParser requires an input string and a format parser\"\n      );\n    }\n    const { locale = null, numberingSystem = null } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n\n    if (!localeToUse.equals(formatParser.locale)) {\n      throw new InvalidArgumentError(\n        `fromFormatParser called with a locale of ${localeToUse}, ` +\n          `but the format parser was created for ${formatParser.locale}`\n      );\n    }\n\n    const { result, zone, specificOffset, invalidReason } = formatParser.explainFromTokens(text);\n\n    if (invalidReason) {\n      return DateTime.invalid(invalidReason);\n    } else {\n      return parseDataToDateTime(\n        result,\n        zone,\n        opts,\n        `format ${formatParser.format}`,\n        text,\n        specificOffset\n      );\n    }\n  }\n\n  // FORMAT PRESETS\n\n  /**\n   * {@link DateTime#toLocaleString} format like 10/14/1983\n   * @type {Object}\n   */\n  static get DATE_SHORT() {\n    return Formats.DATE_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED() {\n    return Formats.DATE_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED_WITH_WEEKDAY() {\n    return Formats.DATE_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_FULL() {\n    return Formats.DATE_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Tuesday, October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_HUGE() {\n    return Formats.DATE_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_SIMPLE() {\n    return Formats.TIME_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SECONDS() {\n    return Formats.TIME_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SHORT_OFFSET() {\n    return Formats.TIME_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_LONG_OFFSET() {\n    return Formats.TIME_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_SIMPLE() {\n    return Formats.TIME_24_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SECONDS() {\n    return Formats.TIME_24_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 EDT', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SHORT_OFFSET() {\n    return Formats.TIME_24_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 Eastern Daylight Time', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_LONG_OFFSET() {\n    return Formats.TIME_24_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT() {\n    return Formats.DATETIME_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT_WITH_SECONDS() {\n    return Formats.DATETIME_SHORT_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED() {\n    return Formats.DATETIME_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_SECONDS() {\n    return Formats.DATETIME_MED_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, 14 Oct 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_WEEKDAY() {\n    return Formats.DATETIME_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL() {\n    return Formats.DATETIME_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30:33 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL_WITH_SECONDS() {\n    return Formats.DATETIME_FULL_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE() {\n    return Formats.DATETIME_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30:33 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE_WITH_SECONDS() {\n    return Formats.DATETIME_HUGE_WITH_SECONDS;\n  }\n}\n\n/**\n * @private\n */\nexport function friendlyDateTime(dateTimeish) {\n  if (DateTime.isDateTime(dateTimeish)) {\n    return dateTimeish;\n  } else if (dateTimeish && dateTimeish.valueOf && isNumber(dateTimeish.valueOf())) {\n    return DateTime.fromJSDate(dateTimeish);\n  } else if (dateTimeish && typeof dateTimeish === \"object\") {\n    return DateTime.fromObject(dateTimeish);\n  } else {\n    throw new InvalidArgumentError(\n      `Unknown datetime argument: ${dateTimeish}, of type ${typeof dateTimeish}`\n    );\n  }\n}\n", "import DateTime from \"./datetime.js\";\nimport Duration from \"./duration.js\";\nimport Interval from \"./interval.js\";\nimport Info from \"./info.js\";\nimport Zone from \"./zone.js\";\nimport FixedOffsetZone from \"./zones/fixedOffsetZone.js\";\nimport IANAZone from \"./zones/IANAZone.js\";\nimport InvalidZone from \"./zones/invalidZone.js\";\nimport SystemZone from \"./zones/systemZone.js\";\nimport Settings from \"./settings.js\";\n\nconst VERSION = \"3.5.0\";\n\nexport {\n  VERSION,\n  DateTime,\n  Duration,\n  Interval,\n  Info,\n  Zone,\n  FixedOffsetZone,\n  IANAZone,\n  InvalidZone,\n  SystemZone,\n  Settings,\n};\n"], "names": ["singleton", "English.formatRelativeTime", "English.months", "English.weekdays", "English.meridiems", "English.eras", "Formats.DATE_SHORT", "Formats.DATE_MED", "Formats.DATE_FULL", "Formats.DATE_HUGE", "Formats.TIME_SIMPLE", "Formats.TIME_WITH_SECONDS", "Formats.TIME_WITH_SHORT_OFFSET", "Formats.TIME_WITH_LONG_OFFSET", "Formats.TIME_24_SIMPLE", "Formats.TIME_24_WITH_SECONDS", "Formats.TIME_24_WITH_SHORT_OFFSET", "Formats.TIME_24_WITH_LONG_OFFSET", "Formats.DATETIME_SHORT", "Formats.DATETIME_MED", "Formats.DATETIME_FULL", "Formats.DATETIME_HUGE", "Formats.DATETIME_SHORT_WITH_SECONDS", "Formats.DATETIME_MED_WITH_SECONDS", "Formats.DATETIME_FULL_WITH_SECONDS", "Formats.DATETIME_HUGE_WITH_SECONDS", "English.meridiemForDateTime", "English.monthForDateTime", "English.weekdayForDateTime", "English.eraForDateTime", "English.monthsShort", "English.weekdaysLong", "English.weekdaysShort", "INVALID", "orderedUnits", "clone", "Formats.DATE_MED_WITH_WEEKDAY", "Formats.DATETIME_MED_WITH_WEEKDAY"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,MAAM,UAAU,SAAS,KAAK,CAAC,EAAE;AACjC;AACA;AACA;AACA;AACO,MAAM,oBAAoB,SAAS,UAAU,CAAC;AACrD,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB,IAAI,KAAK,CAAC,CAAC,kBAAkB,EAAE,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;AACrD,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACO,MAAM,oBAAoB,SAAS,UAAU,CAAC;AACrD,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB,IAAI,KAAK,CAAC,CAAC,kBAAkB,EAAE,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;AACrD,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACO,MAAM,oBAAoB,SAAS,UAAU,CAAC;AACrD,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB,IAAI,KAAK,CAAC,CAAC,kBAAkB,EAAE,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;AACrD,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACO,MAAM,6BAA6B,SAAS,UAAU,CAAC,EAAE;AAChE;AACA;AACA;AACA;AACO,MAAM,gBAAgB,SAAS,UAAU,CAAC;AACjD,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,KAAK,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAClC,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACO,MAAM,oBAAoB,SAAS,UAAU,CAAC,EAAE;AACvD;AACA;AACA;AACA;AACO,MAAM,mBAAmB,SAAS,UAAU,CAAC;AACpD,EAAE,WAAW,GAAG;AAChB,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;AACvC,GAAG;AACH;;AC5DA;AACA;AACA;AACA;AACA,MAAM,CAAC,GAAG,SAAS;AACnB,EAAE,CAAC,GAAG,OAAO;AACb,EAAE,CAAC,GAAG,MAAM,CAAC;AACb;AACO,MAAM,UAAU,GAAG;AAC1B,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR,CAAC,CAAC;AACF;AACO,MAAM,QAAQ,GAAG;AACxB,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR,CAAC,CAAC;AACF;AACO,MAAM,qBAAqB,GAAG;AACrC,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AACF;AACO,MAAM,SAAS,GAAG;AACzB,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR,CAAC,CAAC;AACF;AACO,MAAM,SAAS,GAAG;AACzB,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AACF;AACO,MAAM,WAAW,GAAG;AAC3B,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,CAAC,CAAC;AACF;AACO,MAAM,iBAAiB,GAAG;AACjC,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,MAAM,EAAE,CAAC;AACX,CAAC,CAAC;AACF;AACO,MAAM,sBAAsB,GAAG;AACtC,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,YAAY,EAAE,CAAC;AACjB,CAAC,CAAC;AACF;AACO,MAAM,qBAAqB,GAAG;AACrC,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,YAAY,EAAE,CAAC;AACjB,CAAC,CAAC;AACF;AACO,MAAM,cAAc,GAAG;AAC9B,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,SAAS,EAAE,KAAK;AAClB,CAAC,CAAC;AACF;AACO,MAAM,oBAAoB,GAAG;AACpC,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,SAAS,EAAE,KAAK;AAClB,CAAC,CAAC;AACF;AACO,MAAM,yBAAyB,GAAG;AACzC,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,SAAS,EAAE,KAAK;AAClB,EAAE,YAAY,EAAE,CAAC;AACjB,CAAC,CAAC;AACF;AACO,MAAM,wBAAwB,GAAG;AACxC,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,SAAS,EAAE,KAAK;AAClB,EAAE,YAAY,EAAE,CAAC;AACjB,CAAC,CAAC;AACF;AACO,MAAM,cAAc,GAAG;AAC9B,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,CAAC,CAAC;AACF;AACO,MAAM,2BAA2B,GAAG;AAC3C,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,MAAM,EAAE,CAAC;AACX,CAAC,CAAC;AACF;AACO,MAAM,YAAY,GAAG;AAC5B,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,CAAC,CAAC;AACF;AACO,MAAM,yBAAyB,GAAG;AACzC,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,MAAM,EAAE,CAAC;AACX,CAAC,CAAC;AACF;AACO,MAAM,yBAAyB,GAAG;AACzC,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,OAAO,EAAE,CAAC;AACZ,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,CAAC,CAAC;AACF;AACO,MAAM,aAAa,GAAG;AAC7B,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,YAAY,EAAE,CAAC;AACjB,CAAC,CAAC;AACF;AACO,MAAM,0BAA0B,GAAG;AAC1C,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,YAAY,EAAE,CAAC;AACjB,CAAC,CAAC;AACF;AACO,MAAM,aAAa,GAAG;AAC7B,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,OAAO,EAAE,CAAC;AACZ,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,YAAY,EAAE,CAAC;AACjB,CAAC,CAAC;AACF;AACO,MAAM,0BAA0B,GAAG;AAC1C,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,OAAO,EAAE,CAAC;AACZ,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,YAAY,EAAE,CAAC;AACjB,CAAC;;AC7KD;AACA;AACA;AACe,MAAM,IAAI,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,MAAM,IAAI,mBAAmB,EAAE,CAAC;AACpC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,MAAM,IAAI,mBAAmB,EAAE,CAAC;AACpC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,QAAQ,GAAG;AACjB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,MAAM,IAAI,mBAAmB,EAAE,CAAC;AACpC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE;AACvB,IAAI,MAAM,IAAI,mBAAmB,EAAE,CAAC;AACpC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,EAAE,EAAE,MAAM,EAAE;AAC3B,IAAI,MAAM,IAAI,mBAAmB,EAAE,CAAC;AACpC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,EAAE,EAAE;AACb,IAAI,MAAM,IAAI,mBAAmB,EAAE,CAAC;AACpC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,MAAM,IAAI,mBAAmB,EAAE,CAAC;AACpC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,MAAM,IAAI,mBAAmB,EAAE,CAAC;AACpC,GAAG;AACH;;AC7FA,IAAIA,WAAS,GAAG,IAAI,CAAC;AACrB;AACA;AACA;AACA;AACA;AACe,MAAM,UAAU,SAAS,IAAI,CAAC;AAC7C;AACA;AACA;AACA;AACA,EAAE,WAAW,QAAQ,GAAG;AACxB,IAAI,IAAIA,WAAS,KAAK,IAAI,EAAE;AAC5B,MAAMA,WAAS,GAAG,IAAI,UAAU,EAAE,CAAC;AACnC,KAAK;AACL,IAAI,OAAOA,WAAS,CAAC;AACrB,GAAG;AACH;AACA;AACA,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG;AACH;AACA;AACA,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC;AAChE,GAAG;AACH;AACA;AACA,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;AACrC,IAAI,OAAO,aAAa,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAC7C,GAAG;AACH;AACA;AACA,EAAE,YAAY,CAAC,EAAE,EAAE,MAAM,EAAE;AAC3B,IAAI,OAAO,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AACjD,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,EAAE,EAAE;AACb,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC;AAC7C,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,SAAS,CAAC,IAAI,KAAK,QAAQ,CAAC;AACvC,GAAG;AACH;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;;ACzDA,IAAI,QAAQ,GAAG,EAAE,CAAC;AAClB,SAAS,OAAO,CAAC,IAAI,EAAE;AACvB,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACvB,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;AACtD,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,QAAQ,EAAE,IAAI;AACpB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,GAAG,EAAE,SAAS;AACpB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,GAAG,EAAE,OAAO;AAClB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;AACxB,CAAC;AACD;AACA,MAAM,SAAS,GAAG;AAClB,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,MAAM,EAAE,CAAC;AACX,CAAC,CAAC;AACF;AACA,SAAS,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE;AAChC,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;AAC3D,IAAI,MAAM,GAAG,iDAAiD,CAAC,IAAI,CAAC,SAAS,CAAC;AAC9E,IAAI,GAAG,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC;AACvE,EAAE,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AACjE,CAAC;AACD;AACA,SAAS,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE;AAChC,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAC5C,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC;AACpB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACzC,IAAI,MAAM,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AAChC;AACA,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE;AACxB,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC1B,KAAK,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;AAClC,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACxC,KAAK;AACL,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD;AACA,IAAI,aAAa,GAAG,EAAE,CAAC;AACvB;AACA;AACA;AACA;AACe,MAAM,QAAQ,SAAS,IAAI,CAAC;AAC3C;AACA;AACA;AACA;AACA,EAAE,OAAO,MAAM,CAAC,IAAI,EAAE;AACtB,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;AAC9B,MAAM,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC/C,KAAK;AACL,IAAI,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;AAC/B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,UAAU,GAAG;AACtB,IAAI,aAAa,GAAG,EAAE,CAAC;AACvB,IAAI,QAAQ,GAAG,EAAE,CAAC;AAClB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,gBAAgB,CAAC,CAAC,EAAE;AAC7B,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AAC/B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE;AAC3B,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,IAAI,IAAI;AACR,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC;AACpE,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,GAAG;AACH;AACA,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,KAAK,EAAE,CAAC;AACZ;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB;AACA,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC5C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;AACrC,IAAI,OAAO,aAAa,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACxD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,EAAE,EAAE,MAAM,EAAE;AAC3B,IAAI,OAAO,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AACjD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,EAAE,EAAE;AACb,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AAC9B;AACA,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO,GAAG,CAAC;AAChC;AACA,IAAI,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnC,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,GAAG,CAAC,aAAa;AAC5E,QAAQ,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC;AAC9B,QAAQ,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC/B;AACA,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE;AACzB,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACjC,KAAK;AACL;AACA;AACA,IAAI,MAAM,YAAY,GAAG,IAAI,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC;AAChD;AACA,IAAI,MAAM,KAAK,GAAG,YAAY,CAAC;AAC/B,MAAM,IAAI;AACV,MAAM,KAAK;AACX,MAAM,GAAG;AACT,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,WAAW,EAAE,CAAC;AACpB,KAAK,CAAC,CAAC;AACP;AACA,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC;AACrB,IAAI,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC7B,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC3C,IAAI,OAAO,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;AACxC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,SAAS,CAAC,IAAI,KAAK,MAAM,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;AACrE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC;AACtB,GAAG;AACH;;AChOA;AACA;AACA,IAAI,WAAW,GAAG,EAAE,CAAC;AACrB,SAAS,WAAW,CAAC,SAAS,EAAE,IAAI,GAAG,EAAE,EAAE;AAC3C,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;AAChD,EAAE,IAAI,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AAC7B,EAAE,IAAI,CAAC,GAAG,EAAE;AACZ,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AAC/C,IAAI,WAAW,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AAC3B,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD;AACA,IAAI,WAAW,GAAG,EAAE,CAAC;AACrB,SAAS,YAAY,CAAC,SAAS,EAAE,IAAI,GAAG,EAAE,EAAE;AAC5C,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;AAChD,EAAE,IAAI,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AAC7B,EAAE,IAAI,CAAC,GAAG,EAAE;AACZ,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AACnD,IAAI,WAAW,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AAC3B,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD;AACA,IAAI,YAAY,GAAG,EAAE,CAAC;AACtB,SAAS,YAAY,CAAC,SAAS,EAAE,IAAI,GAAG,EAAE,EAAE;AAC5C,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;AAChD,EAAE,IAAI,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;AAC9B,EAAE,IAAI,CAAC,GAAG,EAAE;AACZ,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AACjD,IAAI,YAAY,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AAC5B,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD;AACA,IAAI,YAAY,GAAG,EAAE,CAAC;AACtB,SAAS,YAAY,CAAC,SAAS,EAAE,IAAI,GAAG,EAAE,EAAE;AAC5C,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,YAAY,EAAE,GAAG,IAAI,CAAC;AACzC,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC;AACxD,EAAE,IAAI,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;AAC9B,EAAE,IAAI,CAAC,GAAG,EAAE;AACZ,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AACvD,IAAI,YAAY,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AAC5B,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD;AACA,IAAI,cAAc,GAAG,IAAI,CAAC;AAC1B,SAAS,YAAY,GAAG;AACxB,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,OAAO,cAAc,CAAC;AAC1B,GAAG,MAAM;AACT,IAAI,cAAc,GAAG,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC;AACxE,IAAI,OAAO,cAAc,CAAC;AAC1B,GAAG;AACH,CAAC;AACD;AACA,IAAI,aAAa,GAAG,EAAE,CAAC;AACvB,SAAS,iBAAiB,CAAC,SAAS,EAAE;AACtC,EAAE,IAAI,IAAI,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;AACtC,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AAC9C;AACA,IAAI,IAAI,GAAG,aAAa,IAAI,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5E,IAAI,aAAa,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;AACpC,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAE;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC1C,EAAE,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE;AACrB,IAAI,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC/C,GAAG;AACH;AACA,EAAE,MAAM,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC1C,EAAE,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE;AACrB,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC;AACvB,GAAG,MAAM;AACT,IAAI,IAAI,OAAO,CAAC;AAChB,IAAI,IAAI,WAAW,CAAC;AACpB,IAAI,IAAI;AACR,MAAM,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,eAAe,EAAE,CAAC;AAC1D,MAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,MAAM,OAAO,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACrD,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,CAAC;AACxD,MAAM,WAAW,GAAG,OAAO,CAAC;AAC5B,KAAK;AACL;AACA,IAAI,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAClD,IAAI,OAAO,CAAC,WAAW,EAAE,eAAe,EAAE,QAAQ,CAAC,CAAC;AACpD,GAAG;AACH,CAAC;AACD;AACA,SAAS,gBAAgB,CAAC,SAAS,EAAE,eAAe,EAAE,cAAc,EAAE;AACtE,EAAE,IAAI,cAAc,IAAI,eAAe,EAAE;AACzC,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AACpC,MAAM,SAAS,IAAI,IAAI,CAAC;AACxB,KAAK;AACL;AACA,IAAI,IAAI,cAAc,EAAE;AACxB,MAAM,SAAS,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC;AAC3C,KAAK;AACL;AACA,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,SAAS,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG,MAAM;AACT,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH,CAAC;AACD;AACA,SAAS,SAAS,CAAC,CAAC,EAAE;AACtB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE;AAChC,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACxC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB,GAAG;AACH,EAAE,OAAO,EAAE,CAAC;AACZ,CAAC;AACD;AACA,SAAS,WAAW,CAAC,CAAC,EAAE;AACxB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC/B,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;AAC9C,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB,GAAG;AACH,EAAE,OAAO,EAAE,CAAC;AACZ,CAAC;AACD;AACA,SAAS,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE;AACnD,EAAE,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;AACjC;AACA,EAAE,IAAI,IAAI,KAAK,OAAO,EAAE;AACxB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,MAAM,IAAI,IAAI,KAAK,IAAI,EAAE;AAC5B,IAAI,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC;AAC7B,GAAG,MAAM;AACT,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;AAC1B,GAAG;AACH,CAAC;AACD;AACA,SAAS,mBAAmB,CAAC,GAAG,EAAE;AAClC,EAAE,IAAI,GAAG,CAAC,eAAe,IAAI,GAAG,CAAC,eAAe,KAAK,MAAM,EAAE;AAC7D,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,MAAM;AACT,IAAI;AACJ,MAAM,GAAG,CAAC,eAAe,KAAK,MAAM;AACpC,MAAM,CAAC,GAAG,CAAC,MAAM;AACjB,MAAM,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;AACjC,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,CAAC,eAAe,KAAK,MAAM;AACpF,MAAM;AACN,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM,mBAAmB,CAAC;AAC1B,EAAE,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE;AACvC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;AACjC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC;AACrC;AACA,IAAI,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,SAAS,EAAE,GAAG,IAAI,CAAC;AAChD;AACA,IAAI,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AAC3D,MAAM,MAAM,QAAQ,GAAG,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,CAAC;AACvD,MAAM,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,QAAQ,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC;AACrE,MAAM,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC9C,KAAK;AACL,GAAG;AACH;AACA,EAAE,MAAM,CAAC,CAAC,EAAE;AACZ,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE;AAClB,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACnD,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACpC,KAAK,MAAM;AACX;AACA,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/D,MAAM,OAAO,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACzC,KAAK;AACL,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM,iBAAiB,CAAC;AACxB,EAAE,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;AAC9B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;AAClC;AACA,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC;AACtB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAC5B;AACA,MAAM,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACnB,KAAK,MAAM,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACzC;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;AAC9C,MAAM,MAAM,OAAO,GAAG,SAAS,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;AACtF,MAAM,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE;AAC7D,QAAQ,CAAC,GAAG,OAAO,CAAC;AACpB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACrB,OAAO,MAAM;AACb;AACA;AACA,QAAQ,CAAC,GAAG,KAAK,CAAC;AAClB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;AACxF,QAAQ,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC,IAAI,CAAC;AACpC,OAAO;AACP,KAAK,MAAM,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC1C,MAAM,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACnB,KAAK,MAAM,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;AACxC,MAAM,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACnB,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;AACvB,KAAK,MAAM;AACX;AACA;AACA,MAAM,CAAC,GAAG,KAAK,CAAC;AAChB,MAAM,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;AAC/D,MAAM,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC,IAAI,CAAC;AAClC,KAAK;AACL;AACA,IAAI,MAAM,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AACtC,IAAI,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,IAAI,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC5C,GAAG;AACH;AACA,EAAE,MAAM,GAAG;AACX,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;AAC3B;AACA;AACA,MAAM,OAAO,IAAI,CAAC,aAAa,EAAE;AACjC,SAAS,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC;AAClC,SAAS,IAAI,CAAC,EAAE,CAAC,CAAC;AAClB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC/C,GAAG;AACH;AACA,EAAE,aAAa,GAAG;AAClB,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC7D,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;AAC3B,MAAM,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK;AACjC,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,cAAc,EAAE;AAC1C,UAAU,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACtE,YAAY,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,MAAM;AAClC,YAAY,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY;AAC1C,WAAW,CAAC,CAAC;AACb,UAAU,OAAO;AACjB,YAAY,GAAG,IAAI;AACnB,YAAY,KAAK,EAAE,UAAU;AAC7B,WAAW,CAAC;AACZ,SAAS,MAAM;AACf,UAAU,OAAO,IAAI,CAAC;AACtB,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA,EAAE,eAAe,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC;AACtC,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM,gBAAgB,CAAC;AACvB,EAAE,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE;AACrC,IAAI,IAAI,CAAC,IAAI,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,CAAC;AAC3C,IAAI,IAAI,CAAC,SAAS,IAAI,WAAW,EAAE,EAAE;AACrC,MAAM,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC1C,KAAK;AACL,GAAG;AACH;AACA,EAAE,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE;AACtB,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE;AAClB,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC1C,KAAK,MAAM;AACX,MAAM,OAAOC,kBAA0B,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC;AACpG,KAAK;AACL,GAAG;AACH;AACA,EAAE,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE;AAC7B,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE;AAClB,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACjD,KAAK,MAAM;AACX,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,GAAG;AACH,CAAC;AACD;AACA,MAAM,oBAAoB,GAAG;AAC7B,EAAE,QAAQ,EAAE,CAAC;AACb,EAAE,WAAW,EAAE,CAAC;AAChB,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACjB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACe,MAAM,MAAM,CAAC;AAC5B,EAAE,OAAO,QAAQ,CAAC,IAAI,EAAE;AACxB,IAAI,OAAO,MAAM,CAAC,MAAM;AACxB,MAAM,IAAI,CAAC,MAAM;AACjB,MAAM,IAAI,CAAC,eAAe;AAC1B,MAAM,IAAI,CAAC,cAAc;AACzB,MAAM,IAAI,CAAC,YAAY;AACvB,MAAM,IAAI,CAAC,WAAW;AACtB,KAAK,CAAC;AACN,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE,WAAW,GAAG,KAAK,EAAE;AAC5F,IAAI,MAAM,eAAe,GAAG,MAAM,IAAI,QAAQ,CAAC,aAAa,CAAC;AAC7D;AACA,IAAI,MAAM,OAAO,GAAG,eAAe,KAAK,WAAW,GAAG,OAAO,GAAG,YAAY,EAAE,CAAC,CAAC;AAChF,IAAI,MAAM,gBAAgB,GAAG,eAAe,IAAI,QAAQ,CAAC,sBAAsB,CAAC;AAChF,IAAI,MAAM,eAAe,GAAG,cAAc,IAAI,QAAQ,CAAC,qBAAqB,CAAC;AAC7E,IAAI,MAAM,aAAa,GAAG,oBAAoB,CAAC,YAAY,CAAC,IAAI,QAAQ,CAAC,mBAAmB,CAAC;AAC7F,IAAI,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,gBAAgB,EAAE,eAAe,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC;AAClG,GAAG;AACH;AACA,EAAE,OAAO,UAAU,GAAG;AACtB,IAAI,cAAc,GAAG,IAAI,CAAC;AAC1B,IAAI,WAAW,GAAG,EAAE,CAAC;AACrB,IAAI,YAAY,GAAG,EAAE,CAAC;AACtB,IAAI,YAAY,GAAG,EAAE,CAAC;AACtB,GAAG;AACH;AACA,EAAE,OAAO,UAAU,CAAC,EAAE,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE,GAAG,EAAE,EAAE;AACpF,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;AAChF,GAAG;AACH;AACA,EAAE,WAAW,CAAC,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,YAAY,EAAE,eAAe,EAAE;AAChF,IAAI,MAAM,CAAC,YAAY,EAAE,qBAAqB,EAAE,oBAAoB,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAClG;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC;AAC/B,IAAI,IAAI,CAAC,eAAe,GAAG,SAAS,IAAI,qBAAqB,IAAI,IAAI,CAAC;AACtE,IAAI,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,oBAAoB,IAAI,IAAI,CAAC;AACzE,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACrC,IAAI,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;AACzF;AACA,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;AACxD,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;AACtD,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB;AACA,IAAI,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;AAC3C,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAClC,GAAG;AACH;AACA,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,EAAE;AACxC,MAAM,IAAI,CAAC,iBAAiB,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;AACzD,KAAK;AACL;AACA,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC;AAClC,GAAG;AACH;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;AAC1C,IAAI,MAAM,cAAc;AACxB,MAAM,CAAC,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,KAAK,MAAM;AACvE,OAAO,IAAI,CAAC,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,CAAC,CAAC;AAC1E,IAAI,OAAO,YAAY,IAAI,cAAc,GAAG,IAAI,GAAG,MAAM,CAAC;AAC1D,GAAG;AACH;AACA,EAAE,KAAK,CAAC,IAAI,EAAE;AACd,IAAI,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AAChE,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,MAAM;AACX,MAAM,OAAO,MAAM,CAAC,MAAM;AAC1B,QAAQ,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe;AAC3C,QAAQ,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe;AACpD,QAAQ,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc;AAClD,QAAQ,oBAAoB,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,YAAY;AACpE,QAAQ,IAAI,CAAC,WAAW,IAAI,KAAK;AACjC,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH;AACA,EAAE,aAAa,CAAC,IAAI,GAAG,EAAE,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;AACtD,GAAG;AACH;AACA,EAAE,iBAAiB,CAAC,IAAI,GAAG,EAAE,EAAE;AAC/B,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC;AACvD,GAAG;AACH;AACA,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,EAAE;AACjC,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,MAAM,EAAEC,MAAc,EAAE,MAAM;AACzD,MAAM,MAAM,IAAI,GAAG,MAAM,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;AACjF,QAAQ,SAAS,GAAG,MAAM,GAAG,QAAQ,GAAG,YAAY,CAAC;AACrD,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,EAAE;AAChD,QAAQ,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;AACjG,OAAO;AACP,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC;AACjD,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,EAAE;AACnC,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,MAAM,EAAEC,QAAgB,EAAE,MAAM;AAC3D,MAAM,MAAM,IAAI,GAAG,MAAM;AACzB,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE;AAC/E,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE;AAC/B,QAAQ,SAAS,GAAG,MAAM,GAAG,QAAQ,GAAG,YAAY,CAAC;AACrD,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,EAAE;AAClD,QAAQ,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,CAAC,EAAE;AAC/D,UAAU,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC;AAC3C,SAAS,CAAC;AACV,OAAO;AACP,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC;AACnD,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA,EAAE,SAAS,GAAG;AACd,IAAI,OAAO,SAAS;AACpB,MAAM,IAAI;AACV,MAAM,SAAS;AACf,MAAM,MAAMC,SAAiB;AAC7B,MAAM,MAAM;AACZ;AACA;AACA,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACjC,UAAU,MAAM,IAAI,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;AAC7D,UAAU,IAAI,CAAC,aAAa,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG;AAClG,YAAY,CAAC,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,WAAW,CAAC;AACvD,WAAW,CAAC;AACZ,SAAS;AACT;AACA,QAAQ,OAAO,IAAI,CAAC,aAAa,CAAC;AAClC,OAAO;AACP,KAAK,CAAC;AACN,GAAG;AACH;AACA,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,MAAM,EAAEC,IAAY,EAAE,MAAM;AACvD,MAAM,MAAM,IAAI,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC;AACnC;AACA;AACA;AACA,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAClC,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;AAC3F,UAAU,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC;AACvC,SAAS,CAAC;AACV,OAAO;AACP;AACA,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACnC,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA,EAAE,OAAO,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;AAC/B,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,QAAQ,CAAC;AAC7C,MAAM,OAAO,GAAG,EAAE,CAAC,aAAa,EAAE;AAClC,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,CAAC;AACrE,IAAI,OAAO,QAAQ,GAAG,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;AAC5C,GAAG;AACH;AACA,EAAE,eAAe,CAAC,IAAI,GAAG,EAAE,EAAE;AAC7B;AACA;AACA,IAAI,OAAO,IAAI,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AAC1F,GAAG;AACH;AACA,EAAE,WAAW,CAAC,EAAE,EAAE,QAAQ,GAAG,EAAE,EAAE;AACjC,IAAI,OAAO,IAAI,iBAAiB,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1D,GAAG;AACH;AACA,EAAE,YAAY,CAAC,IAAI,GAAG,EAAE,EAAE;AAC1B,IAAI,OAAO,IAAI,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,CAAC;AACnE,GAAG;AACH;AACA,EAAE,aAAa,CAAC,IAAI,GAAG,EAAE,EAAE;AAC3B,IAAI,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACxC,GAAG;AACH;AACA,EAAE,SAAS,GAAG;AACd,IAAI;AACJ,MAAM,IAAI,CAAC,MAAM,KAAK,IAAI;AAC1B,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,OAAO;AAC3C,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC;AACrF,MAAM;AACN,GAAG;AACH;AACA,EAAE,eAAe,GAAG;AACpB,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;AAC3B,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;AAC/B,KAAK,MAAM,IAAI,CAAC,iBAAiB,EAAE,EAAE;AACrC,MAAM,OAAO,oBAAoB,CAAC;AAClC,KAAK,MAAM;AACX,MAAM,OAAO,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5C,KAAK;AACL,GAAG;AACH;AACA,EAAE,cAAc,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC;AAC3C,GAAG;AACH;AACA,EAAE,qBAAqB,GAAG;AAC1B,IAAI,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,WAAW,CAAC;AAC9C,GAAG;AACH;AACA,EAAE,cAAc,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC;AAC1C,GAAG;AACH;AACA,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI;AACJ,MAAM,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM;AAClC,MAAM,IAAI,CAAC,eAAe,KAAK,KAAK,CAAC,eAAe;AACpD,MAAM,IAAI,CAAC,cAAc,KAAK,KAAK,CAAC,cAAc;AAClD,MAAM;AACN,GAAG;AACH;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AACrF,GAAG;AACH;;AC9hBA,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB;AACA;AACA;AACA;AACA;AACe,MAAM,eAAe,SAAS,IAAI,CAAC;AAClD;AACA;AACA;AACA;AACA,EAAE,WAAW,WAAW,GAAG;AAC3B,IAAI,IAAI,SAAS,KAAK,IAAI,EAAE;AAC5B,MAAM,SAAS,GAAG,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC;AACzC,KAAK;AACL,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,QAAQ,CAAC,MAAM,EAAE;AAC1B,IAAI,OAAO,MAAM,KAAK,CAAC,GAAG,eAAe,CAAC,WAAW,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;AACpF,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,cAAc,CAAC,CAAC,EAAE;AAC3B,IAAI,IAAI,CAAC,EAAE;AACX,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;AACjE,MAAM,IAAI,CAAC,EAAE;AACb,QAAQ,OAAO,IAAI,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB,IAAI,KAAK,EAAE,CAAC;AACZ;AACA,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;AACxB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AACjF,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,QAAQ,GAAG;AACjB,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;AAC1B,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC7D,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,EAAE,EAAE,MAAM,EAAE;AAC3B,IAAI,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC5C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,SAAS,CAAC,IAAI,KAAK,OAAO,IAAI,SAAS,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC;AACxE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;;ACnJA;AACA;AACA;AACA;AACe,MAAM,WAAW,SAAS,IAAI,CAAC;AAC9C,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,EAAE,CAAC;AACZ;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,GAAG;AACH;AACA;AACA,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH;AACA;AACA,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC;AACzB,GAAG;AACH;AACA;AACA,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA,EAAE,UAAU,GAAG;AACf,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA,EAAE,YAAY,GAAG;AACjB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;;ACpDA;AACA;AACA;AASA;AACO,SAAS,aAAa,CAAC,KAAK,EAAE,WAAW,EAAE;AAElD,EAAE,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE;AAC5C,IAAI,OAAO,WAAW,CAAC;AACvB,GAAG,MAAM,IAAI,KAAK,YAAY,IAAI,EAAE;AACpC,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC9B,IAAI,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;AACxC,IAAI,IAAI,OAAO,KAAK,SAAS,EAAE,OAAO,WAAW,CAAC;AAClD,SAAS,IAAI,OAAO,KAAK,OAAO,IAAI,OAAO,KAAK,QAAQ,EAAE,OAAO,UAAU,CAAC,QAAQ,CAAC;AACrF,SAAS,IAAI,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,EAAE,OAAO,eAAe,CAAC,WAAW,CAAC;AACxF,SAAS,OAAO,eAAe,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAClF,GAAG,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC9B,IAAI,OAAO,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC3C,GAAG,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAI,KAAK,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU,EAAE;AACnG;AACA;AACA,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,MAAM;AACT,IAAI,OAAO,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;AAClC,GAAG;AACH;;ACjCA,MAAM,gBAAgB,GAAG;AACzB,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,OAAO,EAAE,iBAAiB;AAC5B,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,QAAQ,EAAE,iBAAiB;AAC7B,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,OAAO,EAAE,uBAAuB;AAClC,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,OAAO,EAAE,iBAAiB;AAC5B,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,IAAI,EAAE,KAAK;AACb,CAAC,CAAC;AACF;AACA,MAAM,qBAAqB,GAAG;AAC9B,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACvB,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;AAC1B,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACvB,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACpB,CAAC,CAAC;AACF;AACA,MAAM,YAAY,GAAG,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAChF;AACO,SAAS,WAAW,CAAC,GAAG,EAAE;AACjC,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AAChC,EAAE,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;AACpB,IAAI,KAAK,GAAG,EAAE,CAAC;AACf,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,MAAM,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACrC;AACA,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;AAC1D,QAAQ,KAAK,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,OAAO,MAAM;AACb,QAAQ,KAAK,MAAM,GAAG,IAAI,qBAAqB,EAAE;AACjD,UAAU,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC;AACxD,UAAU,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,EAAE;AAC1C,YAAY,KAAK,IAAI,IAAI,GAAG,GAAG,CAAC;AAChC,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAC/B,GAAG,MAAM;AACT,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,CAAC;AACD;AACA;AACA,IAAI,eAAe,GAAG,EAAE,CAAC;AAClB,SAAS,oBAAoB,GAAG;AACvC,EAAE,eAAe,GAAG,EAAE,CAAC;AACvB,CAAC;AACD;AACO,SAAS,UAAU,CAAC,EAAE,eAAe,EAAE,EAAE,MAAM,GAAG,EAAE,EAAE;AAC7D,EAAE,MAAM,EAAE,GAAG,eAAe,IAAI,MAAM,CAAC;AACvC;AACA,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE;AAC5B,IAAI,eAAe,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;AAC7B,GAAG;AACH,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE;AACpC,IAAI,eAAe,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;AACjF,GAAG;AACH;AACA,EAAE,OAAO,eAAe,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;AACrC;;AChFA,IAAI,GAAG,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE;AAC1B,EAAE,WAAW,GAAG,QAAQ;AACxB,EAAE,aAAa,GAAG,IAAI;AACtB,EAAE,sBAAsB,GAAG,IAAI;AAC/B,EAAE,qBAAqB,GAAG,IAAI;AAC9B,EAAE,kBAAkB,GAAG,EAAE;AACzB,EAAE,cAAc;AAChB,EAAE,mBAAmB,GAAG,IAAI,CAAC;AAC7B;AACA;AACA;AACA;AACe,MAAM,QAAQ,CAAC;AAC9B;AACA;AACA;AACA;AACA,EAAE,WAAW,GAAG,GAAG;AACnB,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,GAAG,CAAC,CAAC,EAAE;AACpB,IAAI,GAAG,GAAG,CAAC,CAAC;AACZ,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,WAAW,CAAC,IAAI,EAAE;AAC/B,IAAI,WAAW,GAAG,IAAI,CAAC;AACvB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,WAAW,GAAG;AAC3B,IAAI,OAAO,aAAa,CAAC,WAAW,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC3D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,aAAa,GAAG;AAC7B,IAAI,OAAO,aAAa,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,aAAa,CAAC,MAAM,EAAE;AACnC,IAAI,aAAa,GAAG,MAAM,CAAC;AAC3B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,sBAAsB,GAAG;AACtC,IAAI,OAAO,sBAAsB,CAAC;AAClC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,sBAAsB,CAAC,eAAe,EAAE;AACrD,IAAI,sBAAsB,GAAG,eAAe,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,qBAAqB,GAAG;AACrC,IAAI,OAAO,qBAAqB,CAAC;AACjC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,qBAAqB,CAAC,cAAc,EAAE;AACnD,IAAI,qBAAqB,GAAG,cAAc,CAAC;AAC3C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,mBAAmB,GAAG;AACnC,IAAI,OAAO,mBAAmB,CAAC;AAC/B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,mBAAmB,CAAC,YAAY,EAAE;AAC/C,IAAI,mBAAmB,GAAG,oBAAoB,CAAC,YAAY,CAAC,CAAC;AAC7D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,kBAAkB,GAAG;AAClC,IAAI,OAAO,kBAAkB,CAAC;AAC9B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,kBAAkB,CAAC,UAAU,EAAE;AAC5C,IAAI,kBAAkB,GAAG,UAAU,GAAG,GAAG,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,cAAc,GAAG;AAC9B,IAAI,OAAO,cAAc,CAAC;AAC1B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,cAAc,CAAC,CAAC,EAAE;AAC/B,IAAI,cAAc,GAAG,CAAC,CAAC;AACvB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,WAAW,GAAG;AACvB,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;AACxB,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;AAC1B,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;AAC1B,IAAI,oBAAoB,EAAE,CAAC;AAC3B,GAAG;AACH;;ACnLe,MAAM,OAAO,CAAC;AAC7B,EAAE,WAAW,CAAC,MAAM,EAAE,WAAW,EAAE;AACnC,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AACnC,GAAG;AACH;AACA,EAAE,SAAS,GAAG;AACd,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;AAC1B,MAAM,OAAO,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;AACnD,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC;AACzB,KAAK;AACL,GAAG;AACH;;ACAA,MAAM,aAAa,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7E,EAAE,UAAU,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACvE;AACA,SAAS,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE;AACrC,EAAE,OAAO,IAAI,OAAO;AACpB,IAAI,mBAAmB;AACvB,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,UAAU,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC;AACrF,GAAG,CAAC;AACJ,CAAC;AACD;AACO,SAAS,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE;AAC5C,EAAE,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AACrD;AACA,EAAE,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC,EAAE;AAC/B,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,EAAE,GAAG,IAAI,CAAC,CAAC;AAChD,GAAG;AACH;AACA,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC;AAC3B;AACA,EAAE,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AAC3B,CAAC;AACD;AACA,SAAS,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE;AAC1C,EAAE,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG,aAAa,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AAC1E,CAAC;AACD;AACA,SAAS,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE;AACzC,EAAE,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG,aAAa;AAC7D,IAAI,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;AAChD,IAAI,GAAG,GAAG,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AAClC,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;AACpC,CAAC;AACD;AACO,SAAS,iBAAiB,CAAC,UAAU,EAAE,WAAW,EAAE;AAC3D,EAAE,OAAO,CAAC,CAAC,UAAU,GAAG,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClD,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe,CAAC,OAAO,EAAE,kBAAkB,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,EAAE;AAClF,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,OAAO;AACtC,IAAI,OAAO,GAAG,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;AAC9C,IAAI,OAAO,GAAG,iBAAiB,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC;AAC1E;AACA,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,OAAO,GAAG,EAAE,GAAG,kBAAkB,IAAI,CAAC,CAAC;AAChF,IAAI,QAAQ,CAAC;AACb;AACA,EAAE,IAAI,UAAU,GAAG,CAAC,EAAE;AACtB,IAAI,QAAQ,GAAG,IAAI,GAAG,CAAC,CAAC;AACxB,IAAI,UAAU,GAAG,eAAe,CAAC,QAAQ,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC;AAC5E,GAAG,MAAM,IAAI,UAAU,GAAG,eAAe,CAAC,IAAI,EAAE,kBAAkB,EAAE,WAAW,CAAC,EAAE;AAClF,IAAI,QAAQ,GAAG,IAAI,GAAG,CAAC,CAAC;AACxB,IAAI,UAAU,GAAG,CAAC,CAAC;AACnB,GAAG,MAAM;AACT,IAAI,QAAQ,GAAG,IAAI,CAAC;AACpB,GAAG;AACH;AACA,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;AACnE,CAAC;AACD;AACO,SAAS,eAAe,CAAC,QAAQ,EAAE,kBAAkB,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,EAAE;AACnF,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,QAAQ;AACpD,IAAI,aAAa,GAAG,iBAAiB,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,EAAE,kBAAkB,CAAC,EAAE,WAAW,CAAC;AAC9F,IAAI,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;AACtC;AACA,EAAE,IAAI,OAAO,GAAG,UAAU,GAAG,CAAC,GAAG,OAAO,GAAG,aAAa,GAAG,CAAC,GAAG,kBAAkB;AACjF,IAAI,IAAI,CAAC;AACT;AACA,EAAE,IAAI,OAAO,GAAG,CAAC,EAAE;AACnB,IAAI,IAAI,GAAG,QAAQ,GAAG,CAAC,CAAC;AACxB,IAAI,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AAChC,GAAG,MAAM,IAAI,OAAO,GAAG,UAAU,EAAE;AACnC,IAAI,IAAI,GAAG,QAAQ,GAAG,CAAC,CAAC;AACxB,IAAI,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;AACpC,GAAG,MAAM;AACT,IAAI,IAAI,GAAG,QAAQ,CAAC;AACpB,GAAG;AACH;AACA,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACzD,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;AACvD,CAAC;AACD;AACO,SAAS,kBAAkB,CAAC,QAAQ,EAAE;AAC7C,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,QAAQ,CAAC;AACxC,EAAE,MAAM,OAAO,GAAG,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;AACnD,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;AACpD,CAAC;AACD;AACO,SAAS,kBAAkB,CAAC,WAAW,EAAE;AAChD,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,WAAW,CAAC;AACxC,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACzD,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;AAC1D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE;AAC9C,EAAE,MAAM,iBAAiB;AACzB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC;AAClC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC;AACrC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AACpC,EAAE,IAAI,iBAAiB,EAAE;AACzB,IAAI,MAAM,cAAc;AACxB,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC9F;AACA,IAAI,IAAI,cAAc,EAAE;AACxB,MAAM,MAAM,IAAI,6BAA6B;AAC7C,QAAQ,gEAAgE;AACxE,OAAO,CAAC;AACR,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC;AACvE,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,eAAe,CAAC;AAChF,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,aAAa,CAAC;AAC1E,IAAI,OAAO,GAAG,CAAC,YAAY,CAAC;AAC5B,IAAI,OAAO,GAAG,CAAC,eAAe,CAAC;AAC/B,IAAI,OAAO,GAAG,CAAC,aAAa,CAAC;AAC7B,IAAI,OAAO;AACX,MAAM,kBAAkB,EAAE,GAAG,CAAC,qBAAqB,EAAE;AACrD,MAAM,WAAW,EAAE,GAAG,CAAC,cAAc,EAAE;AACvC,KAAK,CAAC;AACN,GAAG,MAAM;AACT,IAAI,OAAO,EAAE,kBAAkB,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC;AACrD,GAAG;AACH,CAAC;AACD;AACO,SAAS,kBAAkB,CAAC,GAAG,EAAE,kBAAkB,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,EAAE;AACjF,EAAE,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;AAC3C,IAAI,SAAS,GAAG,cAAc;AAC9B,MAAM,GAAG,CAAC,UAAU;AACpB,MAAM,CAAC;AACP,MAAM,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,kBAAkB,EAAE,WAAW,CAAC;AACpE,KAAK;AACL,IAAI,YAAY,GAAG,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrD;AACA,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,OAAO,cAAc,CAAC,UAAU,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;AACpD,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE;AACzB,IAAI,OAAO,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AAClD,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE;AAC5B,IAAI,OAAO,cAAc,CAAC,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;AAClD,GAAG,MAAM,OAAO,KAAK,CAAC;AACtB,CAAC;AACD;AACO,SAAS,qBAAqB,CAAC,GAAG,EAAE;AAC3C,EAAE,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;AACvC,IAAI,YAAY,GAAG,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AACxE;AACA,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,OAAO,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AAC5C,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE;AAC5B,IAAI,OAAO,cAAc,CAAC,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;AAClD,GAAG,MAAM,OAAO,KAAK,CAAC;AACtB,CAAC;AACD;AACO,SAAS,uBAAuB,CAAC,GAAG,EAAE;AAC7C,EAAE,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;AACvC,IAAI,UAAU,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;AACjD,IAAI,QAAQ,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AAC5E;AACA,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,OAAO,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AAC5C,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE;AAC1B,IAAI,OAAO,cAAc,CAAC,OAAO,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9C,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE;AACxB,IAAI,OAAO,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1C,GAAG,MAAM,OAAO,KAAK,CAAC;AACtB,CAAC;AACD;AACO,SAAS,kBAAkB,CAAC,GAAG,EAAE;AACxC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC;AACpD,EAAE,MAAM,SAAS;AACjB,MAAM,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;AACjC,OAAO,IAAI,KAAK,EAAE,IAAI,MAAM,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,IAAI,WAAW,KAAK,CAAC,CAAC;AACxE,IAAI,WAAW,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;AAC/C,IAAI,WAAW,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;AAC/C,IAAI,gBAAgB,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AAC3D;AACA,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,OAAO,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACxC,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE;AAC3B,IAAI,OAAO,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC5C,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE;AAC3B,IAAI,OAAO,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC5C,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE;AAChC,IAAI,OAAO,cAAc,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;AACtD,GAAG,MAAM,OAAO,KAAK,CAAC;AACtB;;AC7MA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,CAAC,CAAC,EAAE;AAC/B,EAAE,OAAO,OAAO,CAAC,KAAK,WAAW,CAAC;AAClC,CAAC;AACD;AACO,SAAS,QAAQ,CAAC,CAAC,EAAE;AAC5B,EAAE,OAAO,OAAO,CAAC,KAAK,QAAQ,CAAC;AAC/B,CAAC;AACD;AACO,SAAS,SAAS,CAAC,CAAC,EAAE;AAC7B,EAAE,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9C,CAAC;AACD;AACO,SAAS,QAAQ,CAAC,CAAC,EAAE;AAC5B,EAAE,OAAO,OAAO,CAAC,KAAK,QAAQ,CAAC;AAC/B,CAAC;AACD;AACO,SAAS,MAAM,CAAC,CAAC,EAAE;AAC1B,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,eAAe,CAAC;AAC/D,CAAC;AACD;AACA;AACA;AACO,SAAS,WAAW,GAAG;AAC9B,EAAE,IAAI;AACN,IAAI,OAAO,OAAO,IAAI,KAAK,WAAW,IAAI,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC;AACpE,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,CAAC;AACD;AACO,SAAS,iBAAiB,GAAG;AACpC,EAAE,IAAI;AACN,IAAI;AACJ,MAAM,OAAO,IAAI,KAAK,WAAW;AACjC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM;AACnB,OAAO,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;AACrF,MAAM;AACN,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACO,SAAS,UAAU,CAAC,KAAK,EAAE;AAClC,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;AAChD,CAAC;AACD;AACO,SAAS,MAAM,CAAC,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE;AACzC,EAAE,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;AACxB,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH,EAAE,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,KAAK;AACpC,IAAI,MAAM,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;AACtD,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC;AACD;AACO,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE;AAChC,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAC/B,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAClB,IAAI,OAAO,CAAC,CAAC;AACb,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AACD;AACO,SAAS,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE;AAC1C,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACzD,CAAC;AACD;AACO,SAAS,oBAAoB,CAAC,QAAQ,EAAE;AAC/C,EAAE,IAAI,QAAQ,IAAI,IAAI,EAAE;AACxB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,MAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;AAC3C,IAAI,MAAM,IAAI,oBAAoB,CAAC,iCAAiC,CAAC,CAAC;AACtE,GAAG,MAAM;AACT,IAAI;AACJ,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9C,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;AACjD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;AACtC,MAAM,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5D,MAAM;AACN,MAAM,MAAM,IAAI,oBAAoB,CAAC,uBAAuB,CAAC,CAAC;AAC9D,KAAK;AACL,IAAI,OAAO;AACX,MAAM,QAAQ,EAAE,QAAQ,CAAC,QAAQ;AACjC,MAAM,WAAW,EAAE,QAAQ,CAAC,WAAW;AACvC,MAAM,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AAC3C,KAAK,CAAC;AACN,GAAG;AACH,CAAC;AACD;AACA;AACA;AACO,SAAS,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE;AACnD,EAAE,OAAO,SAAS,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,GAAG,CAAC;AAC7D,CAAC;AACD;AACA;AACO,SAAS,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;AAC/B,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACnC,CAAC;AACD;AACO,SAAS,QAAQ,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE;AACvC,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;AAC1B,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAClD,GAAG,MAAM;AACT,IAAI,MAAM,GAAG,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC3C,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD;AACO,SAAS,YAAY,CAAC,MAAM,EAAE;AACrC,EAAE,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,EAAE,EAAE;AAC/D,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG,MAAM;AACT,IAAI,OAAO,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AAChC,GAAG;AACH,CAAC;AACD;AACO,SAAS,aAAa,CAAC,MAAM,EAAE;AACtC,EAAE,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,EAAE,EAAE;AAC/D,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG,MAAM;AACT,IAAI,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC;AAC9B,GAAG;AACH,CAAC;AACD;AACO,SAAS,WAAW,CAAC,QAAQ,EAAE;AACtC;AACA,EAAE,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,EAAE,EAAE;AACrE,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG,MAAM;AACT,IAAI,MAAM,CAAC,GAAG,UAAU,CAAC,IAAI,GAAG,QAAQ,CAAC,GAAG,IAAI,CAAC;AACjD,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACzB,GAAG;AACH,CAAC;AACD;AACO,SAAS,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,GAAG,KAAK,EAAE;AAC5D,EAAE,MAAM,MAAM,GAAG,EAAE,IAAI,MAAM;AAC7B,IAAI,OAAO,GAAG,UAAU,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACnD,EAAE,OAAO,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC;AAC3C,CAAC;AACD;AACA;AACA;AACO,SAAS,UAAU,CAAC,IAAI,EAAE;AACjC,EAAE,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;AAClE,CAAC;AACD;AACO,SAAS,UAAU,CAAC,IAAI,EAAE;AACjC,EAAE,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACtC,CAAC;AACD;AACO,SAAS,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE;AACzC,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;AAC9C,IAAI,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,GAAG,QAAQ,IAAI,EAAE,CAAC;AAC7C;AACA,EAAE,IAAI,QAAQ,KAAK,CAAC,EAAE;AACtB,IAAI,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AACzC,GAAG,MAAM;AACT,IAAI,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AAC5E,GAAG;AACH,CAAC;AACD;AACA;AACO,SAAS,YAAY,CAAC,GAAG,EAAE;AAClC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG;AAClB,IAAI,GAAG,CAAC,IAAI;AACZ,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC;AACjB,IAAI,GAAG,CAAC,GAAG;AACX,IAAI,GAAG,CAAC,IAAI;AACZ,IAAI,GAAG,CAAC,MAAM;AACd,IAAI,GAAG,CAAC,MAAM;AACd,IAAI,GAAG,CAAC,WAAW;AACnB,GAAG,CAAC;AACJ;AACA;AACA,EAAE,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,EAAE;AACvC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB;AACA;AACA;AACA,IAAI,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AACvD,GAAG;AACH,EAAE,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;AACD;AACA;AACA,SAAS,eAAe,CAAC,IAAI,EAAE,kBAAkB,EAAE,WAAW,EAAE;AAChE,EAAE,MAAM,KAAK,GAAG,iBAAiB,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,EAAE,kBAAkB,CAAC,EAAE,WAAW,CAAC,CAAC;AACvF,EAAE,OAAO,CAAC,KAAK,GAAG,kBAAkB,GAAG,CAAC,CAAC;AACzC,CAAC;AACD;AACO,SAAS,eAAe,CAAC,QAAQ,EAAE,kBAAkB,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,EAAE;AACnF,EAAE,MAAM,UAAU,GAAG,eAAe,CAAC,QAAQ,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC;AAChF,EAAE,MAAM,cAAc,GAAG,eAAe,CAAC,QAAQ,GAAG,CAAC,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC;AACxF,EAAE,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,UAAU,GAAG,cAAc,IAAI,CAAC,CAAC;AAClE,CAAC;AACD;AACO,SAAS,cAAc,CAAC,IAAI,EAAE;AACrC,EAAE,IAAI,IAAI,GAAG,EAAE,EAAE;AACjB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,MAAM,OAAO,IAAI,GAAG,QAAQ,CAAC,kBAAkB,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC/E,CAAC;AACD;AACA;AACA;AACO,SAAS,aAAa,CAAC,EAAE,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,GAAG,IAAI,EAAE;AACzE,EAAE,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC;AAC3B,IAAI,QAAQ,GAAG;AACf,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,GAAG,EAAE,SAAS;AACpB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,MAAM,EAAE,SAAS;AACvB,KAAK,CAAC;AACN;AACA,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC,GAAG;AACH;AACA,EAAE,MAAM,QAAQ,GAAG,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,QAAQ,EAAE,CAAC;AAC/D;AACA,EAAE,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,QAAQ,CAAC;AAC1D,KAAK,aAAa,CAAC,IAAI,CAAC;AACxB,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,cAAc,CAAC,CAAC;AAC1D,EAAE,OAAO,MAAM,GAAG,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;AACtC,CAAC;AACD;AACA;AACO,SAAS,YAAY,CAAC,UAAU,EAAE,YAAY,EAAE;AACvD,EAAE,IAAI,OAAO,GAAG,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;AACzC;AACA;AACA,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;AAC7B,IAAI,OAAO,GAAG,CAAC,CAAC;AAChB,GAAG;AACH;AACA,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC;AAChD,IAAI,YAAY,GAAG,OAAO,GAAG,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC;AAC5E,EAAE,OAAO,OAAO,GAAG,EAAE,GAAG,YAAY,CAAC;AACrC,CAAC;AACD;AACA;AACA;AACO,SAAS,QAAQ,CAAC,KAAK,EAAE;AAChC,EAAE,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AACrC,EAAE,IAAI,OAAO,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC;AAC9E,IAAI,MAAM,IAAI,oBAAoB,CAAC,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AAClE,EAAE,OAAO,YAAY,CAAC;AACtB,CAAC;AACD;AACO,SAAS,eAAe,CAAC,GAAG,EAAE,UAAU,EAAE;AACjD,EAAE,MAAM,UAAU,GAAG,EAAE,CAAC;AACxB,EAAE,KAAK,MAAM,CAAC,IAAI,GAAG,EAAE;AACvB,IAAI,IAAI,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE;AAChC,MAAM,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB,MAAM,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE,SAAS;AAClD,MAAM,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC9C,KAAK;AACL,GAAG;AACH,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE;AAC7C,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;AACjD,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;AAC/C,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACnC;AACA,EAAE,QAAQ,MAAM;AAChB,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,IAAI,KAAK,QAAQ;AACjB,MAAM,OAAO,CAAC,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAClE,IAAI,KAAK,QAAQ;AACjB,MAAM,OAAO,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,IAAI;AACJ,MAAM,MAAM,IAAI,UAAU,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAAC;AACzF,GAAG;AACH,CAAC;AACD;AACO,SAAS,UAAU,CAAC,GAAG,EAAE;AAChC,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC;AAChE;;ACpTA;AACA;AACA;AACA;AACO,MAAM,UAAU,GAAG;AAC1B,EAAE,SAAS;AACX,EAAE,UAAU;AACZ,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,KAAK;AACP,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,QAAQ;AACV,EAAE,WAAW;AACb,EAAE,SAAS;AACX,EAAE,UAAU;AACZ,EAAE,UAAU;AACZ,CAAC,CAAC;AACF;AACO,MAAM,WAAW,GAAG;AAC3B,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,CAAC,CAAC;AACF;AACO,MAAM,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzF;AACO,SAAS,MAAM,CAAC,MAAM,EAAE;AAC/B,EAAE,QAAQ,MAAM;AAChB,IAAI,KAAK,QAAQ;AACjB,MAAM,OAAO,CAAC,GAAG,YAAY,CAAC,CAAC;AAC/B,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,CAAC,GAAG,WAAW,CAAC,CAAC;AAC9B,IAAI,KAAK,MAAM;AACf,MAAM,OAAO,CAAC,GAAG,UAAU,CAAC,CAAC;AAC7B,IAAI,KAAK,SAAS;AAClB,MAAM,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC7E,IAAI,KAAK,SAAS;AAClB,MAAM,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACtF,IAAI;AACJ,MAAM,OAAO,IAAI,CAAC;AAClB,GAAG;AACH,CAAC;AACD;AACO,MAAM,YAAY,GAAG;AAC5B,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,WAAW;AACb,EAAE,UAAU;AACZ,EAAE,QAAQ;AACV,EAAE,UAAU;AACZ,EAAE,QAAQ;AACV,CAAC,CAAC;AACF;AACO,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AAC/E;AACO,MAAM,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAClE;AACO,SAAS,QAAQ,CAAC,MAAM,EAAE;AACjC,EAAE,QAAQ,MAAM;AAChB,IAAI,KAAK,QAAQ;AACjB,MAAM,OAAO,CAAC,GAAG,cAAc,CAAC,CAAC;AACjC,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,CAAC,GAAG,aAAa,CAAC,CAAC;AAChC,IAAI,KAAK,MAAM;AACf,MAAM,OAAO,CAAC,GAAG,YAAY,CAAC,CAAC;AAC/B,IAAI,KAAK,SAAS;AAClB,MAAM,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACjD,IAAI;AACJ,MAAM,OAAO,IAAI,CAAC;AAClB,GAAG;AACH,CAAC;AACD;AACO,MAAM,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACtC;AACO,MAAM,QAAQ,GAAG,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;AACzD;AACO,MAAM,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACtC;AACO,MAAM,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACrC;AACO,SAAS,IAAI,CAAC,MAAM,EAAE;AAC7B,EAAE,QAAQ,MAAM;AAChB,IAAI,KAAK,QAAQ;AACjB,MAAM,OAAO,CAAC,GAAG,UAAU,CAAC,CAAC;AAC7B,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,CAAC,GAAG,SAAS,CAAC,CAAC;AAC5B,IAAI,KAAK,MAAM;AACf,MAAM,OAAO,CAAC,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAI;AACJ,MAAM,OAAO,IAAI,CAAC;AAClB,GAAG;AACH,CAAC;AACD;AACO,SAAS,mBAAmB,CAAC,EAAE,EAAE;AACxC,EAAE,OAAO,SAAS,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACzC,CAAC;AACD;AACO,SAAS,kBAAkB,CAAC,EAAE,EAAE,MAAM,EAAE;AAC/C,EAAE,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;AAC1C,CAAC;AACD;AACO,SAAS,gBAAgB,CAAC,EAAE,EAAE,MAAM,EAAE;AAC7C,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACtC,CAAC;AACD;AACO,SAAS,cAAc,CAAC,EAAE,EAAE,MAAM,EAAE;AAC3C,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3C,CAAC;AACD;AACO,SAAS,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,GAAG,QAAQ,EAAE,MAAM,GAAG,KAAK,EAAE;AACpF,EAAE,MAAM,KAAK,GAAG;AAChB,IAAI,KAAK,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;AAC1B,IAAI,QAAQ,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;AACjC,IAAI,MAAM,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC;AAC5B,IAAI,KAAK,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;AAC1B,IAAI,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;AAChC,IAAI,KAAK,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;AAC1B,IAAI,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;AAC/B,IAAI,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;AAC/B,GAAG,CAAC;AACJ;AACA,EAAE,MAAM,QAAQ,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACxE;AACA,EAAE,IAAI,OAAO,KAAK,MAAM,IAAI,QAAQ,EAAE;AACtC,IAAI,MAAM,KAAK,GAAG,IAAI,KAAK,MAAM,CAAC;AAClC,IAAI,QAAQ,KAAK;AACjB,MAAM,KAAK,CAAC;AACZ,QAAQ,OAAO,KAAK,GAAG,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,MAAM,KAAK,CAAC,CAAC;AACb,QAAQ,OAAO,KAAK,GAAG,WAAW,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,MAAM,KAAK,CAAC;AACZ,QAAQ,OAAO,KAAK,GAAG,OAAO,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAE1D,KAAK;AACL,GAAG;AACH;AACA,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC;AACpD,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;AAC9B,IAAI,QAAQ,GAAG,QAAQ,KAAK,CAAC;AAC7B,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;AAC1B,IAAI,OAAO,GAAG,MAAM;AACpB,QAAQ,QAAQ;AAChB,UAAU,QAAQ,CAAC,CAAC,CAAC;AACrB,UAAU,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC;AACpC,QAAQ,QAAQ;AAChB,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtB,QAAQ,IAAI,CAAC;AACb,EAAE,OAAO,QAAQ,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;AAC/E;;ACjKA,SAAS,eAAe,CAAC,MAAM,EAAE,aAAa,EAAE;AAChD,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;AACb,EAAE,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;AAC9B,IAAI,IAAI,KAAK,CAAC,OAAO,EAAE;AACvB,MAAM,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC;AACrB,KAAK,MAAM;AACX,MAAM,CAAC,IAAI,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACpC,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,CAAC;AACX,CAAC;AACD;AACA,MAAM,sBAAsB,GAAG;AAC/B,EAAE,CAAC,EAAEC,UAAkB;AACvB,EAAE,EAAE,EAAEC,QAAgB;AACtB,EAAE,GAAG,EAAEC,SAAiB;AACxB,EAAE,IAAI,EAAEC,SAAiB;AACzB,EAAE,CAAC,EAAEC,WAAmB;AACxB,EAAE,EAAE,EAAEC,iBAAyB;AAC/B,EAAE,GAAG,EAAEC,sBAA8B;AACrC,EAAE,IAAI,EAAEC,qBAA6B;AACrC,EAAE,CAAC,EAAEC,cAAsB;AAC3B,EAAE,EAAE,EAAEC,oBAA4B;AAClC,EAAE,GAAG,EAAEC,yBAAiC;AACxC,EAAE,IAAI,EAAEC,wBAAgC;AACxC,EAAE,CAAC,EAAEC,cAAsB;AAC3B,EAAE,EAAE,EAAEC,YAAoB;AAC1B,EAAE,GAAG,EAAEC,aAAqB;AAC5B,EAAE,IAAI,EAAEC,aAAqB;AAC7B,EAAE,CAAC,EAAEC,2BAAmC;AACxC,EAAE,EAAE,EAAEC,yBAAiC;AACvC,EAAE,GAAG,EAAEC,0BAAkC;AACzC,EAAE,IAAI,EAAEC,0BAAkC;AAC1C,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACe,MAAM,SAAS,CAAC;AAC/B,EAAE,OAAO,MAAM,CAAC,MAAM,EAAE,IAAI,GAAG,EAAE,EAAE;AACnC,IAAI,OAAO,IAAI,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACvC,GAAG;AACH;AACA,EAAE,OAAO,WAAW,CAAC,GAAG,EAAE;AAC1B;AACA;AACA;AACA,IAAI,IAAI,OAAO,GAAG,IAAI;AACtB,MAAM,WAAW,GAAG,EAAE;AACtB,MAAM,SAAS,GAAG,KAAK,CAAC;AACxB,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;AACtB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,MAAM,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC9B,MAAM,IAAI,CAAC,KAAK,GAAG,EAAE;AACrB,QAAQ,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;AACpC,UAAU,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,CAAC;AAC7F,SAAS;AACT,QAAQ,OAAO,GAAG,IAAI,CAAC;AACvB,QAAQ,WAAW,GAAG,EAAE,CAAC;AACzB,QAAQ,SAAS,GAAG,CAAC,SAAS,CAAC;AAC/B,OAAO,MAAM,IAAI,SAAS,EAAE;AAC5B,QAAQ,WAAW,IAAI,CAAC,CAAC;AACzB,OAAO,MAAM,IAAI,CAAC,KAAK,OAAO,EAAE;AAChC,QAAQ,WAAW,IAAI,CAAC,CAAC;AACzB,OAAO,MAAM;AACb,QAAQ,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;AACpC,UAAU,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,CAAC;AAChF,SAAS;AACT,QAAQ,WAAW,GAAG,CAAC,CAAC;AACxB,QAAQ,OAAO,GAAG,CAAC,CAAC;AACpB,OAAO;AACP,KAAK;AACL;AACA,IAAI,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;AAChC,MAAM,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,CAAC;AACzF,KAAK;AACL;AACA,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH;AACA,EAAE,OAAO,sBAAsB,CAAC,KAAK,EAAE;AACvC,IAAI,OAAO,sBAAsB,CAAC,KAAK,CAAC,CAAC;AACzC,GAAG;AACH;AACA,EAAE,WAAW,CAAC,MAAM,EAAE,UAAU,EAAE;AAClC,IAAI,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;AAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;AACtB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH;AACA,EAAE,uBAAuB,CAAC,EAAE,EAAE,IAAI,EAAE;AACpC,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;AACjC,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;AACpD,KAAK;AACL,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC;AACzE,IAAI,OAAO,EAAE,CAAC,MAAM,EAAE,CAAC;AACvB,GAAG;AACH;AACA,EAAE,WAAW,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE;AAC7B,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC;AAC/D,GAAG;AACH;AACA,EAAE,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;AAC/C,GAAG;AACH;AACA,EAAE,mBAAmB,CAAC,EAAE,EAAE,IAAI,EAAE;AAChC,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,aAAa,EAAE,CAAC;AACtD,GAAG;AACH;AACA,EAAE,cAAc,CAAC,QAAQ,EAAE,IAAI,EAAE;AACjC,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACtD,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;AAClF,GAAG;AACH;AACA,EAAE,eAAe,CAAC,EAAE,EAAE,IAAI,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,eAAe,EAAE,CAAC;AACxD,GAAG;AACH;AACA,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE;AAChB;AACA,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AAC/B,MAAM,OAAO,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5B,KAAK;AACL;AACA,IAAI,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AAClC;AACA,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE;AACf,MAAM,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACrB,KAAK;AACL;AACA,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACpD,GAAG;AACH;AACA,EAAE,wBAAwB,CAAC,EAAE,EAAE,GAAG,EAAE;AACpC,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,IAAI;AACxD,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,KAAK,SAAS;AAC7F,MAAM,MAAM,GAAG,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC;AACrE,MAAM,YAAY,GAAG,CAAC,IAAI,KAAK;AAC/B,QAAQ,IAAI,EAAE,CAAC,aAAa,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;AAChE,UAAU,OAAO,GAAG,CAAC;AACrB,SAAS;AACT;AACA,QAAQ,OAAO,EAAE,CAAC,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;AAC1E,OAAO;AACP,MAAM,QAAQ,GAAG;AACjB,QAAQ,YAAY;AACpB,YAAYC,mBAA2B,CAAC,EAAE,CAAC;AAC3C,YAAY,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,EAAE,WAAW,CAAC;AACtE,MAAM,KAAK,GAAG,CAAC,MAAM,EAAE,UAAU;AACjC,QAAQ,YAAY;AACpB,YAAYC,gBAAwB,CAAC,EAAE,EAAE,MAAM,CAAC;AAChD,YAAY,MAAM,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,OAAO,CAAC;AAC/F,MAAM,OAAO,GAAG,CAAC,MAAM,EAAE,UAAU;AACnC,QAAQ,YAAY;AACpB,YAAYC,kBAA0B,CAAC,EAAE,EAAE,MAAM,CAAC;AAClD,YAAY,MAAM;AAClB,cAAc,UAAU,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE;AACnG,cAAc,SAAS;AACvB,aAAa;AACb,MAAM,UAAU,GAAG,CAAC,KAAK,KAAK;AAC9B,QAAQ,MAAM,UAAU,GAAG,SAAS,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;AACnE,QAAQ,IAAI,UAAU,EAAE;AACxB,UAAU,OAAO,IAAI,CAAC,uBAAuB,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;AAC9D,SAAS,MAAM;AACf,UAAU,OAAO,KAAK,CAAC;AACvB,SAAS;AACT,OAAO;AACP,MAAM,GAAG,GAAG,CAAC,MAAM;AACnB,QAAQ,YAAY,GAAGC,cAAsB,CAAC,EAAE,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,KAAK,CAAC;AAC1F,MAAM,aAAa,GAAG,CAAC,KAAK,KAAK;AACjC;AACA,QAAQ,QAAQ,KAAK;AACrB;AACA,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;AAC5C,UAAU,KAAK,GAAG,CAAC;AACnB;AACA,UAAU,KAAK,KAAK;AACpB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;AAC/C;AACA,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AACvC,UAAU,KAAK,IAAI;AACnB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAC1C;AACA,UAAU,KAAK,IAAI;AACnB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAChE,UAAU,KAAK,KAAK;AACpB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC;AAC9D;AACA,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AACvC,UAAU,KAAK,IAAI;AACnB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAC1C;AACA,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;AACpE,UAAU,KAAK,IAAI;AACnB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;AACvE,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AACrC,UAAU,KAAK,IAAI;AACnB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACxC;AACA,UAAU,KAAK,GAAG;AAClB;AACA,YAAY,OAAO,YAAY,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;AAChF,UAAU,KAAK,IAAI;AACnB;AACA,YAAY,OAAO,YAAY,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;AAC/E,UAAU,KAAK,KAAK;AACpB;AACA,YAAY,OAAO,YAAY,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;AAChF,UAAU,KAAK,MAAM;AACrB;AACA,YAAY,OAAO,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;AAC3F,UAAU,KAAK,OAAO;AACtB;AACA,YAAY,OAAO,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;AAC1F;AACA,UAAU,KAAK,GAAG;AAClB;AACA,YAAY,OAAO,EAAE,CAAC,QAAQ,CAAC;AAC/B;AACA,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,QAAQ,EAAE,CAAC;AAC9B;AACA,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,oBAAoB,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AAC/F,UAAU,KAAK,IAAI;AACnB,YAAY,OAAO,oBAAoB,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAClG;AACA,UAAU,KAAK,GAAG;AAClB;AACA,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;AACxC,UAAU,KAAK,KAAK;AACpB;AACA,YAAY,OAAO,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAC1C,UAAU,KAAK,MAAM;AACrB;AACA,YAAY,OAAO,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACzC,UAAU,KAAK,OAAO;AACtB;AACA,YAAY,OAAO,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC3C;AACA,UAAU,KAAK,GAAG;AAClB;AACA,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;AACxC,UAAU,KAAK,KAAK;AACpB;AACA,YAAY,OAAO,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC3C,UAAU,KAAK,MAAM;AACrB;AACA,YAAY,OAAO,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC1C,UAAU,KAAK,OAAO;AACtB;AACA,YAAY,OAAO,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC5C;AACA,UAAU,KAAK,GAAG;AAClB;AACA,YAAY,OAAO,oBAAoB;AACvC,gBAAgB,MAAM,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,OAAO,CAAC;AACrE,gBAAgB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AACnC,UAAU,KAAK,IAAI;AACnB;AACA,YAAY,OAAO,oBAAoB;AACvC,gBAAgB,MAAM,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,OAAO,CAAC;AACrE,gBAAgB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACtC,UAAU,KAAK,KAAK;AACpB;AACA,YAAY,OAAO,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACxC,UAAU,KAAK,MAAM;AACrB;AACA,YAAY,OAAO,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACvC,UAAU,KAAK,OAAO;AACtB;AACA,YAAY,OAAO,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACzC;AACA,UAAU,KAAK,GAAG;AAClB;AACA,YAAY,OAAO,oBAAoB;AACvC,gBAAgB,MAAM,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,OAAO,CAAC;AACrD,gBAAgB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AACnC,UAAU,KAAK,IAAI;AACnB;AACA,YAAY,OAAO,oBAAoB;AACvC,gBAAgB,MAAM,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,OAAO,CAAC;AACrD,gBAAgB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACtC,UAAU,KAAK,KAAK;AACpB;AACA,YAAY,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACzC,UAAU,KAAK,MAAM;AACrB;AACA,YAAY,OAAO,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACxC,UAAU,KAAK,OAAO;AACtB;AACA,YAAY,OAAO,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC1C;AACA,UAAU,KAAK,GAAG;AAClB;AACA,YAAY,OAAO,oBAAoB,GAAG,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAClG,UAAU,KAAK,IAAI;AACnB;AACA,YAAY,OAAO,oBAAoB;AACvC,gBAAgB,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC;AACnD,gBAAgB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1D,UAAU,KAAK,MAAM;AACrB;AACA,YAAY,OAAO,oBAAoB;AACvC,gBAAgB,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC;AACnD,gBAAgB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACrC,UAAU,KAAK,QAAQ;AACvB;AACA,YAAY,OAAO,oBAAoB;AACvC,gBAAgB,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC;AACnD,gBAAgB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACrC;AACA,UAAU,KAAK,GAAG;AAClB;AACA,YAAY,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC;AAChC,UAAU,KAAK,IAAI;AACnB;AACA,YAAY,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC;AAC/B,UAAU,KAAK,OAAO;AACtB,YAAY,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC;AACjC,UAAU,KAAK,IAAI;AACnB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjE,UAAU,KAAK,MAAM;AACrB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AAC5C,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;AAC3C,UAAU,KAAK,IAAI;AACnB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AAC9C,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC;AAChD,UAAU,KAAK,IAAI;AACnB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;AACnD,UAAU,KAAK,IAAI;AACnB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACtE,UAAU,KAAK,MAAM;AACrB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;AACjD,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;AACxC,UAAU,KAAK,KAAK;AACpB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AAC3C,UAAU,KAAK,GAAG;AAClB;AACA,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;AACxC,UAAU,KAAK,IAAI;AACnB;AACA,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AAC3C,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AACtD,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACnC,UAAU;AACV,YAAY,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;AACrC,SAAS;AACT,OAAO,CAAC;AACR;AACA,IAAI,OAAO,eAAe,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,aAAa,CAAC,CAAC;AACtE,GAAG;AACH;AACA,EAAE,wBAAwB,CAAC,GAAG,EAAE,GAAG,EAAE;AACrC,IAAI,MAAM,YAAY,GAAG,CAAC,KAAK,KAAK;AACpC,QAAQ,QAAQ,KAAK,CAAC,CAAC,CAAC;AACxB,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,aAAa,CAAC;AACjC,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,QAAQ,CAAC;AAC5B,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,QAAQ,CAAC;AAC5B,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,MAAM,CAAC;AAC1B,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,KAAK,CAAC;AACzB,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,MAAM,CAAC;AAC1B,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,OAAO,CAAC;AAC3B,UAAU,KAAK,GAAG;AAClB,YAAY,OAAO,MAAM,CAAC;AAC1B,UAAU;AACV,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,OAAO;AACP,MAAM,aAAa,GAAG,CAAC,MAAM,KAAK,CAAC,KAAK,KAAK;AAC7C,QAAQ,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;AAC3C,QAAQ,IAAI,MAAM,EAAE;AACpB,UAAU,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;AAC5D,SAAS,MAAM;AACf,UAAU,OAAO,KAAK,CAAC;AACvB,SAAS;AACT,OAAO;AACP,MAAM,MAAM,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC;AACzC,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM;AAChC,QAAQ,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,OAAO,GAAG,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC1E,QAAQ,EAAE;AACV,OAAO;AACP,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChF,IAAI,OAAO,eAAe,CAAC,MAAM,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC;AAC7D,GAAG;AACH;;AC5YA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,GAAG,8EAA8E,CAAC;AACjG;AACA,SAAS,cAAc,CAAC,GAAG,OAAO,EAAE;AACpC,EAAE,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AAC1D,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,CAAC;AACD;AACA,SAAS,iBAAiB,CAAC,GAAG,UAAU,EAAE;AAC1C,EAAE,OAAO,CAAC,CAAC;AACX,IAAI,UAAU;AACd,OAAO,MAAM;AACb,QAAQ,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,EAAE,EAAE,KAAK;AAClD,UAAU,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAClD,UAAU,OAAO,CAAC,EAAE,GAAG,UAAU,EAAE,GAAG,GAAG,EAAE,EAAE,IAAI,IAAI,UAAU,EAAE,IAAI,CAAC,CAAC;AACvE,SAAS;AACT,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;AACrB,OAAO;AACP,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB,CAAC;AACD;AACA,SAAS,KAAK,CAAC,CAAC,EAAE,GAAG,QAAQ,EAAE;AAC/B,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE;AACjB,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACxB,GAAG;AACH;AACA,EAAE,KAAK,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,QAAQ,EAAE;AAC7C,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,IAAI,IAAI,CAAC,EAAE;AACX,MAAM,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC;AAC1B,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACtB,CAAC;AACD;AACA,SAAS,WAAW,CAAC,GAAG,IAAI,EAAE;AAC9B,EAAE,OAAO,CAAC,KAAK,EAAE,MAAM,KAAK;AAC5B,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;AACnB,IAAI,IAAI,CAAC,CAAC;AACV;AACA,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AACrD,KAAK;AACL,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AACnC,GAAG,CAAC;AACJ,CAAC;AACD;AACA;AACA,MAAM,WAAW,GAAG,iCAAiC,CAAC;AACtD,MAAM,eAAe,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACtF,MAAM,gBAAgB,GAAG,qDAAqD,CAAC;AAC/E,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;AAC5E,MAAM,qBAAqB,GAAG,MAAM,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AACrE,MAAM,WAAW,GAAG,6CAA6C,CAAC;AAClE,MAAM,YAAY,GAAG,6BAA6B,CAAC;AACnD,MAAM,eAAe,GAAG,kBAAkB,CAAC;AAC3C,MAAM,kBAAkB,GAAG,WAAW,CAAC,UAAU,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;AAC5E,MAAM,qBAAqB,GAAG,WAAW,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAC7D,MAAM,WAAW,GAAG,uBAAuB,CAAC;AAC5C,MAAM,YAAY,GAAG,MAAM;AAC3B,EAAE,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC;AAChF,CAAC,CAAC;AACF,MAAM,qBAAqB,GAAG,MAAM,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AACrE;AACA,SAAS,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE;AACnC,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AACvB,EAAE,OAAO,WAAW,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC;AACD;AACA,SAAS,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE;AACtC,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;AAC5B,IAAI,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;AACpC,IAAI,GAAG,EAAE,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;AAClC,GAAG,CAAC;AACJ;AACA,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAClC,CAAC;AACD;AACA,SAAS,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE;AACvC,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;AAChC,IAAI,OAAO,EAAE,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;AACtC,IAAI,OAAO,EAAE,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;AACtC,IAAI,YAAY,EAAE,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAChD,GAAG,CAAC;AACJ;AACA,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAClC,CAAC;AACD;AACA,SAAS,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE;AACzC,EAAE,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACpD,IAAI,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACnE,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AAC/D,EAAE,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAChC,CAAC;AACD;AACA,SAAS,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE;AACxC,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;AACrE,EAAE,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAChC,CAAC;AACD;AACA;AACA;AACA,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,GAAG,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D;AACA;AACA;AACA,MAAM,WAAW;AACjB,EAAE,8PAA8P,CAAC;AACjQ;AACA,SAAS,kBAAkB,CAAC,KAAK,EAAE;AACnC,EAAE,MAAM,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,CAAC;AAC/F,IAAI,KAAK,CAAC;AACV;AACA,EAAE,MAAM,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;AACzC,EAAE,MAAM,eAAe,GAAG,SAAS,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;AAC5D;AACA,EAAE,MAAM,WAAW,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,KAAK;AACzC,IAAI,GAAG,KAAK,SAAS,KAAK,KAAK,KAAK,GAAG,IAAI,iBAAiB,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;AAC5E;AACA,EAAE,OAAO;AACT,IAAI;AACJ,MAAM,KAAK,EAAE,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAChD,MAAM,MAAM,EAAE,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAClD,MAAM,KAAK,EAAE,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAChD,MAAM,IAAI,EAAE,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAC9C,MAAM,KAAK,EAAE,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAChD,MAAM,OAAO,EAAE,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AACpD,MAAM,OAAO,EAAE,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,SAAS,KAAK,IAAI,CAAC;AACxE,MAAM,YAAY,EAAE,WAAW,CAAC,WAAW,CAAC,eAAe,CAAC,EAAE,eAAe,CAAC;AAC9E,KAAK;AACL,GAAG,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM,UAAU,GAAG;AACnB,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;AACd,CAAC,CAAC;AACF;AACA,SAAS,WAAW,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE;AAC3F,EAAE,MAAM,MAAM,GAAG;AACjB,IAAI,IAAI,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC,GAAG,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC;AAC9F,IAAI,KAAK,EAAEC,WAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;AACpD,IAAI,GAAG,EAAE,YAAY,CAAC,MAAM,CAAC;AAC7B,IAAI,IAAI,EAAE,YAAY,CAAC,OAAO,CAAC;AAC/B,IAAI,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC;AACnC,GAAG,CAAC;AACJ;AACA,EAAE,IAAI,SAAS,EAAE,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;AACzD,EAAE,IAAI,UAAU,EAAE;AAClB,IAAI,MAAM,CAAC,OAAO;AAClB,MAAM,UAAU,CAAC,MAAM,GAAG,CAAC;AAC3B,UAAUC,YAAoB,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC;AACtD,UAAUC,aAAqB,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACxD,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD;AACA;AACA,MAAM,OAAO;AACb,EAAE,iMAAiM,CAAC;AACpM;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,MAAM;AACR;AACA,MAAM,UAAU;AAChB,MAAM,MAAM;AACZ,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,UAAU;AAChB,MAAM,YAAY;AAClB,KAAK,GAAG,KAAK;AACb,IAAI,MAAM,GAAG,WAAW,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AAC/F;AACA,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;AACnC,GAAG,MAAM,IAAI,SAAS,EAAE;AACxB,IAAI,MAAM,GAAG,CAAC,CAAC;AACf,GAAG,MAAM;AACT,IAAI,MAAM,GAAG,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;AACpD,GAAG;AACH;AACA,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;AAC/C,CAAC;AACD;AACA,SAAS,iBAAiB,CAAC,CAAC,EAAE;AAC9B;AACA,EAAE,OAAO,CAAC;AACV,KAAK,OAAO,CAAC,oBAAoB,EAAE,GAAG,CAAC;AACvC,KAAK,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC;AAC7B,KAAK,IAAI,EAAE,CAAC;AACZ,CAAC;AACD;AACA;AACA;AACA,MAAM,OAAO;AACb,IAAI,4HAA4H;AAChI,EAAE,MAAM;AACR,IAAI,wJAAwJ;AAC5J,EAAE,KAAK;AACP,IAAI,2HAA2H,CAAC;AAChI;AACA,SAAS,mBAAmB,CAAC,KAAK,EAAE;AACpC,EAAE,MAAM,GAAG,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,GAAG,KAAK;AACxF,IAAI,MAAM,GAAG,WAAW,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AAC/F,EAAE,OAAO,CAAC,MAAM,EAAE,eAAe,CAAC,WAAW,CAAC,CAAC;AAC/C,CAAC;AACD;AACA,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,EAAE,MAAM,GAAG,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,GAAG,KAAK;AACxF,IAAI,MAAM,GAAG,WAAW,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AAC/F,EAAE,OAAO,CAAC,MAAM,EAAE,eAAe,CAAC,WAAW,CAAC,CAAC;AAC/C,CAAC;AACD;AACA,MAAM,4BAA4B,GAAG,cAAc,CAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC;AACxF,MAAM,6BAA6B,GAAG,cAAc,CAAC,YAAY,EAAE,qBAAqB,CAAC,CAAC;AAC1F,MAAM,gCAAgC,GAAG,cAAc,CAAC,eAAe,EAAE,qBAAqB,CAAC,CAAC;AAChG,MAAM,oBAAoB,GAAG,cAAc,CAAC,YAAY,CAAC,CAAC;AAC1D;AACA,MAAM,0BAA0B,GAAG,iBAAiB;AACpD,EAAE,aAAa;AACf,EAAE,cAAc;AAChB,EAAE,gBAAgB;AAClB,EAAE,eAAe;AACjB,CAAC,CAAC;AACF,MAAM,2BAA2B,GAAG,iBAAiB;AACrD,EAAE,kBAAkB;AACpB,EAAE,cAAc;AAChB,EAAE,gBAAgB;AAClB,EAAE,eAAe;AACjB,CAAC,CAAC;AACF,MAAM,4BAA4B,GAAG,iBAAiB;AACtD,EAAE,qBAAqB;AACvB,EAAE,cAAc;AAChB,EAAE,gBAAgB;AAClB,EAAE,eAAe;AACjB,CAAC,CAAC;AACF,MAAM,uBAAuB,GAAG,iBAAiB;AACjD,EAAE,cAAc;AAChB,EAAE,gBAAgB;AAClB,EAAE,eAAe;AACjB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,CAAC,EAAE;AAChC,EAAE,OAAO,KAAK;AACd,IAAI,CAAC;AACL,IAAI,CAAC,4BAA4B,EAAE,0BAA0B,CAAC;AAC9D,IAAI,CAAC,6BAA6B,EAAE,2BAA2B,CAAC;AAChE,IAAI,CAAC,gCAAgC,EAAE,4BAA4B,CAAC;AACpE,IAAI,CAAC,oBAAoB,EAAE,uBAAuB,CAAC;AACnD,GAAG,CAAC;AACJ,CAAC;AACD;AACO,SAAS,gBAAgB,CAAC,CAAC,EAAE;AACpC,EAAE,OAAO,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;AAChE,CAAC;AACD;AACO,SAAS,aAAa,CAAC,CAAC,EAAE;AACjC,EAAE,OAAO,KAAK;AACd,IAAI,CAAC;AACL,IAAI,CAAC,OAAO,EAAE,mBAAmB,CAAC;AAClC,IAAI,CAAC,MAAM,EAAE,mBAAmB,CAAC;AACjC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;AACzB,GAAG,CAAC;AACJ,CAAC;AACD;AACO,SAAS,gBAAgB,CAAC,CAAC,EAAE;AACpC,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC,CAAC;AACrD,CAAC;AACD;AACA,MAAM,kBAAkB,GAAG,iBAAiB,CAAC,cAAc,CAAC,CAAC;AAC7D;AACO,SAAS,gBAAgB,CAAC,CAAC,EAAE;AACpC,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC,CAAC;AACrD,CAAC;AACD;AACA,MAAM,4BAA4B,GAAG,cAAc,CAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC;AACxF,MAAM,oBAAoB,GAAG,cAAc,CAAC,YAAY,CAAC,CAAC;AAC1D;AACA,MAAM,+BAA+B,GAAG,iBAAiB;AACzD,EAAE,cAAc;AAChB,EAAE,gBAAgB;AAClB,EAAE,eAAe;AACjB,CAAC,CAAC;AACF;AACO,SAAS,QAAQ,CAAC,CAAC,EAAE;AAC5B,EAAE,OAAO,KAAK;AACd,IAAI,CAAC;AACL,IAAI,CAAC,4BAA4B,EAAE,0BAA0B,CAAC;AAC9D,IAAI,CAAC,oBAAoB,EAAE,+BAA+B,CAAC;AAC3D,GAAG,CAAC;AACJ;;AC9TA,MAAMC,SAAO,GAAG,kBAAkB,CAAC;AACnC;AACA;AACO,MAAM,cAAc,GAAG;AAC9B,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,KAAK,EAAE,CAAC,GAAG,EAAE;AACnB,MAAM,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE;AAC1B,MAAM,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC/B,MAAM,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;AAC3C,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,OAAO,EAAE,EAAE,GAAG,EAAE;AACtB,MAAM,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3B,MAAM,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;AACvC,KAAK;AACL,IAAI,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE;AAC1E,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,GAAG,IAAI,EAAE;AACrD,IAAI,OAAO,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE;AACnC,GAAG;AACH,EAAE,YAAY,GAAG;AACjB,IAAI,KAAK,EAAE;AACX,MAAM,QAAQ,EAAE,CAAC;AACjB,MAAM,MAAM,EAAE,EAAE;AAChB,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,IAAI,EAAE,GAAG;AACf,MAAM,KAAK,EAAE,GAAG,GAAG,EAAE;AACrB,MAAM,OAAO,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE;AAC5B,MAAM,OAAO,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACjC,MAAM,YAAY,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;AAC7C,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,KAAK,EAAE,EAAE,GAAG,EAAE;AACpB,MAAM,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3B,MAAM,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,MAAM,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;AAC5C,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,CAAC;AACd,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,KAAK,EAAE,EAAE,GAAG,EAAE;AACpB,MAAM,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3B,MAAM,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,MAAM,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;AAC5C,KAAK;AACL;AACA,IAAI,GAAG,cAAc;AACrB,GAAG;AACH,EAAE,kBAAkB,GAAG,QAAQ,GAAG,GAAG;AACrC,EAAE,mBAAmB,GAAG,QAAQ,GAAG,IAAI;AACvC,EAAE,cAAc,GAAG;AACnB,IAAI,KAAK,EAAE;AACX,MAAM,QAAQ,EAAE,CAAC;AACjB,MAAM,MAAM,EAAE,EAAE;AAChB,MAAM,KAAK,EAAE,kBAAkB,GAAG,CAAC;AACnC,MAAM,IAAI,EAAE,kBAAkB;AAC9B,MAAM,KAAK,EAAE,kBAAkB,GAAG,EAAE;AACpC,MAAM,OAAO,EAAE,kBAAkB,GAAG,EAAE,GAAG,EAAE;AAC3C,MAAM,OAAO,EAAE,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChD,MAAM,YAAY,EAAE,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;AAC5D,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,KAAK,EAAE,kBAAkB,GAAG,EAAE;AACpC,MAAM,IAAI,EAAE,kBAAkB,GAAG,CAAC;AAClC,MAAM,KAAK,EAAE,CAAC,kBAAkB,GAAG,EAAE,IAAI,CAAC;AAC1C,MAAM,OAAO,EAAE,CAAC,kBAAkB,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;AACjD,MAAM,OAAO,EAAE,CAAC,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;AACtD,MAAM,YAAY,EAAE,CAAC,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,IAAI,CAAC;AAClE,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,mBAAmB,GAAG,CAAC;AACpC,MAAM,IAAI,EAAE,mBAAmB;AAC/B,MAAM,KAAK,EAAE,mBAAmB,GAAG,EAAE;AACrC,MAAM,OAAO,EAAE,mBAAmB,GAAG,EAAE,GAAG,EAAE;AAC5C,MAAM,OAAO,EAAE,mBAAmB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACjD,MAAM,YAAY,EAAE,mBAAmB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;AAC7D,KAAK;AACL,IAAI,GAAG,cAAc;AACrB,GAAG,CAAC;AACJ;AACA;AACA,MAAMC,cAAY,GAAG;AACrB,EAAE,OAAO;AACT,EAAE,UAAU;AACZ,EAAE,QAAQ;AACV,EAAE,OAAO;AACT,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,SAAS;AACX,EAAE,SAAS;AACX,EAAE,cAAc;AAChB,CAAC,CAAC;AACF;AACA,MAAM,YAAY,GAAGA,cAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AACrD;AACA;AACA,SAASC,OAAK,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,GAAG,KAAK,EAAE;AACzC;AACA,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,MAAM,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,GAAG,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,EAAE;AAC3E,IAAI,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;AAChC,IAAI,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,IAAI,GAAG,CAAC,kBAAkB;AACzE,IAAI,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM;AACrC,GAAG,CAAC;AACJ,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC5B,CAAC;AACD;AACA,SAAS,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAE;AACxC,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;AACnC,EAAE,KAAK,MAAM,IAAI,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;AAC5C,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;AACpB,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACvD,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD;AACA;AACA,SAAS,eAAe,CAAC,MAAM,EAAE,IAAI,EAAE;AACvC;AACA;AACA,EAAE,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7D;AACA,EAAED,cAAY,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAK;AAClD,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE;AACrC,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AACpD,QAAQ,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC;AACtD,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC;AACzC,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,MAAM,GAAG,IAAI,GAAG,MAAM,CAAC;AACjD,OAAO;AACP,MAAM,OAAO,OAAO,CAAC;AACrB,KAAK,MAAM;AACX,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK;AACL,GAAG,EAAE,IAAI,CAAC,CAAC;AACX;AACA;AACA;AACA,EAAEA,cAAY,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAK;AAC7C,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE;AACrC,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC5C,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC;AACnC,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;AAC9D,OAAO;AACP,MAAM,OAAO,OAAO,CAAC;AACrB,KAAK,MAAM;AACX,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK;AACL,GAAG,EAAE,IAAI,CAAC,CAAC;AACX,CAAC;AACD;AACA;AACA,SAAS,YAAY,CAAC,IAAI,EAAE;AAC5B,EAAE,MAAM,OAAO,GAAG,EAAE,CAAC;AACrB,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACnD,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE;AACrB,MAAM,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC3B,KAAK;AACL,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAM,QAAQ,CAAC;AAC9B;AACA;AACA;AACA,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB,KAAK,UAAU,IAAI,KAAK,CAAC;AACvE,IAAI,IAAI,MAAM,GAAG,QAAQ,GAAG,cAAc,GAAG,YAAY,CAAC;AAC1D;AACA,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE;AACvB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAChC;AACA;AACA;AACA,IAAI,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;AAC7C;AACA;AACA;AACA,IAAI,IAAI,CAAC,kBAAkB,GAAG,QAAQ,GAAG,UAAU,GAAG,QAAQ,CAAC;AAC/D;AACA;AACA;AACA,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC;AAC1C;AACA;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB;AACA;AACA;AACA,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE;AACjC,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,CAAC;AAC9D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,UAAU,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE;AACpC,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAChD,MAAM,MAAM,IAAI,oBAAoB;AACpC,QAAQ,CAAC,4DAA4D;AACrE,UAAU,GAAG,KAAK,IAAI,GAAG,MAAM,GAAG,OAAO,GAAG;AAC5C,SAAS,CAAC;AACV,OAAO,CAAC;AACR,KAAK;AACL;AACA,IAAI,OAAO,IAAI,QAAQ,CAAC;AACxB,MAAM,MAAM,EAAE,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,aAAa,CAAC;AAC1D,MAAM,GAAG,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;AAClC,MAAM,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;AACjD,MAAM,MAAM,EAAE,IAAI,CAAC,MAAM;AACzB,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,gBAAgB,CAAC,YAAY,EAAE;AACxC,IAAI,IAAI,QAAQ,CAAC,YAAY,CAAC,EAAE;AAChC,MAAM,OAAO,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;AAC/C,KAAK,MAAM,IAAI,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;AAClD,MAAM,OAAO,YAAY,CAAC;AAC1B,KAAK,MAAM,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;AACjD,MAAM,OAAO,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;AAC/C,KAAK,MAAM;AACX,MAAM,MAAM,IAAI,oBAAoB;AACpC,QAAQ,CAAC,0BAA0B,EAAE,YAAY,CAAC,SAAS,EAAE,OAAO,YAAY,CAAC,CAAC;AAClF,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE;AAC7B,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAC5C,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,OAAO,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC/C,KAAK,MAAM;AACX,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC;AAC/F,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AACjC,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAC5C,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,OAAO,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC/C,KAAK,MAAM;AACX,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC;AAC/F,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,OAAO,CAAC,MAAM,EAAE,WAAW,GAAG,IAAI,EAAE;AAC7C,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,IAAI,oBAAoB,CAAC,kDAAkD,CAAC,CAAC;AACzF,KAAK;AACL;AACA,IAAI,MAAM,OAAO,GAAG,MAAM,YAAY,OAAO,GAAG,MAAM,GAAG,IAAI,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAC1F;AACA,IAAI,IAAI,QAAQ,CAAC,cAAc,EAAE;AACjC,MAAM,MAAM,IAAI,oBAAoB,CAAC,OAAO,CAAC,CAAC;AAC9C,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,QAAQ,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;AACvC,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,OAAO,aAAa,CAAC,IAAI,EAAE;AAC7B,IAAI,MAAM,UAAU,GAAG;AACvB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,GAAG,EAAE,MAAM;AACjB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,YAAY,EAAE,cAAc;AAClC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,CAAC;AACxC;AACA,IAAI,IAAI,CAAC,UAAU,EAAE,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;AACtD;AACA,IAAI,OAAO,UAAU,CAAC;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,UAAU,CAAC,CAAC,EAAE;AACvB,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,eAAe,KAAK,KAAK,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC;AACjD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,eAAe,GAAG;AACxB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC;AAC1D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE;AAC3B;AACA,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,GAAG,IAAI;AACb,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK;AACzD,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,OAAO;AACvB,QAAQ,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,wBAAwB,CAAC,IAAI,EAAE,GAAG,CAAC;AAC/E,QAAQD,SAAO,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,IAAI,GAAG,EAAE,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAOA,SAAO,CAAC;AACtC;AACA,IAAI,MAAM,CAAC,GAAGC,cAAY;AAC1B,OAAO,GAAG,CAAC,CAAC,IAAI,KAAK;AACrB,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtC,QAAQ,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;AAC9B,UAAU,OAAO,IAAI,CAAC;AACtB,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,GAAG;AACvB,WAAW,eAAe,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AACpG,WAAW,MAAM,CAAC,GAAG,CAAC,CAAC;AACvB,OAAO,CAAC;AACR,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACxB;AACA,IAAI,OAAO,IAAI,CAAC,GAAG;AACnB,OAAO,aAAa,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,IAAI,QAAQ,EAAE,GAAG,IAAI,EAAE,CAAC;AACzF,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;AACjC,IAAI,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC9B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,GAAG;AACV;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC;AACA,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC;AAChB,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;AAChD,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAC;AAC7F,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;AAChD,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AAC9C,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC;AAC/F,MAAM,CAAC,IAAI,GAAG,CAAC;AACf,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;AAChD,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;AACpD,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC;AACrD;AACA;AACA,MAAM,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACrE,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,KAAK,CAAC;AAC9B,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,IAAI,GAAG,EAAE,EAAE;AACvB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC;AACA,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AACnC,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,IAAI,QAAQ,EAAE,OAAO,IAAI,CAAC;AACtD;AACA,IAAI,IAAI,GAAG;AACX,MAAM,oBAAoB,EAAE,KAAK;AACjC,MAAM,eAAe,EAAE,KAAK;AAC5B,MAAM,aAAa,EAAE,KAAK;AAC1B,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,GAAG,IAAI;AACb,MAAM,aAAa,EAAE,KAAK;AAC1B,KAAK,CAAC;AACN;AACA,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;AAClE,IAAI,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACpC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;AACxB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;AACxB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAAG;AAC/C,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AACtB,MAAM,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AACnE,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,4BAA4B,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;AACnE,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,CAAC;AAClC;AACA,IAAI,OAAO,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACtD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC3B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC;AACA,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AACnD,MAAM,MAAM,GAAG,EAAE,CAAC;AAClB;AACA,IAAI,KAAK,MAAM,CAAC,IAAIA,cAAY,EAAE;AAClC,MAAM,IAAI,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE;AAC3E,QAAQ,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C,OAAO;AACP,KAAK;AACL;AACA,IAAI,OAAOC,OAAK,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;AACjD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,QAAQ,EAAE;AAClB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC;AACA,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;AACnC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,EAAE,EAAE;AACf,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;AACtB,IAAI,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AAC9C,MAAM,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,OAAOA,OAAK,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;AACjD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,GAAG,CAAC,IAAI,EAAE;AACZ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,GAAG,CAAC,MAAM,EAAE;AACd,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC;AACA,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;AACzF,IAAI,OAAOA,OAAK,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,EAAE,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE;AAC5E,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC,CAAC;AAC5D,IAAI,MAAM,IAAI,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC;AACrD,IAAI,OAAOA,OAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,EAAE,CAAC,IAAI,EAAE;AACX,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;AAC7D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,GAAG;AACd,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AACjC,IAAI,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACvC,IAAI,OAAOA,OAAK,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;AAC/C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC,IAAI,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;AACxE,IAAI,OAAOA,OAAK,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;AAC/C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,GAAG,KAAK,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC;AACA,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5B,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL;AACA,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD;AACA,IAAI,MAAM,KAAK,GAAG,EAAE;AACpB,MAAM,WAAW,GAAG,EAAE;AACtB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC7B,IAAI,IAAI,QAAQ,CAAC;AACjB;AACA,IAAI,KAAK,MAAM,CAAC,IAAID,cAAY,EAAE;AAClC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;AACjC,QAAQ,QAAQ,GAAG,CAAC,CAAC;AACrB;AACA,QAAQ,IAAI,GAAG,GAAG,CAAC,CAAC;AACpB;AACA;AACA,QAAQ,KAAK,MAAM,EAAE,IAAI,WAAW,EAAE;AACtC,UAAU,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;AACtD,UAAU,WAAW,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AAC9B,SAAS;AACT;AACA;AACA,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;AAC/B,UAAU,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACzB,SAAS;AACT;AACA;AACA;AACA,QAAQ,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAClC,QAAQ,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACrB,QAAQ,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC;AACxD;AACA;AACA,OAAO,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;AACpC,QAAQ,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACjC,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,IAAI,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE;AACnC,MAAM,IAAI,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAClC,QAAQ,KAAK,CAAC,QAAQ,CAAC;AACvB,UAAU,GAAG,KAAK,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;AAC9F,OAAO;AACP,KAAK;AACL;AACA,IAAI,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACxC,IAAI,OAAOC,OAAK,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,CAAC;AAChD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC,IAAI,OAAO,IAAI,CAAC,OAAO;AACvB,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,cAAc;AACpB,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;AACvB,IAAI,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AAC9C,MAAM,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC9D,KAAK;AACL,IAAI,OAAOA,OAAK,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,IAAI,CAAC,CAAC;AAClD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC;AACvD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,QAAQ,GAAG;AACjB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,GAAG,GAAG,CAAC;AAC1D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC;AACxD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC;AACvD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC;AACtD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC;AACvD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC;AACzD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC;AACzD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC;AAC9D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC;AACjC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,aAAa,GAAG;AACtB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;AACrD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,kBAAkB,GAAG;AAC3B,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AACzC,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACrC,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL;AACA,IAAI,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;AACxB;AACA,MAAM,IAAI,EAAE,KAAK,SAAS,IAAI,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,SAAS,IAAI,EAAE,KAAK,CAAC,CAAC;AAC5E,MAAM,OAAO,EAAE,KAAK,EAAE,CAAC;AACvB,KAAK;AACL;AACA,IAAI,KAAK,MAAM,CAAC,IAAID,cAAY,EAAE;AAClC,MAAM,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;AAChD,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;;ACr9BA,MAAMD,SAAO,GAAG,kBAAkB,CAAC;AACnC;AACA;AACA,SAAS,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE;AACtC,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AAChC,IAAI,OAAO,QAAQ,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AACxD,GAAG,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;AACnC,IAAI,OAAO,QAAQ,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AACtD,GAAG,MAAM,IAAI,GAAG,GAAG,KAAK,EAAE;AAC1B,IAAI,OAAO,QAAQ,CAAC,OAAO;AAC3B,MAAM,kBAAkB;AACxB,MAAM,CAAC,kEAAkE,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC;AACjH,KAAK,CAAC;AACN,GAAG,MAAM;AACT,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAM,QAAQ,CAAC;AAC9B;AACA;AACA;AACA,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB;AACA;AACA;AACA,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;AAC1B;AACA;AACA;AACA,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC;AACxB;AACA;AACA;AACA,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC;AAC1C;AACA;AACA;AACA,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,OAAO,CAAC,MAAM,EAAE,WAAW,GAAG,IAAI,EAAE;AAC7C,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,IAAI,oBAAoB,CAAC,kDAAkD,CAAC,CAAC;AACzF,KAAK;AACL;AACA,IAAI,MAAM,OAAO,GAAG,MAAM,YAAY,OAAO,GAAG,MAAM,GAAG,IAAI,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAC1F;AACA,IAAI,IAAI,QAAQ,CAAC,cAAc,EAAE;AACjC,MAAM,MAAM,IAAI,oBAAoB,CAAC,OAAO,CAAC,CAAC;AAC9C,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,QAAQ,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;AACvC,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,aAAa,CAAC,KAAK,EAAE,GAAG,EAAE;AACnC,IAAI,MAAM,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC;AAC9C,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACvC;AACA,IAAI,MAAM,aAAa,GAAG,gBAAgB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AACjE;AACA,IAAI,IAAI,aAAa,IAAI,IAAI,EAAE;AAC/B,MAAM,OAAO,IAAI,QAAQ,CAAC;AAC1B,QAAQ,KAAK,EAAE,UAAU;AACzB,QAAQ,GAAG,EAAE,QAAQ;AACrB,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,OAAO,aAAa,CAAC;AAC3B,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE;AAChC,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AACnD,MAAM,EAAE,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACnC,IAAI,OAAO,QAAQ,CAAC,aAAa,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACpD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,MAAM,CAAC,GAAG,EAAE,QAAQ,EAAE;AAC/B,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AACnD,MAAM,EAAE,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACjC,IAAI,OAAO,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;AACrD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE;AAC7B,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;AAChB,MAAM,IAAI,KAAK,EAAE,YAAY,CAAC;AAC9B,MAAM,IAAI;AACV,QAAQ,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC1C,QAAQ,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;AACrC,OAAO,CAAC,OAAO,CAAC,EAAE;AAClB,QAAQ,YAAY,GAAG,KAAK,CAAC;AAC7B,OAAO;AACP;AACA,MAAM,IAAI,GAAG,EAAE,UAAU,CAAC;AAC1B,MAAM,IAAI;AACV,QAAQ,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACxC,QAAQ,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC;AACjC,OAAO,CAAC,OAAO,CAAC,EAAE;AAClB,QAAQ,UAAU,GAAG,KAAK,CAAC;AAC3B,OAAO;AACP;AACA,MAAM,IAAI,YAAY,IAAI,UAAU,EAAE;AACtC,QAAQ,OAAO,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAClD,OAAO;AACP;AACA,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC9C,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,OAAO,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC5C,SAAS;AACT,OAAO,MAAM,IAAI,UAAU,EAAE;AAC7B,QAAQ,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC9C,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC3C,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC;AAC7F,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,UAAU,CAAC,CAAC,EAAE;AACvB,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,eAAe,KAAK,KAAK,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;AACxC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,GAAG,GAAG;AACZ,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;AACxC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC;AACvC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,aAAa,GAAG;AACtB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;AACrD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,kBAAkB,GAAG;AAC3B,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,IAAI,GAAG,cAAc,EAAE;AAChC,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;AACrE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,IAAI,GAAG,cAAc,EAAE,IAAI,EAAE;AACrC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,CAAC;AAClC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACjD,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,IAAI,IAAI,EAAE,cAAc,EAAE;AAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;AAC3D,KAAK,MAAM;AACX,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACrB,KAAK;AACL,IAAI,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAClC,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;AAChG,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,IAAI,EAAE;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC;AAC1F,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AACjD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,QAAQ,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC;AACpC,IAAI,OAAO,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,QAAQ,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC;AACpC,IAAI,OAAO,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC;AAC9B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,QAAQ,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC;AACpC,IAAI,OAAO,IAAI,CAAC,CAAC,IAAI,QAAQ,IAAI,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC;AACnD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;AAC3B,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC,IAAI,OAAO,QAAQ,CAAC,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AAClE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,GAAG,SAAS,EAAE;AACxB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;AACjC,IAAI,MAAM,MAAM,GAAG,SAAS;AAC5B,SAAS,GAAG,CAAC,gBAAgB,CAAC;AAC9B,SAAS,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACxC,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;AACpD,MAAM,OAAO,GAAG,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI;AACpB,MAAM,CAAC,GAAG,CAAC,CAAC;AACZ;AACA,IAAI,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE;AACvB,MAAM,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;AACvC,QAAQ,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;AACjD,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AACpD,MAAM,CAAC,GAAG,IAAI,CAAC;AACf,MAAM,CAAC,IAAI,CAAC,CAAC;AACb,KAAK;AACL;AACA,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,QAAQ,EAAE;AACpB,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AACpD;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;AACvE,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL;AACA,IAAI,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI;AACpB,MAAM,GAAG,GAAG,CAAC;AACb,MAAM,IAAI,CAAC;AACX;AACA,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;AACvB,IAAI,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE;AACvB,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAClE,MAAM,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;AAC/C,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AACpD,MAAM,CAAC,GAAG,IAAI,CAAC;AACf,MAAM,GAAG,IAAI,CAAC,CAAC;AACf,KAAK;AACL;AACA,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,aAAa,EAAE;AAC/B,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;AACjC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;AAC/E,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,KAAK,EAAE;AAClB,IAAI,OAAO,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAChD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,KAAK,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC;AACpC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AAChC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,KAAK,EAAE;AAClB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC;AACpC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAChC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,KAAK,EAAE;AACjB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC;AACpC,IAAI,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC;AAClD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AACzC,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL;AACA,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,KAAK,EAAE;AACtB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AACjD,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAC9C;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,MAAM;AACX,MAAM,OAAO,QAAQ,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1C,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,KAAK,EAAE;AACf,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AACjD,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAC9C,IAAI,OAAO,QAAQ,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,KAAK,CAAC,SAAS,EAAE;AAC1B,IAAI,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,SAAS;AACpC,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC,OAAO,MAAM;AACb,QAAQ,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,IAAI,KAAK;AACpC,UAAU,IAAI,CAAC,OAAO,EAAE;AACxB,YAAY,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACjC,WAAW,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACzE,YAAY,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAChD,WAAW,MAAM;AACjB,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACnD,WAAW;AACX,SAAS;AACT,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC;AAClB,OAAO,CAAC;AACR,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG,CAAC,SAAS,EAAE;AACxB,IAAI,IAAI,KAAK,GAAG,IAAI;AACpB,MAAM,YAAY,GAAG,CAAC,CAAC;AACvB,IAAI,MAAM,OAAO,GAAG,EAAE;AACtB,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK;AAClC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE;AAChC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE;AAChC,OAAO,CAAC;AACR,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AACjD,MAAM,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AACtD;AACA,IAAI,KAAK,MAAM,CAAC,IAAI,GAAG,EAAE;AACzB,MAAM,YAAY,IAAI,CAAC,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9C;AACA,MAAM,IAAI,YAAY,KAAK,CAAC,EAAE;AAC9B,QAAQ,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC;AACvB,OAAO,MAAM;AACb,QAAQ,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;AACzC,UAAU,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9D,SAAS;AACT;AACA,QAAQ,KAAK,GAAG,IAAI,CAAC;AACrB,OAAO;AACP,KAAK;AACL;AACA,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACnC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,GAAG,SAAS,EAAE;AAC3B,IAAI,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACjD,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;AACvC,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;AACxC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAOA,SAAO,CAAC;AACtC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;AACrD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAAG;AAC/C,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AACtB,MAAM,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7E,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,4BAA4B,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;AACnE,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,UAAU,GAAG3B,UAAkB,EAAE,IAAI,GAAG,EAAE,EAAE;AAC7D,IAAI,OAAO,IAAI,CAAC,OAAO;AACvB,QAAQ,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC;AACjF,QAAQ2B,SAAO,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,IAAI,EAAE;AACd,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAOA,SAAO,CAAC;AACtC,IAAI,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,GAAG;AACd,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAOA,SAAO,CAAC;AACtC,IAAI,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AACzD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,IAAI,EAAE;AAClB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAOA,SAAO,CAAC;AACtC,IAAI,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACjE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,UAAU,EAAE,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE;AACnD,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAOA,SAAO,CAAC;AACtC,IAAI,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACtF,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE;AACzB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC3C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,KAAK,EAAE;AACtB,IAAI,OAAO,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,GAAG;AACH;;ACxoBA;AACA;AACA;AACe,MAAM,IAAI,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC,WAAW,EAAE;AAC7C,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;AAClE;AACA,IAAI,OAAO,CAAC,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC;AAChF,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,eAAe,CAAC,IAAI,EAAE;AAC/B,IAAI,OAAO,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACtC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,aAAa,CAAC,KAAK,EAAE;AAC9B,IAAI,OAAO,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;AACtD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,EAAE,EAAE;AAC/D,IAAI,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,cAAc,EAAE,CAAC;AAC9D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,yBAAyB,CAAC,EAAE,MAAM,GAAG,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,EAAE,EAAE;AAC1E,IAAI,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,qBAAqB,EAAE,CAAC;AACrE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,kBAAkB,CAAC,EAAE,MAAM,GAAG,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,EAAE,EAAE;AACnE;AACA,IAAI,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,cAAc,EAAE,CAAC,KAAK,EAAE,CAAC;AACtE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,MAAM;AACf,IAAI,MAAM,GAAG,MAAM;AACnB,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,eAAe,GAAG,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,cAAc,GAAG,SAAS,EAAE,GAAG,EAAE;AAC7F,IAAI;AACJ,IAAI,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7F,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,YAAY;AACrB,IAAI,MAAM,GAAG,MAAM;AACnB,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,eAAe,GAAG,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,cAAc,GAAG,SAAS,EAAE,GAAG,EAAE;AAC7F,IAAI;AACJ,IAAI,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACnG,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,QAAQ,CAAC,MAAM,GAAG,MAAM,EAAE,EAAE,MAAM,GAAG,IAAI,EAAE,eAAe,GAAG,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,EAAE,EAAE;AAClG,IAAI,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACrF,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,cAAc;AACvB,IAAI,MAAM,GAAG,MAAM;AACnB,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,eAAe,GAAG,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,EAAE;AACjE,IAAI;AACJ,IAAI,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC3F,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,EAAE,EAAE;AAC3C,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,IAAI,CAAC,MAAM,GAAG,OAAO,EAAE,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,EAAE,EAAE;AACxD,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC/D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,QAAQ,GAAG;AACpB,IAAI,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,EAAE,UAAU,EAAE,iBAAiB,EAAE,EAAE,CAAC;AACxE,GAAG;AACH;;AC1MA,SAAS,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE;AACjC,EAAE,MAAM,WAAW,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE;AAC3F,IAAI,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;AACnD,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACxD,CAAC;AACD;AACA,SAAS,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE;AAC9C,EAAE,MAAM,OAAO,GAAG;AAClB,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AACxC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;AACzE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;AACpE,IAAI;AACJ,MAAM,OAAO;AACb,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK;AAChB,QAAQ,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnC,QAAQ,OAAO,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,OAAO;AACP,KAAK;AACL,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC;AACrB,GAAG,CAAC;AACJ;AACA,EAAE,MAAM,OAAO,GAAG,EAAE,CAAC;AACrB,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC;AACzB,EAAE,IAAI,WAAW,EAAE,SAAS,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,OAAO,EAAE;AACxC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAClC,MAAM,WAAW,GAAG,IAAI,CAAC;AACzB;AACA,MAAM,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC5C,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACxC;AACA,MAAM,IAAI,SAAS,GAAG,KAAK,EAAE;AAC7B;AACA,QAAQ,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;AACxB,QAAQ,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA,QAAQ,IAAI,MAAM,GAAG,KAAK,EAAE;AAC5B;AACA,UAAU,SAAS,GAAG,MAAM,CAAC;AAC7B;AACA,UAAU,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;AAC1B,UAAU,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACzC,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,MAAM,GAAG,SAAS,CAAC;AAC3B,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;AACnD,CAAC;AACD;AACe,aAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;AACtD,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,GAAG,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACxF;AACA,EAAE,MAAM,eAAe,GAAG,KAAK,GAAG,MAAM,CAAC;AACzC;AACA,EAAE,MAAM,eAAe,GAAG,KAAK,CAAC,MAAM;AACtC,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;AAC1E,GAAG,CAAC;AACJ;AACA,EAAE,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;AACpC,IAAI,IAAI,SAAS,GAAG,KAAK,EAAE;AAC3B,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;AACpD,KAAK;AACL;AACA,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,eAAe,IAAI,SAAS,GAAG,MAAM,CAAC,CAAC;AAClG,KAAK;AACL,GAAG;AACH;AACA,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACtD;AACA,EAAE,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;AAClC,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC;AACrD,OAAO,OAAO,CAAC,GAAG,eAAe,CAAC;AAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtB,GAAG,MAAM;AACT,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG;AACH;;ACtFA,MAAM,WAAW,GAAG,mDAAmD,CAAC;AACxE;AACA,SAAS,OAAO,CAAC,KAAK,EAAE,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;AACzC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACzD,CAAC;AACD;AACA,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACtC,MAAM,WAAW,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACjC,MAAM,iBAAiB,GAAG,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AACvD;AACA,SAAS,YAAY,CAAC,CAAC,EAAE;AACzB;AACA;AACA,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;AAC1E,CAAC;AACD;AACA,SAAS,oBAAoB,CAAC,CAAC,EAAE;AACjC,EAAE,OAAO,CAAC;AACV,KAAK,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AACvB,KAAK,OAAO,CAAC,iBAAiB,EAAE,GAAG,CAAC;AACpC,KAAK,WAAW,EAAE,CAAC;AACnB,CAAC;AACD;AACA,SAAS,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE;AACpC,EAAE,IAAI,OAAO,KAAK,IAAI,EAAE;AACxB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,MAAM;AACT,IAAI,OAAO;AACX,MAAM,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxD,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB,QAAQ,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,oBAAoB,CAAC,CAAC,CAAC,KAAK,oBAAoB,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU;AAClG,KAAK,CAAC;AACN,GAAG;AACH,CAAC;AACD;AACA,SAAS,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE;AAC/B,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC;AACpE,CAAC;AACD;AACA,SAAS,MAAM,CAAC,KAAK,EAAE;AACvB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;AACtC,CAAC;AACD;AACA,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;AAC9D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE;AAClC,EAAE,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;AAC7B,IAAI,GAAG,GAAG,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC;AAChC,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC;AAClC,IAAI,IAAI,GAAG,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC;AACjC,IAAI,GAAG,GAAG,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC;AAChC,IAAI,QAAQ,GAAG,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC;AACvC,IAAI,UAAU,GAAG,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC;AACzC,IAAI,QAAQ,GAAG,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC;AACvC,IAAI,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC;AACxC,IAAI,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC;AACxC,IAAI,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC;AACxC,IAAI,OAAO,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAC9F,IAAI,OAAO,GAAG,CAAC,CAAC,KAAK;AACrB,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;AAC1B,OAAO;AACP,MAAM,QAAQ,CAAC,CAAC,GAAG;AACnB;AACA,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7C,QAAQ,KAAK,IAAI;AACjB,UAAU,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C;AACA,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,QAAQ,KAAK,IAAI;AACjB,UAAU,OAAO,OAAO,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;AACpD,QAAQ,KAAK,MAAM;AACnB,UAAU,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC;AAC/B,QAAQ,KAAK,OAAO;AACpB,UAAU,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC;AACpC,QAAQ,KAAK,QAAQ;AACrB,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9B;AACA,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,QAAQ,KAAK,IAAI;AACjB,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9B,QAAQ,KAAK,KAAK;AAClB,UAAU,OAAO,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACrD,QAAQ,KAAK,MAAM;AACnB,UAAU,OAAO,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACpD,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,QAAQ,KAAK,IAAI;AACjB,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9B,QAAQ,KAAK,KAAK;AAClB,UAAU,OAAO,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AACtD,QAAQ,KAAK,MAAM;AACnB,UAAU,OAAO,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AACrD;AACA,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,QAAQ,KAAK,IAAI;AACjB,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9B;AACA,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,OAAO,CAAC,UAAU,CAAC,CAAC;AACrC,QAAQ,KAAK,KAAK;AAClB,UAAU,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;AAChC;AACA,QAAQ,KAAK,IAAI;AACjB,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9B,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,QAAQ,KAAK,IAAI;AACjB,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9B,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,QAAQ,KAAK,IAAI;AACjB,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9B,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,QAAQ,KAAK,IAAI;AACjB,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9B,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,QAAQ,KAAK,IAAI;AACjB,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9B,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,OAAO,CAAC,UAAU,CAAC,CAAC;AACrC,QAAQ,KAAK,KAAK;AAClB,UAAU,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;AAChC,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC;AACnC,QAAQ,KAAK,IAAI;AACjB,UAAU,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;AAClC,QAAQ,KAAK,KAAK;AAClB,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9B;AACA,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC;AAC3C;AACA,QAAQ,KAAK,MAAM;AACnB,UAAU,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC;AAC/B,QAAQ,KAAK,IAAI;AACjB,UAAU,OAAO,OAAO,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;AACpD;AACA,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,QAAQ,KAAK,IAAI;AACjB,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9B;AACA,QAAQ,KAAK,GAAG,CAAC;AACjB,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9B,QAAQ,KAAK,KAAK;AAClB,UAAU,OAAO,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AACxD,QAAQ,KAAK,MAAM;AACnB,UAAU,OAAO,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AACvD,QAAQ,KAAK,KAAK;AAClB,UAAU,OAAO,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACvD,QAAQ,KAAK,MAAM;AACnB,UAAU,OAAO,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACtD;AACA,QAAQ,KAAK,GAAG,CAAC;AACjB,QAAQ,KAAK,IAAI;AACjB,UAAU,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxF,QAAQ,KAAK,KAAK;AAClB,UAAU,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnF;AACA;AACA,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,MAAM,CAAC,oBAAoB,CAAC,CAAC;AAC9C;AACA;AACA,QAAQ,KAAK,GAAG;AAChB,UAAU,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC;AACrC,QAAQ;AACR,UAAU,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;AAC5B,OAAO;AACP,KAAK,CAAC;AACN;AACA,EAAE,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI;AACjC,IAAI,aAAa,EAAE,WAAW;AAC9B,GAAG,CAAC;AACJ;AACA,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACrB;AACA,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD;AACA,MAAM,uBAAuB,GAAG;AAChC,EAAE,IAAI,EAAE;AACR,IAAI,SAAS,EAAE,IAAI;AACnB,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,SAAS,EAAE,IAAI;AACnB,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,SAAS,EAAE,IAAI;AACnB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,SAAS,EAAE,GAAG;AAChB,EAAE,SAAS,EAAE,GAAG;AAChB,EAAE,MAAM,EAAE;AACV,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,SAAS,EAAE,IAAI;AACnB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,SAAS,EAAE,IAAI;AACnB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,SAAS,EAAE,IAAI;AACnB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,SAAS,EAAE,IAAI;AACnB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,KAAK,EAAE,KAAK;AAChB,GAAG;AACH,CAAC,CAAC;AACF;AACA,SAAS,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE;AACtD,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;AAC/B;AACA,EAAE,IAAI,IAAI,KAAK,SAAS,EAAE;AAC1B,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxC,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,CAAC,OAAO;AACvB,MAAM,GAAG,EAAE,OAAO,GAAG,GAAG,GAAG,KAAK;AAChC,KAAK,CAAC;AACN,GAAG;AACH;AACA,EAAE,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AACjC;AACA;AACA;AACA;AACA,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC;AACxB,EAAE,IAAI,IAAI,KAAK,MAAM,EAAE;AACvB,IAAI,IAAI,UAAU,CAAC,MAAM,IAAI,IAAI,EAAE;AACnC,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC3D,KAAK,MAAM,IAAI,UAAU,CAAC,SAAS,IAAI,IAAI,EAAE;AAC7C,MAAM,IAAI,UAAU,CAAC,SAAS,KAAK,KAAK,IAAI,UAAU,CAAC,SAAS,KAAK,KAAK,EAAE;AAC5E,QAAQ,UAAU,GAAG,QAAQ,CAAC;AAC9B,OAAO,MAAM;AACb,QAAQ,UAAU,GAAG,QAAQ,CAAC;AAC9B,OAAO;AACP,KAAK,MAAM;AACX;AACA;AACA,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC7D,KAAK;AACL,GAAG;AACH,EAAE,IAAI,GAAG,GAAG,uBAAuB,CAAC,UAAU,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAC/B,IAAI,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AACrB,GAAG;AACH;AACA,EAAE,IAAI,GAAG,EAAE;AACX,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,GAAG;AACT,KAAK,CAAC;AACN,GAAG;AACH;AACA,EAAE,OAAO,SAAS,CAAC;AACnB,CAAC;AACD;AACA,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACjF,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC5B,CAAC;AACD;AACA,SAAS,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE;AACvC,EAAE,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACrC;AACA,EAAE,IAAI,OAAO,EAAE;AACf,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;AACnB,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC;AACvB,IAAI,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE;AAC9B,MAAM,IAAI,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE;AACvC,QAAQ,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;AAC7B,UAAU,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AAC/C,QAAQ,IAAI,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,KAAK,EAAE;AACnC,UAAU,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC;AACxF,SAAS;AACT,QAAQ,UAAU,IAAI,MAAM,CAAC;AAC7B,OAAO;AACP,KAAK;AACL,IAAI,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC1B,GAAG,MAAM;AACT,IAAI,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AACzB,GAAG;AACH,CAAC;AACD;AACA,SAAS,mBAAmB,CAAC,OAAO,EAAE;AACtC,EAAE,MAAM,OAAO,GAAG,CAAC,KAAK,KAAK;AAC7B,IAAI,QAAQ,KAAK;AACjB,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,aAAa,CAAC;AAC7B,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,QAAQ,CAAC;AACxB,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,QAAQ,CAAC;AACxB,MAAM,KAAK,GAAG,CAAC;AACf,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,MAAM,CAAC;AACtB,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,KAAK,CAAC;AACrB,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,SAAS,CAAC;AACzB,MAAM,KAAK,GAAG,CAAC;AACf,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,OAAO,CAAC;AACvB,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,MAAM,CAAC;AACtB,MAAM,KAAK,GAAG,CAAC;AACf,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,SAAS,CAAC;AACzB,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,YAAY,CAAC;AAC5B,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,UAAU,CAAC;AAC1B,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,SAAS,CAAC;AACzB,MAAM;AACN,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,GAAG,CAAC;AACJ;AACA,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC;AAClB,EAAE,IAAI,cAAc,CAAC;AACrB,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AAC/B,IAAI,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACtC,GAAG;AACH;AACA,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AAC/B,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,IAAI,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC;AAC/B,GAAG;AACH;AACA,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AAC/B,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxC,GAAG;AACH;AACA,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AAC/B,IAAI,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE;AAC3C,MAAM,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;AACtB,KAAK,MAAM,IAAI,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE;AACpD,MAAM,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;AACpB,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE;AACpC,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;AAC3B,GAAG;AACH;AACA,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AAC/B,IAAI,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACvC,GAAG;AACH;AACA,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AACrD,IAAI,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,EAAE;AACX,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AACxB,KAAK;AACL;AACA,IAAI,OAAO,CAAC,CAAC;AACb,GAAG,EAAE,EAAE,CAAC,CAAC;AACT;AACA,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;AACtC,CAAC;AACD;AACA,IAAI,kBAAkB,GAAG,IAAI,CAAC;AAC9B;AACA,SAAS,gBAAgB,GAAG;AAC5B,EAAE,IAAI,CAAC,kBAAkB,EAAE;AAC3B,IAAI,kBAAkB,GAAG,QAAQ,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;AAC5D,GAAG;AACH;AACA,EAAE,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AACD;AACA,SAAS,qBAAqB,CAAC,KAAK,EAAE,MAAM,EAAE;AAC9C,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA,EAAE,MAAM,UAAU,GAAG,SAAS,CAAC,sBAAsB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACjE,EAAE,MAAM,MAAM,GAAG,kBAAkB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AACxD;AACA,EAAE,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AACpD,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD;AACO,SAAS,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE;AAClD,EAAE,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,qBAAqB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;AACxF,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,MAAM,WAAW,CAAC;AACzB,EAAE,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE;AAC9B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,MAAM,GAAG,iBAAiB,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3E,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,YAAY,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AACjE,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,CAAC;AACrE;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;AACjC,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7D,MAAM,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AAC5C,MAAM,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC/B,KAAK;AACL,GAAG;AACH;AACA,EAAE,iBAAiB,CAAC,KAAK,EAAE;AAC3B,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC;AAC/E,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC;AAC3E,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE,cAAc,CAAC,GAAG,OAAO;AAChD,YAAY,mBAAmB,CAAC,OAAO,CAAC;AACxC,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AACpC,MAAM,IAAI,cAAc,CAAC,OAAO,EAAE,GAAG,CAAC,IAAI,cAAc,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;AACxE,QAAQ,MAAM,IAAI,6BAA6B;AAC/C,UAAU,uDAAuD;AACjE,SAAS,CAAC;AACV,OAAO;AACP,MAAM,OAAO;AACb,QAAQ,KAAK;AACb,QAAQ,MAAM,EAAE,IAAI,CAAC,MAAM;AAC3B,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK;AACzB,QAAQ,UAAU;AAClB,QAAQ,OAAO;AACf,QAAQ,MAAM;AACd,QAAQ,IAAI;AACZ,QAAQ,cAAc;AACtB,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC;AACnC,GAAG;AACH;AACA,EAAE,IAAI,aAAa,GAAG;AACtB,IAAI,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,GAAG,IAAI,CAAC;AAChF,GAAG;AACH,CAAC;AACD;AACO,SAAS,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;AACzD,EAAE,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACjD,EAAE,OAAO,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;AACzC,CAAC;AACD;AACO,SAAS,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;AACvD,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACnG,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC;AACvD,CAAC;AACD;AACO,SAAS,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE;AACvD,EAAE,IAAI,CAAC,UAAU,EAAE;AACnB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA,EAAE,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AACzD,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC,CAAC;AACvD,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC;AACnC,EAAE,MAAM,YAAY,GAAG,EAAE,CAAC,eAAe,EAAE,CAAC;AAC5C,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,YAAY,CAAC,CAAC,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC;AACrE;;ACncA,MAAM,OAAO,GAAG,kBAAkB,CAAC;AACnC,MAAM,QAAQ,GAAG,OAAO,CAAC;AACzB;AACA,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,OAAO,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;AACrF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,sBAAsB,CAAC,EAAE,EAAE;AACpC,EAAE,IAAI,EAAE,CAAC,QAAQ,KAAK,IAAI,EAAE;AAC5B,IAAI,EAAE,CAAC,QAAQ,GAAG,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxC,GAAG;AACH,EAAE,OAAO,EAAE,CAAC,QAAQ,CAAC;AACrB,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,2BAA2B,CAAC,EAAE,EAAE;AACzC,EAAE,IAAI,EAAE,CAAC,aAAa,KAAK,IAAI,EAAE;AACjC,IAAI,EAAE,CAAC,aAAa,GAAG,eAAe;AACtC,MAAM,EAAE,CAAC,CAAC;AACV,MAAM,EAAE,CAAC,GAAG,CAAC,qBAAqB,EAAE;AACpC,MAAM,EAAE,CAAC,GAAG,CAAC,cAAc,EAAE;AAC7B,KAAK,CAAC;AACN,GAAG;AACH,EAAE,OAAO,EAAE,CAAC,aAAa,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA,SAAS,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE;AAC3B,EAAE,MAAM,OAAO,GAAG;AAClB,IAAI,EAAE,EAAE,IAAI,CAAC,EAAE;AACf,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI;AACnB,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACb,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACb,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG;AACjB,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO;AACzB,GAAG,CAAC;AACJ,EAAE,OAAO,IAAI,QAAQ,CAAC,EAAE,GAAG,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC;AAC7D,CAAC;AACD;AACA;AACA;AACA,SAAS,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE;AACnC;AACA,EAAE,IAAI,QAAQ,GAAG,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;AACzC;AACA;AACA,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACjC;AACA;AACA,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE;AAChB,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AACzB,GAAG;AACH;AACA;AACA,EAAE,QAAQ,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC;AACnC;AACA;AACA,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACjC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;AACjB,IAAI,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;AAC1B,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AACpE,CAAC;AACD;AACA;AACA,SAAS,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE;AAC7B,EAAE,EAAE,IAAI,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC;AAC3B;AACA,EAAE,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AACzB;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,CAAC,CAAC,cAAc,EAAE;AAC5B,IAAI,KAAK,EAAE,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC;AAC9B,IAAI,GAAG,EAAE,CAAC,CAAC,UAAU,EAAE;AACvB,IAAI,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE;AACzB,IAAI,MAAM,EAAE,CAAC,CAAC,aAAa,EAAE;AAC7B,IAAI,MAAM,EAAE,CAAC,CAAC,aAAa,EAAE;AAC7B,IAAI,WAAW,EAAE,CAAC,CAAC,kBAAkB,EAAE;AACvC,GAAG,CAAC;AACJ,CAAC;AACD;AACA;AACA,SAAS,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE;AACpC,EAAE,OAAO,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACpD,CAAC;AACD;AACA;AACA,SAAS,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE;AAC/B,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC;AACrB,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;AAC9C,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;AAChF,IAAI,CAAC,GAAG;AACR,MAAM,GAAG,IAAI,CAAC,CAAC;AACf,MAAM,IAAI;AACV,MAAM,KAAK;AACX,MAAM,GAAG;AACT,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACtD,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;AAC5B,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC;AACjC,KAAK;AACL,IAAI,WAAW,GAAG,QAAQ,CAAC,UAAU,CAAC;AACtC,MAAM,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;AAC9C,MAAM,QAAQ,EAAE,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;AACvD,MAAM,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC;AACjD,MAAM,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;AAC9C,MAAM,IAAI,EAAE,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;AAC3C,MAAM,KAAK,EAAE,GAAG,CAAC,KAAK;AACtB,MAAM,OAAO,EAAE,GAAG,CAAC,OAAO;AAC1B,MAAM,OAAO,EAAE,GAAG,CAAC,OAAO;AAC1B,MAAM,YAAY,EAAE,GAAG,CAAC,YAAY;AACpC,KAAK,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC;AACzB,IAAI,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AAC9B;AACA,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACpD;AACA,EAAE,IAAI,WAAW,KAAK,CAAC,EAAE;AACzB,IAAI,EAAE,IAAI,WAAW,CAAC;AACtB;AACA,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;AACnB,CAAC;AACD;AACA;AACA;AACA,SAAS,mBAAmB,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE;AACrF,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;AACjC,EAAE,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,KAAK,UAAU,EAAE;AAClE,IAAI,MAAM,kBAAkB,GAAG,UAAU,IAAI,IAAI;AACjD,MAAM,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE;AACzC,QAAQ,GAAG,IAAI;AACf,QAAQ,IAAI,EAAE,kBAAkB;AAChC,QAAQ,cAAc;AACtB,OAAO,CAAC,CAAC;AACT,IAAI,OAAO,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC/C,GAAG,MAAM;AACT,IAAI,OAAO,QAAQ,CAAC,OAAO;AAC3B,MAAM,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC,CAAC;AACnF,KAAK,CAAC;AACN,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA,SAAS,YAAY,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI,EAAE;AACjD,EAAE,OAAO,EAAE,CAAC,OAAO;AACnB,MAAM,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;AAC/C,QAAQ,MAAM;AACd,QAAQ,WAAW,EAAE,IAAI;AACzB,OAAO,CAAC,CAAC,wBAAwB,CAAC,EAAE,EAAE,MAAM,CAAC;AAC7C,MAAM,IAAI,CAAC;AACX,CAAC;AACD;AACA,SAAS,SAAS,CAAC,CAAC,EAAE,QAAQ,EAAE;AAChC,EAAE,MAAM,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;AACrD,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;AACb,EAAE,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC;AAC5C,EAAE,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9C;AACA,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,CAAC,IAAI,GAAG,CAAC;AACb,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAC7B,IAAI,CAAC,IAAI,GAAG,CAAC;AACb,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,GAAG,MAAM;AACT,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAC7B,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,GAAG;AACH,EAAE,OAAO,CAAC,CAAC;AACX,CAAC;AACD;AACA,SAAS,SAAS;AAClB,EAAE,CAAC;AACH,EAAE,QAAQ;AACV,EAAE,eAAe;AACjB,EAAE,oBAAoB;AACtB,EAAE,aAAa;AACf,EAAE,YAAY;AACd,EAAE;AACF,EAAE,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,CAAC,IAAI,GAAG,CAAC;AACb,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE;AACvE,MAAM,CAAC,IAAI,GAAG,CAAC;AACf,KAAK;AACL,GAAG,MAAM;AACT,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AAC9B,GAAG;AACH;AACA,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE;AACrE,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AAC9B;AACA,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,KAAK,CAAC,IAAI,CAAC,oBAAoB,EAAE;AACxD,MAAM,CAAC,IAAI,GAAG,CAAC;AACf,MAAM,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;AACxC,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,aAAa,EAAE;AACrB,IAAI,IAAI,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE;AAC5D,MAAM,CAAC,IAAI,GAAG,CAAC;AACf,KAAK,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AACxB,MAAM,CAAC,IAAI,GAAG,CAAC;AACf,MAAM,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC3C,MAAM,CAAC,IAAI,GAAG,CAAC;AACf,MAAM,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC3C,KAAK,MAAM;AACX,MAAM,CAAC,IAAI,GAAG,CAAC;AACf,MAAM,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC1C,MAAM,CAAC,IAAI,GAAG,CAAC;AACf,MAAM,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC1C,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,YAAY,EAAE;AACpB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACrC,GAAG;AACH,EAAE,OAAO,CAAC,CAAC;AACX,CAAC;AACD;AACA;AACA,MAAM,iBAAiB,GAAG;AAC1B,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,GAAG,EAAE,CAAC;AACV,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,EAAE,CAAC;AACb,IAAI,MAAM,EAAE,CAAC;AACb,IAAI,WAAW,EAAE,CAAC;AAClB,GAAG;AACH,EAAE,qBAAqB,GAAG;AAC1B,IAAI,UAAU,EAAE,CAAC;AACjB,IAAI,OAAO,EAAE,CAAC;AACd,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,EAAE,CAAC;AACb,IAAI,MAAM,EAAE,CAAC;AACb,IAAI,WAAW,EAAE,CAAC;AAClB,GAAG;AACH,EAAE,wBAAwB,GAAG;AAC7B,IAAI,OAAO,EAAE,CAAC;AACd,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,EAAE,CAAC;AACb,IAAI,MAAM,EAAE,CAAC;AACb,IAAI,WAAW,EAAE,CAAC;AAClB,GAAG,CAAC;AACJ;AACA;AACA,MAAM,YAAY,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC;AACxF,EAAE,gBAAgB,GAAG;AACrB,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,MAAM;AACV,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,GAAG;AACH,EAAE,mBAAmB,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;AACvF;AACA;AACA,SAAS,aAAa,CAAC,IAAI,EAAE;AAC7B,EAAE,MAAM,UAAU,GAAG;AACrB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,KAAK,EAAE,MAAM;AACjB,IAAI,KAAK,EAAE,OAAO;AAClB,IAAI,MAAM,EAAE,OAAO;AACnB,IAAI,GAAG,EAAE,KAAK;AACd,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,KAAK,EAAE,MAAM;AACjB,IAAI,MAAM,EAAE,QAAQ;AACpB,IAAI,OAAO,EAAE,QAAQ;AACrB,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,QAAQ,EAAE,SAAS;AACvB,IAAI,MAAM,EAAE,QAAQ;AACpB,IAAI,OAAO,EAAE,QAAQ;AACrB,IAAI,WAAW,EAAE,aAAa;AAC9B,IAAI,YAAY,EAAE,aAAa;AAC/B,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,QAAQ,EAAE,SAAS;AACvB,IAAI,UAAU,EAAE,YAAY;AAC5B,IAAI,WAAW,EAAE,YAAY;AAC7B,IAAI,WAAW,EAAE,YAAY;AAC7B,IAAI,QAAQ,EAAE,UAAU;AACxB,IAAI,SAAS,EAAE,UAAU;AACzB,IAAI,OAAO,EAAE,SAAS;AACtB,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AACxB;AACA,EAAE,IAAI,CAAC,UAAU,EAAE,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;AACpD;AACA,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC;AACD;AACA,SAAS,2BAA2B,CAAC,IAAI,EAAE;AAC3C,EAAE,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC5B,IAAI,KAAK,cAAc,CAAC;AACxB,IAAI,KAAK,eAAe;AACxB,MAAM,OAAO,cAAc,CAAC;AAC5B,IAAI,KAAK,iBAAiB,CAAC;AAC3B,IAAI,KAAK,kBAAkB;AAC3B,MAAM,OAAO,iBAAiB,CAAC;AAC/B,IAAI,KAAK,eAAe,CAAC;AACzB,IAAI,KAAK,gBAAgB;AACzB,MAAM,OAAO,eAAe,CAAC;AAC7B,IAAI;AACJ,MAAM,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;AACjC,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,IAAI,EAAE;AAClC,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;AACnC,IAAI,IAAI,YAAY,KAAK,SAAS,EAAE;AACpC,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;AACpC,KAAK;AACL;AACA,IAAI,oBAAoB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAC3D,GAAG;AACH,EAAE,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAC;AACpC,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE;AAC5B,EAAE,MAAM,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC9D,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;AACnD,GAAG;AACH;AACA,EAAE,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACtC;AACA,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AACZ;AACA;AACA,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC9B,IAAI,KAAK,MAAM,CAAC,IAAI,YAAY,EAAE;AAClC,MAAM,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;AAC/B,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;AACtC,OAAO;AACP,KAAK;AACL;AACA,IAAI,MAAM,OAAO,GAAG,uBAAuB,CAAC,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC5E,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACvC,KAAK;AACL;AACA,IAAI,MAAM,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAClD,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;AAC/C,GAAG,MAAM;AACT,IAAI,EAAE,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;AACxB,GAAG;AACH;AACA,EAAE,OAAO,IAAI,QAAQ,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5C,CAAC;AACD;AACA,SAAS,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE;AACxC,EAAE,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK;AAC3D,IAAI,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,KAAK;AAC1B,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AAC5D,MAAM,MAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAC/D,MAAM,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACvC,KAAK;AACL,IAAI,MAAM,GAAG,CAAC,IAAI,KAAK;AACvB,MAAM,IAAI,IAAI,CAAC,SAAS,EAAE;AAC1B,QAAQ,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;AACvC,UAAU,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7E,SAAS,MAAM,OAAO,CAAC,CAAC;AACxB,OAAO,MAAM;AACb,QAAQ,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/C,OAAO;AACP,KAAK,CAAC;AACN;AACA,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;AACjB,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAChD,GAAG;AACH;AACA,EAAE,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;AACjC,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC/B,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AAC9B,MAAM,OAAO,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACjC,KAAK;AACL,GAAG;AACH,EAAE,OAAO,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC;AACD;AACA,SAAS,QAAQ,CAAC,OAAO,EAAE;AAC3B,EAAE,IAAI,IAAI,GAAG,EAAE;AACf,IAAI,IAAI,CAAC;AACT,EAAE,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;AAC7E,IAAI,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACvC,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5D,GAAG,MAAM;AACT,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC/B,GAAG;AACH,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACtB,CAAC;AACD;AACA;AACA;AACA;AACA,IAAI,YAAY,CAAC;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oBAAoB,GAAG,EAAE,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAM,QAAQ,CAAC;AAC9B;AACA;AACA;AACA,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,QAAQ,CAAC,WAAW,CAAC;AACrD;AACA,IAAI,IAAI,OAAO;AACf,MAAM,MAAM,CAAC,OAAO;AACpB,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,OAAO,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;AACrE,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACrD;AACA;AACA;AACA,IAAI,IAAI,CAAC,EAAE,GAAG,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAClE;AACA,IAAI,IAAI,CAAC,GAAG,IAAI;AAChB,MAAM,CAAC,GAAG,IAAI,CAAC;AACf,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAChG;AACA,MAAM,IAAI,SAAS,EAAE;AACrB,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9C,OAAO,MAAM;AACb;AACA;AACA,QAAQ,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACvF,QAAQ,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACjC,QAAQ,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,OAAO,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;AAC7E,QAAQ,CAAC,GAAG,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC;AAC/B,QAAQ,CAAC,GAAG,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;AAChC,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB;AACA;AACA;AACA,IAAI,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;AAC7C;AACA;AACA;AACA,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B;AACA;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB;AACA;AACA;AACA,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B;AACA;AACA;AACA,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACf;AACA;AACA;AACA,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACf;AACA;AACA;AACA,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG,GAAG;AACf,IAAI,OAAO,IAAI,QAAQ,CAAC,EAAE,CAAC,CAAC;AAC5B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,KAAK,GAAG;AACjB,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC;AAC5C,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,GAAG,IAAI,CAAC;AACnE,IAAI,OAAO,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC;AAClF,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG,GAAG;AACf,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC;AAC5C,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,GAAG,IAAI,CAAC;AACnE;AACA,IAAI,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC,WAAW,CAAC;AAC5C,IAAI,OAAO,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC;AAClF,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,UAAU,CAAC,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE;AACxC,IAAI,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC;AACnD,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;AAC1B,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;AAC/C,KAAK;AACL;AACA,IAAI,MAAM,SAAS,GAAG,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;AACxE,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;AAC5B,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC;AAC1D,KAAK;AACL;AACA,IAAI,OAAO,IAAI,QAAQ,CAAC;AACxB,MAAM,EAAE,EAAE,EAAE;AACZ,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,GAAG,EAAE,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC;AACrC,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,UAAU,CAAC,YAAY,EAAE,OAAO,GAAG,EAAE,EAAE;AAChD,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;AACjC,MAAM,MAAM,IAAI,oBAAoB;AACpC,QAAQ,CAAC,sDAAsD,EAAE,OAAO,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;AACjH,OAAO,CAAC;AACR,KAAK,MAAM,IAAI,YAAY,GAAG,CAAC,QAAQ,IAAI,YAAY,GAAG,QAAQ,EAAE;AACpE;AACA,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AACxD,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,QAAQ,CAAC;AAC1B,QAAQ,EAAE,EAAE,YAAY;AACxB,QAAQ,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC;AAC/D,QAAQ,GAAG,EAAE,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC;AACvC,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,WAAW,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,EAAE;AAC5C,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AAC5B,MAAM,MAAM,IAAI,oBAAoB,CAAC,wCAAwC,CAAC,CAAC;AAC/E,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,QAAQ,CAAC;AAC1B,QAAQ,EAAE,EAAE,OAAO,GAAG,IAAI;AAC1B,QAAQ,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC;AAC/D,QAAQ,GAAG,EAAE,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC;AACvC,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,UAAU,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE;AACpC,IAAI,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;AACpB,IAAI,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;AAC5B,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC;AAC1D,KAAK;AACL;AACA,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACxC,IAAI,MAAM,UAAU,GAAG,eAAe,CAAC,GAAG,EAAE,2BAA2B,CAAC,CAAC;AACzE,IAAI,MAAM,EAAE,kBAAkB,EAAE,WAAW,EAAE,GAAG,mBAAmB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AACrF;AACA,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAE;AAChC,MAAM,YAAY,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC;AACtD,UAAU,IAAI,CAAC,cAAc;AAC7B,UAAU,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC;AACjC,MAAM,eAAe,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC;AACxD,MAAM,kBAAkB,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC;AACxD,MAAM,gBAAgB,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC;AACvF,MAAM,cAAc,GAAG,kBAAkB,IAAI,gBAAgB;AAC7D,MAAM,eAAe,GAAG,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,UAAU,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,cAAc,IAAI,eAAe,KAAK,eAAe,EAAE;AAChE,MAAM,MAAM,IAAI,6BAA6B;AAC7C,QAAQ,qEAAqE;AAC7E,OAAO,CAAC;AACR,KAAK;AACL;AACA,IAAI,IAAI,gBAAgB,IAAI,eAAe,EAAE;AAC7C,MAAM,MAAM,IAAI,6BAA6B,CAAC,wCAAwC,CAAC,CAAC;AACxF,KAAK;AACL;AACA,IAAI,MAAM,WAAW,GAAG,eAAe,KAAK,UAAU,CAAC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC;AACnF;AACA;AACA,IAAI,IAAI,KAAK;AACb,MAAM,aAAa;AACnB,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAC5C,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,KAAK,GAAG,gBAAgB,CAAC;AAC/B,MAAM,aAAa,GAAG,qBAAqB,CAAC;AAC5C,MAAM,MAAM,GAAG,eAAe,CAAC,MAAM,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC;AACxE,KAAK,MAAM,IAAI,eAAe,EAAE;AAChC,MAAM,KAAK,GAAG,mBAAmB,CAAC;AAClC,MAAM,aAAa,GAAG,wBAAwB,CAAC;AAC/C,MAAM,MAAM,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;AAC1C,KAAK,MAAM;AACX,MAAM,KAAK,GAAG,YAAY,CAAC;AAC3B,MAAM,aAAa,GAAG,iBAAiB,CAAC;AACxC,KAAK;AACL;AACA;AACA,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;AAC3B,MAAM,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAC9B,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;AAC3B,QAAQ,UAAU,GAAG,IAAI,CAAC;AAC1B,OAAO,MAAM,IAAI,UAAU,EAAE;AAC7B,QAAQ,UAAU,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;AACzC,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAClC,OAAO;AACP,KAAK;AACL;AACA;AACA,IAAI,MAAM,kBAAkB,GAAG,WAAW;AAC1C,UAAU,kBAAkB,CAAC,UAAU,EAAE,kBAAkB,EAAE,WAAW,CAAC;AACzE,UAAU,eAAe;AACzB,UAAU,qBAAqB,CAAC,UAAU,CAAC;AAC3C,UAAU,uBAAuB,CAAC,UAAU,CAAC;AAC7C,MAAM,OAAO,GAAG,kBAAkB,IAAI,kBAAkB,CAAC,UAAU,CAAC,CAAC;AACrE;AACA,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACvC,KAAK;AACL;AACA;AACA,IAAI,MAAM,SAAS,GAAG,WAAW;AACjC,UAAU,eAAe,CAAC,UAAU,EAAE,kBAAkB,EAAE,WAAW,CAAC;AACtE,UAAU,eAAe;AACzB,UAAU,kBAAkB,CAAC,UAAU,CAAC;AACxC,UAAU,UAAU;AACpB,MAAM,CAAC,OAAO,EAAE,WAAW,CAAC,GAAG,OAAO,CAAC,SAAS,EAAE,YAAY,EAAE,SAAS,CAAC;AAC1E,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC;AAC1B,QAAQ,EAAE,EAAE,OAAO;AACnB,QAAQ,IAAI,EAAE,SAAS;AACvB,QAAQ,CAAC,EAAE,WAAW;AACtB,QAAQ,GAAG;AACX,OAAO,CAAC,CAAC;AACT;AACA;AACA,IAAI,IAAI,UAAU,CAAC,OAAO,IAAI,cAAc,IAAI,GAAG,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;AAC9E,MAAM,OAAO,QAAQ,CAAC,OAAO;AAC7B,QAAQ,oBAAoB;AAC5B,QAAQ,CAAC,oCAAoC,EAAE,UAAU,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;AACjG,OAAO,CAAC;AACR,KAAK;AACL;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5C,KAAK;AACL;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,OAAO,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,EAAE;AAClC,IAAI,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;AAClD,IAAI,OAAO,mBAAmB,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;AACzE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,EAAE;AACtC,IAAI,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;AACtD,IAAI,OAAO,mBAAmB,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;AACzE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,EAAE;AACnC,IAAI,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;AACnD,IAAI,OAAO,mBAAmB,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACrE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE;AAC1C,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;AAC/C,MAAM,MAAM,IAAI,oBAAoB,CAAC,kDAAkD,CAAC,CAAC;AACzF,KAAK;AACL;AACA,IAAI,MAAM,EAAE,MAAM,GAAG,IAAI,EAAE,eAAe,GAAG,IAAI,EAAE,GAAG,IAAI;AAC1D,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC;AACpC,QAAQ,MAAM;AACd,QAAQ,eAAe;AACvB,QAAQ,WAAW,EAAE,IAAI;AACzB,OAAO,CAAC;AACR,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,OAAO,CAAC,GAAG,eAAe,CAAC,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AAC5F,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACvC,KAAK,MAAM;AACX,MAAM,OAAO,mBAAmB,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;AAChG,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,OAAO,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE;AAC1C,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAChD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,OAAO,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,EAAE;AAClC,IAAI,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC9C,IAAI,OAAO,mBAAmB,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACpE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,OAAO,CAAC,MAAM,EAAE,WAAW,GAAG,IAAI,EAAE;AAC7C,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,IAAI,oBAAoB,CAAC,kDAAkD,CAAC,CAAC;AACzF,KAAK;AACL;AACA,IAAI,MAAM,OAAO,GAAG,MAAM,YAAY,OAAO,GAAG,MAAM,GAAG,IAAI,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAC1F;AACA,IAAI,IAAI,QAAQ,CAAC,cAAc,EAAE;AACjC,MAAM,MAAM,IAAI,oBAAoB,CAAC,OAAO,CAAC,CAAC;AAC9C,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,QAAQ,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;AACvC,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,UAAU,CAAC,CAAC,EAAE;AACvB,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,eAAe,KAAK,KAAK,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,kBAAkB,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,EAAE;AACzD,IAAI,MAAM,SAAS,GAAG,kBAAkB,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;AACpF,IAAI,OAAO,CAAC,SAAS,GAAG,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACjF,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,YAAY,CAAC,GAAG,EAAE,UAAU,GAAG,EAAE,EAAE;AAC5C,IAAI,MAAM,QAAQ,GAAG,iBAAiB,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;AAClG,IAAI,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC/C,GAAG;AACH;AACA,EAAE,OAAO,UAAU,GAAG;AACtB,IAAI,YAAY,GAAG,SAAS,CAAC;AAC7B,IAAI,oBAAoB,GAAG,EAAE,CAAC;AAC9B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,GAAG,CAAC,IAAI,EAAE;AACZ,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC;AACjC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,aAAa,GAAG;AACtB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;AACrD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,kBAAkB,GAAG;AAC3B,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC;AACjD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,eAAe,GAAG;AACxB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC;AAC1D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,cAAc,GAAG;AACvB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC;AACzD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,QAAQ,GAAG;AACjB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AAChD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC;AAC5C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AAC5D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,GAAG,GAAG;AACZ,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC;AAC3C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC;AAC5C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC;AAC9C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC;AAC9C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,WAAW,GAAG,GAAG,CAAC;AACnD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,QAAQ,GAAG;AACjB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC,QAAQ,GAAG,GAAG,CAAC;AACtE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,UAAU,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC,UAAU,GAAG,GAAG,CAAC;AACxE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC;AACrE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5E,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,2BAA2B,CAAC,IAAI,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC;AAC1E,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,eAAe,GAAG;AACxB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,2BAA2B,CAAC,IAAI,CAAC,CAAC,UAAU,GAAG,GAAG,CAAC;AAC7E,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,aAAa,GAAG;AACtB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,2BAA2B,CAAC,IAAI,CAAC,CAAC,QAAQ,GAAG,GAAG,CAAC;AAC3E,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC;AACnE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,UAAU,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAC5F,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAC3F,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAChG,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAC/F,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;AACxC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,eAAe,GAAG;AACxB,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AACtB,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE;AAC3C,QAAQ,MAAM,EAAE,OAAO;AACvB,QAAQ,MAAM,EAAE,IAAI,CAAC,MAAM;AAC3B,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,cAAc,GAAG;AACvB,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AACtB,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE;AAC3C,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,MAAM,EAAE,IAAI,CAAC,MAAM;AAC3B,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,aAAa,GAAG;AACtB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACvD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;AAC5B,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK,MAAM;AACX,MAAM;AACN,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM;AAC3D,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM;AACnD,QAAQ;AACR,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,GAAG;AACvB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,EAAE;AAC7C,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;AACpB,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC;AAC3B,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC;AAC3B,IAAI,MAAM,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC;AACvD,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC;AACrD;AACA,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC;AAC/D,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,MAAM,GAAG,QAAQ,CAAC,CAAC;AAC7D,IAAI,IAAI,EAAE,KAAK,EAAE,EAAE;AACnB,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;AACpB,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,OAAO,GAAG,EAAE,GAAG,QAAQ,CAAC;AACxC,IAAI,MAAM,GAAG,GAAG,OAAO,GAAG,EAAE,GAAG,QAAQ,CAAC;AACxC,IAAI,MAAM,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AAChC,IAAI,MAAM,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AAChC,IAAI;AACJ,MAAM,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI;AACzB,MAAM,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,MAAM;AAC7B,MAAM,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,MAAM;AAC7B,MAAM,EAAE,CAAC,WAAW,KAAK,EAAE,CAAC,WAAW;AACvC,MAAM;AACN,MAAM,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAClE,KAAK;AACL,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;AAClB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAC9C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,UAAU,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;AACtD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,eAAe,GAAG;AACxB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;AAC/D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,oBAAoB,GAAG;AAC7B,IAAI,OAAO,IAAI,CAAC,OAAO;AACvB,QAAQ,eAAe;AACvB,UAAU,IAAI,CAAC,aAAa;AAC5B,UAAU,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE;AAC1C,UAAU,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE;AACnC,SAAS;AACT,QAAQ,GAAG,CAAC;AACZ,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,qBAAqB,CAAC,IAAI,GAAG,EAAE,EAAE;AACnC,IAAI,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAC,MAAM;AAClE,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;AAC1B,MAAM,IAAI;AACV,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AAC5B,IAAI,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,QAAQ,EAAE,CAAC;AACjE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,GAAG,EAAE,EAAE;AAC/B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;AAChE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC9C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,aAAa,GAAG,KAAK,EAAE,gBAAgB,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE;AAC1E,IAAI,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;AACrD,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAChC,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAC9B,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;AACrD,KAAK,MAAM;AACX,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC;AAC1B,MAAM,IAAI,aAAa,IAAI,gBAAgB,EAAE;AAC7C,QAAQ,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACjD,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AACtC,QAAQ,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AACpD,OAAO;AACP,MAAM,OAAO,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;AAC9C,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,EAAE,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,GAAG,EAAE,EAAE;AAChE,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,CAAC,CAAC;AAC5E,IAAI,OAAO,KAAK,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;AAChC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,MAAM,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;AACxC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,GAAG,CAAC,MAAM,EAAE;AACd,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC;AACA,IAAI,MAAM,UAAU,GAAG,eAAe,CAAC,MAAM,EAAE,2BAA2B,CAAC,CAAC;AAC5E,IAAI,MAAM,EAAE,kBAAkB,EAAE,WAAW,EAAE,GAAG,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1F;AACA,IAAI,MAAM,gBAAgB;AAC1B,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC;AACzC,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC;AAC3C,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC;AACxC,MAAM,eAAe,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC;AACxD,MAAM,kBAAkB,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC;AACxD,MAAM,gBAAgB,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC;AACvF,MAAM,cAAc,GAAG,kBAAkB,IAAI,gBAAgB;AAC7D,MAAM,eAAe,GAAG,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,UAAU,CAAC;AACrE;AACA,IAAI,IAAI,CAAC,cAAc,IAAI,eAAe,KAAK,eAAe,EAAE;AAChE,MAAM,MAAM,IAAI,6BAA6B;AAC7C,QAAQ,qEAAqE;AAC7E,OAAO,CAAC;AACR,KAAK;AACL;AACA,IAAI,IAAI,gBAAgB,IAAI,eAAe,EAAE;AAC7C,MAAM,MAAM,IAAI,6BAA6B,CAAC,wCAAwC,CAAC,CAAC;AACxF,KAAK;AACL;AACA,IAAI,IAAI,KAAK,CAAC;AACd,IAAI,IAAI,gBAAgB,EAAE;AAC1B,MAAM,KAAK,GAAG,eAAe;AAC7B,QAAQ,EAAE,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,EAAE,kBAAkB,EAAE,WAAW,CAAC,EAAE,GAAG,UAAU,EAAE;AACtF,QAAQ,kBAAkB;AAC1B,QAAQ,WAAW;AACnB,OAAO,CAAC;AACR,KAAK,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AACjD,MAAM,KAAK,GAAG,kBAAkB,CAAC,EAAE,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,UAAU,EAAE,CAAC,CAAC;AACnF,KAAK,MAAM;AACX,MAAM,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,EAAE,GAAG,UAAU,EAAE,CAAC;AACpD;AACA;AACA;AACA,MAAM,IAAI,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AACvC,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAC9E,OAAO;AACP,KAAK;AACL;AACA,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACtD,IAAI,OAAO,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;AAClC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AACpD,IAAI,OAAO,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;AAC9C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,QAAQ,EAAE;AAClB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;AAC7D,IAAI,OAAO,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;AAC9C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,cAAc,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE;AACjD,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC;AACA,IAAI,MAAM,CAAC,GAAG,EAAE;AAChB,MAAM,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACpD,IAAI,QAAQ,cAAc;AAC1B,MAAM,KAAK,OAAO;AAClB,QAAQ,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;AACpB;AACA,MAAM,KAAK,UAAU,CAAC;AACtB,MAAM,KAAK,QAAQ;AACnB,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AAClB;AACA,MAAM,KAAK,OAAO,CAAC;AACnB,MAAM,KAAK,MAAM;AACjB,QAAQ,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;AACnB;AACA,MAAM,KAAK,OAAO;AAClB,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AACrB;AACA,MAAM,KAAK,SAAS;AACpB,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AACrB;AACA,MAAM,KAAK,SAAS;AACpB,QAAQ,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC;AAC1B,QAAQ,MAAM;AAGd;AACA,KAAK;AACL;AACA,IAAI,IAAI,cAAc,KAAK,OAAO,EAAE;AACpC,MAAM,IAAI,cAAc,EAAE;AAC1B,QAAQ,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;AACtD,QAAQ,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;AACjC,QAAQ,IAAI,OAAO,GAAG,WAAW,EAAE;AACnC,UAAU,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AAC7C,SAAS;AACT,QAAQ,CAAC,CAAC,OAAO,GAAG,WAAW,CAAC;AAChC,OAAO,MAAM;AACb,QAAQ,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC;AACtB,OAAO;AACP,KAAK;AACL;AACA,IAAI,IAAI,cAAc,KAAK,UAAU,EAAE;AACvC,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAC1C,MAAM,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChC,KAAK;AACL;AACA,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,OAAO;AACvB,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;AAChC,WAAW,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;AAC9B,WAAW,KAAK,CAAC,CAAC,CAAC;AACnB,QAAQ,IAAI,CAAC;AACb,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC,OAAO;AACvB,QAAQ,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB,CAAC,IAAI,EAAE,GAAG,CAAC;AAC1F,QAAQ,OAAO,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,UAAU,GAAG3B,UAAkB,EAAE,IAAI,GAAG,EAAE,EAAE;AAC7D,IAAI,OAAO,IAAI,CAAC,OAAO;AACvB,QAAQ,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC;AAC/E,QAAQ,OAAO,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,IAAI,GAAG,EAAE,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC,OAAO;AACvB,QAAQ,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC;AAC9E,QAAQ,EAAE,CAAC;AACX,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC;AACR,IAAI,MAAM,GAAG,UAAU;AACvB,IAAI,eAAe,GAAG,KAAK;AAC3B,IAAI,oBAAoB,GAAG,KAAK;AAChC,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,YAAY,GAAG,KAAK;AACxB,GAAG,GAAG,EAAE,EAAE;AACV,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL;AACA,IAAI,MAAM,GAAG,GAAG,MAAM,KAAK,UAAU,CAAC;AACtC;AACA,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACjC,IAAI,CAAC,IAAI,GAAG,CAAC;AACb,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,EAAE,GAAG,EAAE,eAAe,EAAE,oBAAoB,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;AAClG,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,UAAU,EAAE,GAAG,EAAE,EAAE;AAC1C,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL;AACA,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,MAAM,KAAK,UAAU,CAAC,CAAC;AAClD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,GAAG;AAClB,IAAI,OAAO,YAAY,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;AAC9C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC;AACZ,IAAI,oBAAoB,GAAG,KAAK;AAChC,IAAI,eAAe,GAAG,KAAK;AAC3B,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,aAAa,GAAG,KAAK;AACzB,IAAI,YAAY,GAAG,KAAK;AACxB,IAAI,MAAM,GAAG,UAAU;AACvB,GAAG,GAAG,EAAE,EAAE;AACV,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL;AACA,IAAI,IAAI,CAAC,GAAG,aAAa,GAAG,GAAG,GAAG,EAAE,CAAC;AACrC,IAAI;AACJ,MAAM,CAAC;AACP,MAAM,SAAS;AACf,QAAQ,IAAI;AACZ,QAAQ,MAAM,KAAK,UAAU;AAC7B,QAAQ,eAAe;AACvB,QAAQ,oBAAoB;AAC5B,QAAQ,aAAa;AACrB,QAAQ,YAAY;AACpB,OAAO;AACP,MAAM;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,GAAG;AACd,IAAI,OAAO,YAAY,CAAC,IAAI,EAAE,+BAA+B,EAAE,KAAK,CAAC,CAAC;AACtE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,iCAAiC,CAAC,CAAC;AACzE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,GAAG;AACd,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACjC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE,kBAAkB,GAAG,IAAI,EAAE,GAAG,EAAE,EAAE;AAC3F,IAAI,IAAI,GAAG,GAAG,cAAc,CAAC;AAC7B;AACA,IAAI,IAAI,WAAW,IAAI,aAAa,EAAE;AACtC,MAAM,IAAI,kBAAkB,EAAE;AAC9B,QAAQ,GAAG,IAAI,GAAG,CAAC;AACnB,OAAO;AACP,MAAM,IAAI,WAAW,EAAE;AACvB,QAAQ,GAAG,IAAI,GAAG,CAAC;AACnB,OAAO,MAAM,IAAI,aAAa,EAAE;AAChC,QAAQ,GAAG,IAAI,IAAI,CAAC;AACpB,OAAO;AACP,KAAK;AACL;AACA,IAAI,OAAO,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AACzC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,IAAI,GAAG,EAAE,EAAE;AACnB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL;AACA,IAAI,OAAO,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC;AACjD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAAG;AAC/C,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AACtB,MAAM,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACjG,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,4BAA4B,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;AACnE,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC3B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;AACxC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC;AAC/C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;AAC3D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;AACxB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC3B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,IAAI,GAAG,EAAE,EAAE;AACtB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;AACjC;AACA,IAAI,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC;AAC/B;AACA,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;AAC5B,MAAM,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;AAChD,MAAM,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC;AACtD,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;AACpC,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;AAClD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,GAAG,cAAc,EAAE,IAAI,GAAG,EAAE,EAAE;AACxD,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;AACjD,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;AACxE,KAAK;AACL;AACA,IAAI,MAAM,OAAO,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,GAAG,IAAI,EAAE,CAAC;AAC5F;AACA,IAAI,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC;AAC9D,MAAM,YAAY,GAAG,aAAa,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE;AAC7D,MAAM,OAAO,GAAG,YAAY,GAAG,IAAI,GAAG,aAAa;AACnD,MAAM,KAAK,GAAG,YAAY,GAAG,aAAa,GAAG,IAAI;AACjD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AACpD;AACA,IAAI,OAAO,YAAY,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC;AACnD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,IAAI,GAAG,cAAc,EAAE,IAAI,GAAG,EAAE,EAAE;AAC5C,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACjD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,aAAa,EAAE;AACvB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,GAAG,IAAI,CAAC;AAC7E,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE;AACrC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC;AACpC;AACA,IAAI,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,EAAE,CAAC;AAC5C,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;AACrF,IAAI;AACJ,MAAM,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,OAAO,IAAI,OAAO,IAAI,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;AAClG,MAAM;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI;AACJ,MAAM,IAAI,CAAC,OAAO;AAClB,MAAM,KAAK,CAAC,OAAO;AACnB,MAAM,IAAI,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE;AACxC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;AAClC,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;AAChC,MAAM;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,OAAO,GAAG,EAAE,EAAE;AAC3B,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;AAC7E,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC;AACzF,IAAI,IAAI,KAAK,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AAC3E,IAAI,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AAC5B,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACrC,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;AAC3B,MAAM,IAAI,GAAG,SAAS,CAAC;AACvB,KAAK;AACL,IAAI,OAAO,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;AAClD,MAAM,GAAG,OAAO;AAChB,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,KAAK;AACX,MAAM,IAAI;AACV,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,CAAC,OAAO,GAAG,EAAE,EAAE;AACnC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AACnC;AACA,IAAI,OAAO,YAAY,CAAC,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE;AAC5F,MAAM,GAAG,OAAO;AAChB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;AACxC,MAAM,SAAS,EAAE,IAAI;AACrB,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG,CAAC,GAAG,SAAS,EAAE;AAC3B,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;AAC/C,MAAM,MAAM,IAAI,oBAAoB,CAAC,yCAAyC,CAAC,CAAC;AAChF,KAAK;AACL,IAAI,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG,CAAC,GAAG,SAAS,EAAE;AAC3B,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;AAC/C,MAAM,MAAM,IAAI,oBAAoB,CAAC,yCAAyC,CAAC,CAAC;AAChF,KAAK;AACL,IAAI,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,iBAAiB,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,GAAG,EAAE,EAAE;AACpD,IAAI,MAAM,EAAE,MAAM,GAAG,IAAI,EAAE,eAAe,GAAG,IAAI,EAAE,GAAG,OAAO;AAC7D,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC;AACpC,QAAQ,MAAM;AACd,QAAQ,eAAe;AACvB,QAAQ,WAAW,EAAE,IAAI;AACzB,OAAO,CAAC,CAAC;AACT,IAAI,OAAO,iBAAiB,CAAC,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AACrD,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,OAAO,iBAAiB,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,GAAG,EAAE,EAAE;AACpD,IAAI,OAAO,QAAQ,CAAC,iBAAiB,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AAC1D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,iBAAiB,CAAC,GAAG,EAAE,OAAO,GAAG,EAAE,EAAE;AAC9C,IAAI,MAAM,EAAE,MAAM,GAAG,IAAI,EAAE,eAAe,GAAG,IAAI,EAAE,GAAG,OAAO;AAC7D,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC;AACpC,QAAQ,MAAM;AACd,QAAQ,eAAe;AACvB,QAAQ,WAAW,EAAE,IAAI;AACzB,OAAO,CAAC,CAAC;AACT,IAAI,OAAO,IAAI,WAAW,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,gBAAgB,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,GAAG,EAAE,EAAE;AACzD,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,YAAY,CAAC,EAAE;AACxD,MAAM,MAAM,IAAI,oBAAoB;AACpC,QAAQ,+DAA+D;AACvE,OAAO,CAAC;AACR,KAAK;AACL,IAAI,MAAM,EAAE,MAAM,GAAG,IAAI,EAAE,eAAe,GAAG,IAAI,EAAE,GAAG,IAAI;AAC1D,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC;AACpC,QAAQ,MAAM;AACd,QAAQ,eAAe;AACvB,QAAQ,WAAW,EAAE,IAAI;AACzB,OAAO,CAAC,CAAC;AACT;AACA,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;AAClD,MAAM,MAAM,IAAI,oBAAoB;AACpC,QAAQ,CAAC,yCAAyC,EAAE,WAAW,CAAC,EAAE,CAAC;AACnE,UAAU,CAAC,sCAAsC,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;AACxE,OAAO,CAAC;AACR,KAAK;AACL;AACA,IAAI,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACjG;AACA,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAC7C,KAAK,MAAM;AACX,MAAM,OAAO,mBAAmB;AAChC,QAAQ,MAAM;AACd,QAAQ,IAAI;AACZ,QAAQ,IAAI;AACZ,QAAQ,CAAC,OAAO,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;AACvC,QAAQ,IAAI;AACZ,QAAQ,cAAc;AACtB,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,UAAU,GAAG;AAC1B,IAAI,OAAOA,UAAkB,CAAC;AAC9B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,QAAQ,GAAG;AACxB,IAAI,OAAOC,QAAgB,CAAC;AAC5B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,qBAAqB,GAAG;AACrC,IAAI,OAAO6B,qBAA6B,CAAC;AACzC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,SAAS,GAAG;AACzB,IAAI,OAAO5B,SAAiB,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,SAAS,GAAG;AACzB,IAAI,OAAOC,SAAiB,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,WAAW,GAAG;AAC3B,IAAI,OAAOC,WAAmB,CAAC;AAC/B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,iBAAiB,GAAG;AACjC,IAAI,OAAOC,iBAAyB,CAAC;AACrC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,sBAAsB,GAAG;AACtC,IAAI,OAAOC,sBAA8B,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,qBAAqB,GAAG;AACrC,IAAI,OAAOC,qBAA6B,CAAC;AACzC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,cAAc,GAAG;AAC9B,IAAI,OAAOC,cAAsB,CAAC;AAClC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,oBAAoB,GAAG;AACpC,IAAI,OAAOC,oBAA4B,CAAC;AACxC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,yBAAyB,GAAG;AACzC,IAAI,OAAOC,yBAAiC,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,wBAAwB,GAAG;AACxC,IAAI,OAAOC,wBAAgC,CAAC;AAC5C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,cAAc,GAAG;AAC9B,IAAI,OAAOC,cAAsB,CAAC;AAClC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,2BAA2B,GAAG;AAC3C,IAAI,OAAOI,2BAAmC,CAAC;AAC/C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,YAAY,GAAG;AAC5B,IAAI,OAAOH,YAAoB,CAAC;AAChC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,yBAAyB,GAAG;AACzC,IAAI,OAAOI,yBAAiC,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,yBAAyB,GAAG;AACzC,IAAI,OAAOc,yBAAiC,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,aAAa,GAAG;AAC7B,IAAI,OAAOjB,aAAqB,CAAC;AACjC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,0BAA0B,GAAG;AAC1C,IAAI,OAAOI,0BAAkC,CAAC;AAC9C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,aAAa,GAAG;AAC7B,IAAI,OAAOH,aAAqB,CAAC;AACjC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,0BAA0B,GAAG;AAC1C,IAAI,OAAOI,0BAAkC,CAAC;AAC9C,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,WAAW,EAAE;AAC9C,EAAE,IAAI,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;AACxC,IAAI,OAAO,WAAW,CAAC;AACvB,GAAG,MAAM,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,IAAI,QAAQ,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,EAAE;AACpF,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;AAC5C,GAAG,MAAM,IAAI,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;AAC7D,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;AAC5C,GAAG,MAAM;AACT,IAAI,MAAM,IAAI,oBAAoB;AAClC,MAAM,CAAC,2BAA2B,EAAE,WAAW,CAAC,UAAU,EAAE,OAAO,WAAW,CAAC,CAAC;AAChF,KAAK,CAAC;AACN,GAAG;AACH;;ACh/EK,MAAC,OAAO,GAAG;;;;"}