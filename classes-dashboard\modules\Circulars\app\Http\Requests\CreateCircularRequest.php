<?php

namespace Circulars\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateCircularRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => 'required|string|max:200',
            'file' => 'required|mimes:pdf|max:4096',
        ];
    }
}
