'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Loader2, ArrowLeft } from 'lucide-react';
import ProfileTab from './components/ProfileTab';
import ImagesTab from './components/ImagesTab';
import EducationTab from './components/EducationTab';
import WorkTab from './components/WorkTab';
import CertificateTab from './components/CertificateTab';
import TuitionTab from './components/TuitionTab';
import {
  updateClassByAdmin,
  updateClassImagesByAdmin,
  fetchClassDataByAdmin,
  fetchConstants,
  addTuitionClassByAdmin,
  deleteTuitionClassByAdmin
} from '@/services/classesApi';
import Link from 'next/link';
import { ClassData } from '@/lib/types';
import { BsPersonCircle } from 'react-icons/bs';
import { FaGoogleScholar } from 'react-icons/fa6';
import { IoShieldCheckmark } from 'react-icons/io5';
import { FaImages } from 'react-icons/fa';
import {
  profileFormSchema,
  educationFormSchema,
  experienceFormSchema,
  certificateFormSchema,
  type ProfileFormValues,
  type EducationFormValues,
  type ExperienceFormValues,
  type CertificateFormValues,
} from '@/lib/validations/classesEditSchema';

const TABS = [
  { key: 'profile', label: 'Profile', icon: <BsPersonCircle /> },
  { key: 'images', label: 'Images', icon: <FaImages /> },
  { key: 'education', label: 'Education', icon: <FaGoogleScholar /> },
  { key: 'work', label: 'Work Experience', icon: <IoShieldCheckmark /> },
  { key: 'certifications', label: 'Certifications', icon: <IoShieldCheckmark /> },
  { key: 'tuition', label: 'Tuition Classes', icon: <FaGoogleScholar /> },
];

const EditClassPage = () => {
  const params = useParams();
  const classId = params.id as string;

  const [classData, setClassData] = useState<ClassData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [profilePhotoFile, setProfilePhotoFile] = useState<File | null>(null);
  const [classesLogoFile, setClassesLogoFile] = useState<File | null>(null);
  const [profilePhotoPreview, setProfilePhotoPreview] = useState<string>('');
  const [activeTab, setActiveTab] = useState('profile');
  const [classesLogoPreview, setClassesLogoPreview] = useState<string>('');
  const [constants, setConstants] = useState<any[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [originalImages, setOriginalImages] = useState({
    profilePhoto: '',
    classesLogo: ''
  });

  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      username: '',
      firstName: '',
      lastName: '',
      className: '',
      email: '',
      contactNo: '',
      isVerified: false,
      birthDate: '',
      catchyHeadline: '',
      tutorBio: '',
    },
    mode: 'onBlur',
  });

  const educationForm = useForm<EducationFormValues>({
    resolver: zodResolver(educationFormSchema),
    defaultValues: {
      education: [],
    },
    mode: 'onChange',
  });

  const experienceForm = useForm<ExperienceFormValues>({
    resolver: zodResolver(experienceFormSchema),
    defaultValues: {
      experience: [],
    },
    mode: 'onChange',
  });

  const certificateForm = useForm<CertificateFormValues>({
    resolver: zodResolver(certificateFormSchema),
    defaultValues: {
      certificates: [],
    },
    mode: 'onChange',
  });


  const [newTuitionClass, setNewTuitionClass] = useState({
    education: '',
    coachingType: [] as string[],
    boardType: [] as string[],
    medium: [] as string[],
    section: [] as string[],
    subject: [] as string[],
    details: [] as string[],
  });
  const [formData, setFormData] = useState<ClassData>({
    id: '',
    username: '',
    firstName: '',
    lastName: '',
    email: '',
    contactNo: '',
    className: '',
    isVerified: false,
    createdAt: '',
    ClassAbout: {
      id: '',
      birthDate: '',
      catchyHeadline: '',
      tutorBio: '',
      profilePhoto: '',
      classesLogo: '',
    },
    status: {
      id: '',
      status: '',
    },
    education: [],
    experience: [],
    certificates: [],
    tuitionClasses: [],
  });

  const fetchClassData = async () => {
    try {
      setIsLoading(true);
      const data = await fetchClassDataByAdmin(classId);
      setClassData(data);

      profileForm.reset({
        username: data.username || '',
        firstName: data.firstName || '',
        lastName: data.lastName || '',
        email: data.email || '',
        contactNo: data.contactNo || '',
        className: data.className || '',
        isVerified: data.isVerified || false,
        birthDate: data.ClassAbout?.birthDate ? new Date(data.ClassAbout.birthDate).toISOString().split('T')[0] : '',
        catchyHeadline: data.ClassAbout?.catchyHeadline || '',
        tutorBio: data.ClassAbout?.tutorBio || '',
      });

      educationForm.reset({
        education: data.education || [],
      });

      experienceForm.reset({
        experience: data.experience || [],
      });

      certificateForm.reset({
        certificates: data.certificates || [],
      });

      setFormData({
        id: data.id,
        username: data.username || '',
        firstName: data.firstName || '',
        lastName: data.lastName || '',
        email: data.email || '',
        contactNo: data.contactNo || '',
        className: data.className || '',
        isVerified: data.isVerified || false,
        createdAt: data.createdAt || '',
        ClassAbout: {
          id: data.ClassAbout?.id || '',
          birthDate: data.ClassAbout?.birthDate || '',
          catchyHeadline: data.ClassAbout?.catchyHeadline || '',
          tutorBio: data.ClassAbout?.tutorBio || '',
          profilePhoto: data.ClassAbout?.profilePhoto || '',
          classesLogo: data.ClassAbout?.classesLogo || '',
        },
        education: data.education || [],
        experience: data.experience || [],
        certificates: data.certificates || [],
        tuitionClasses: data.tuitionClasses || [],
        status: {
          id: data.status?.id || '',
          status: data.status?.status || '',
        }
      });

      // Set original images for comparison
      setOriginalImages({
        profilePhoto: data.ClassAbout?.profilePhoto || '',
        classesLogo: data.ClassAbout?.classesLogo || ''
      });

      if (data.ClassAbout?.profilePhoto) {
        setProfilePhotoPreview(`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${data.ClassAbout.profilePhoto}`);
      }
      if (data.ClassAbout?.classesLogo) {
        setClassesLogoPreview(`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${data.ClassAbout.classesLogo}`);
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to load class data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (classId) {
      fetchClassData();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [classId]); 

  const handleClassAboutChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      ClassAbout: {
        ...prev.ClassAbout,
        [field]: value
      }
    }));
  };





  const handleImageChange = (type: 'profilePhoto' | 'classesLogo', file: File | null) => {
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        if (type === 'profilePhoto') {
          setProfilePhotoFile(file);
          setProfilePhotoPreview(result);
        } else {
          setClassesLogoFile(file);
          setClassesLogoPreview(result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (data: ProfileFormValues) => {
    setIsSaving(true);

    try {
      const updatedFormData = {
        ...formData,
        username: data.username,
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        contactNo: data.contactNo,
        className: data.className,
        isVerified: data.isVerified,
        ClassAbout: {
          ...formData.ClassAbout,
          birthDate: data.birthDate,
          catchyHeadline: data.catchyHeadline,
          tutorBio: data.tutorBio,
        }
      };

      await updateClassByAdmin(classId, updatedFormData);
      toast.success('Profile updated successfully!');
    } catch (err: any) {
      toast.error(err.message || 'Failed to update profile.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleImagesSubmit = async () => {
    setIsSaving(true);

    try {
      const hasNewFiles = profilePhotoFile || classesLogoFile;
      const profilePhotoRemoved = originalImages.profilePhoto && formData.ClassAbout.profilePhoto === '';
      const classesLogoRemoved = originalImages.classesLogo && formData.ClassAbout.classesLogo === '';
      const hasImageRemovals = profilePhotoRemoved || classesLogoRemoved;

      if (hasNewFiles) {
        const imageFormData = new FormData();
        if (profilePhotoFile) {
          imageFormData.append('profilePhoto', profilePhotoFile);
        }
        if (classesLogoFile) {
          imageFormData.append('classesLogo', classesLogoFile);
        }

        await updateClassImagesByAdmin(classId, imageFormData);
      }

      if (hasImageRemovals) {
        await updateClassByAdmin(classId, formData);
      }

      if (hasNewFiles || hasImageRemovals) {
        toast.success('Images updated successfully!');
        // Refresh the data to get the latest state
        await fetchClassData();
      } else {
        // No changes made
        toast.success('Images section saved successfully!');
      }
    } catch (err: any) {
      toast.error(err.message || 'Failed to update images.');
    } finally {
      setIsSaving(false);
    }
  };

  const parseFieldValue = (value: string | null | undefined): string => {
    if (!value) return 'Not specified';

    try {
      const parsed = JSON.parse(value);
      return Array.isArray(parsed) ? parsed.join(', ') : value;
    } catch {
      return value;
    }
  };

  const getConstantValues = (name: string) => {
    return constants.find((cat: any) => cat.name === name)?.details || [];
  };

  const handleNewTuitionChange = (field: string, value: string | string[]) => {
    setNewTuitionClass(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddTuitionClass = async () => {
    try {
      if (!newTuitionClass.education) {
        toast.error('Please select education category');
        return;
      }

      const formattedData = {
        classId: classId,
        tuitionDetails: [{
          education: newTuitionClass.education,
          coachingType: newTuitionClass.coachingType.length > 0 ? JSON.stringify(newTuitionClass.coachingType) : null,
          boardType: newTuitionClass.boardType.length > 0 ? JSON.stringify(newTuitionClass.boardType) : null,
          medium: newTuitionClass.medium.length > 0 ? JSON.stringify(newTuitionClass.medium) : null,
          section: newTuitionClass.section.length > 0 ? JSON.stringify(newTuitionClass.section) : null,
          subject: newTuitionClass.subject.length > 0 ? JSON.stringify(newTuitionClass.subject) : null,
          details: newTuitionClass.details.length > 0 ? JSON.stringify(newTuitionClass.details) : null,
        }]
      };

      await addTuitionClassByAdmin(formattedData);

      toast.success('Tuition class added successfully');
      setShowAddForm(false);
      setNewTuitionClass({
        education: '',
        coachingType: [],
        boardType: [],
        medium: [],
        section: [],
        subject: [],
        details: [],
      });
      // Refresh the class data
      await fetchClassData();
    } catch (error: any) {
      toast.error(error.message || 'Failed to add tuition class');
    }
  };

  const handleDeleteTuitionClass = async (tuitionId: string) => {
    try {
      await deleteTuitionClassByAdmin(tuitionId, classId);
      toast.success('Tuition class deleted successfully');
      await fetchClassData();
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete tuition class');
    }
  };

  const fetchConstantsData = async () => {
    try {
      const data = await fetchConstants();
      setConstants(data);
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch constants');
    }
  };

  useEffect(() => {
    fetchConstantsData();
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-orange-500" />
      </div>
    );
  }

  if (!classData) {
    return (
      <div className="text-center py-10 text-gray-600 dark:text-gray-400">
        Class not found
      </div>
    );
  }

  return (
    <div className="max-w-7xl p-6 space-y-6 bg-white">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/dashboard">
          <Button variant="outline" size="sm" className="border-gray-300 text-black hover:bg-black hover:text-white">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
        <h1 className="text-3xl font-bold text-black">
          Edit Class Profile
        </h1>
      </div>
      <div className="flex flex-wrap gap-4 border-b border-gray-200 dark:border-gray-700 pb-2">
        {TABS.map(({ key, label, icon }) => (
          <button
            key={key}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${activeTab === key
              ? "bg-orange-100 text-customOrange dark:bg-orange-900 dark:text-orange-200 font-semibold"
              : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
              }`}
            onClick={() => setActiveTab(key)}
          >
            {icon}
            {label}
          </button>
        ))}
      </div>
      <div className='space-y-4'>
        {activeTab === 'profile' && (
          <ProfileTab
            profileForm={profileForm}
            handleSubmit={handleSubmit}
            isSaving={isSaving}
            formData={formData}
          />
        )}

        {activeTab === 'images' && (
          <ImagesTab
            isSaving={isSaving}
            formData={formData}
            profilePhotoPreview={profilePhotoPreview}
            classesLogoPreview={classesLogoPreview}
            handleImageChange={handleImageChange}
            handleClassAboutChange={handleClassAboutChange}
            setProfilePhotoPreview={setProfilePhotoPreview}
            setProfilePhotoFile={setProfilePhotoFile}
            setClassesLogoPreview={setClassesLogoPreview}
            setClassesLogoFile={setClassesLogoFile}
            onSave={handleImagesSubmit}
          />
        )}

        {activeTab === 'education' && (
          <EducationTab
            educationForm={educationForm}
            formData={formData}
            isSaving={isSaving}
            onSubmit={async () => {
              setIsSaving(true);
              try {
                await updateClassByAdmin(classId, formData);
                toast.success('Education updated successfully!');
              } catch (err: any) {
                toast.error(err.message || 'Failed to update education.');
              } finally {
                setIsSaving(false);
              }
            }}
            onEducationUpdate={fetchClassData}
          />
        )}

        {activeTab === 'work' && (
          <WorkTab
            experienceForm={experienceForm}
            formData={formData}
            isSaving={isSaving}
            onSubmit={async () => {
              setIsSaving(true);
              try {
                await updateClassByAdmin(classId, formData);
                toast.success('Experience updated successfully!');
              } catch (err: any) {
                toast.error(err.message || 'Failed to update experience.');
              } finally {
                setIsSaving(false);
              }
            }}
            onExperienceUpdate={fetchClassData}
          />
        )}

        {activeTab === 'certifications' && (
          <CertificateTab
            certificateForm={certificateForm}
            formData={formData}
            isSaving={isSaving}
            onSubmit={async () => {
              setIsSaving(true);
              try {
                await updateClassByAdmin(classId, formData);
                toast.success('Certificates updated successfully!');
              } catch (err: any) {
                toast.error(err.message || 'Failed to update certificates.');
              } finally {
                setIsSaving(false);
              }
            }}
            onCertificateUpdate={fetchClassData}
          />
        )}

        {activeTab === 'tuition' && (
          <TuitionTab
            showAddForm={showAddForm}
            setShowAddForm={setShowAddForm}
            newTuitionClass={newTuitionClass}
            handleNewTuitionChange={handleNewTuitionChange}
            handleAddTuitionClass={handleAddTuitionClass}
            constants={constants}
            getConstantValues={getConstantValues}
            formData={formData}
            handleDeleteTuitionClass={handleDeleteTuitionClass}
            parseFieldValue={parseFieldValue}
            isSaving={isSaving}
            onSave={async () => {
              setIsSaving(true);
              try {
                await updateClassByAdmin(classId, formData);
                toast.success('Tuition classes updated successfully!');
              } catch (err: any) {
                toast.error(err.message || 'Failed to update tuition classes.');
              } finally {
                setIsSaving(false);
              }
            }}
          />
        )}
      </div>
    </div>
  );
};

export default EditClassPage;